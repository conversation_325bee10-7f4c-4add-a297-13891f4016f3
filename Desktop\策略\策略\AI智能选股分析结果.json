{"analysis_time": "2025-07-30 21:37:50", "method": "AI智能选股分析", "data_sources": {"hot_stocks_count": 200, "news_stocks_count": 34, "combined_count": 110}, "complete_analysis": [{"code": "001221", "name": "悍高", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 80, "news_score": 1.0, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 83.25, "risk_level": "低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分优秀；消息面关注", "investment_advice": "⭐ 建议买入"}, {"code": "600010", "name": "包钢股份", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 80, "news_score": 0.67, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 4, "ai_score": 80.36, "risk_level": "低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分优秀；消息面关注", "investment_advice": "⭐ 建议买入"}, {"code": "002570", "name": "贝因美", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 80, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 77.39, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面谨慎", "investment_advice": "⭐ 建议买入"}, {"code": "600117", "name": "西宁特钢", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 80, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 77.39, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面谨慎", "investment_advice": "⭐ 建议买入"}, {"code": "600326", "name": "西藏天路", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 70, "news_score": 0.67, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 2, "ai_score": 76.36, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面关注", "investment_advice": "⭐ 建议买入"}, {"code": "002097", "name": "山河智能", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 70, "news_score": 0.67, "news_level": "⚠️ 观望", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 76.36, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面观望", "investment_advice": "⭐ 建议买入"}, {"code": "600111", "name": "沪市600111", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 1.33, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 2, "ai_score": 74.14, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面关注", "investment_advice": "👀 可以考虑"}, {"code": "600895", "name": "沪市600895", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 1.0, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 71.25, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面关注", "investment_advice": "👀 可以考虑"}, {"code": "002265", "name": "深市002265", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 1.0, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 71.25, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面关注", "investment_advice": "👀 可以考虑"}, {"code": "601138", "name": "沪市601138", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.67, "news_level": "⚠️ 观望", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 68.36, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面观望", "investment_advice": "👀 可以考虑"}, {"code": "600570", "name": "恒生电子", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.67, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 2, "ai_score": 68.36, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面关注", "investment_advice": "👀 可以考虑"}, {"code": "002104", "name": "深市002104", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.67, "news_level": "⚠️ 观望", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 68.36, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面观望", "investment_advice": "👀 可以考虑"}, {"code": "601669", "name": "中国电建", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 45, "news_score": 0.67, "news_level": "⚠️ 观望", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 66.36, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面观望", "investment_advice": "👀 可以考虑"}, {"code": "000796", "name": "深市000796", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 65.39, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面谨慎", "investment_advice": "👀 可以考虑"}, {"code": "002370", "name": "深市002370", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 65.39, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面谨慎", "investment_advice": "👀 可以考虑"}, {"code": "601696", "name": "沪市601696", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 65.39, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面谨慎", "investment_advice": "👀 可以考虑"}, {"code": "000798", "name": "深市000798", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 65.39, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面谨慎", "investment_advice": "👀 可以考虑"}, {"code": "002827", "name": "深市002827", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 65.39, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面谨慎", "investment_advice": "👀 可以考虑"}, {"code": "600749", "name": "西藏旅游", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 80, "news_score": 0.0, "news_level": "❌ 回避", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 64.5, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面回避", "investment_advice": "⚠️ 谨慎观望"}, {"code": "300528", "name": "幸福蓝海", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 80, "news_score": 0.0, "news_level": "❌ 回避", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 64.5, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面回避", "investment_advice": "⚠️ 谨慎观望"}, {"code": "000003", "name": "万科A", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "000004", "name": "深市000004", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "001001", "name": "深市001001", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002001", "name": "深市002001", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "000558", "name": "深市000558", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.0, "news_level": "❌ 回避", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "热门股和消息面双重验证；消息面回避", "investment_advice": "❌ 不建议买入"}, {"code": "001002", "name": "深市001002", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "603259", "name": "药明康德", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.0, "news_level": "❌ 回避", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "热门股和消息面双重验证；消息面回避", "investment_advice": "❌ 不建议买入"}, {"code": "002003", "name": "深市002003", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "000785", "name": "深市000785", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002837", "name": "深市002837", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "600581", "name": "沪市600581", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "603616", "name": "沪市603616", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "001318", "name": "深市001318", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002490", "name": "深市002490", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "600684", "name": "沪市600684", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "600392", "name": "沪市600392", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.0, "news_level": "❌ 回避", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "热门股和消息面双重验证；消息面回避", "investment_advice": "❌ 不建议买入"}, {"code": "002343", "name": "深市002343", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "300732", "name": "创业板300732", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "300059", "name": "创业板300059", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "005001", "name": "深市005001", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002017", "name": "深市002017", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "603367", "name": "沪市603367", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002644", "name": "深市002644", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "603280", "name": "沪市603280", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002287", "name": "深市002287", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002905", "name": "深市002905", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "601606", "name": "沪市601606", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "600651", "name": "沪市600651", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "603839", "name": "沪市603839", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002564", "name": "深市002564", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "000953", "name": "深市000953", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "000657", "name": "深市000657", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "301038", "name": "创业板301038", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002151", "name": "深市002151", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002675", "name": "深市002675", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "601869", "name": "沪市601869", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "000813", "name": "深市000813", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "603767", "name": "沪市603767", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "300706", "name": "创业板300706", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "300750", "name": "创业板300750", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002453", "name": "深市002453", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "600011", "name": "沪市600011", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "301078", "name": "创业板301078", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "004005", "name": "深市004005", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "300251", "name": "创业板300251", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "601162", "name": "沪市601162", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "004003", "name": "深市004003", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "600408", "name": "沪市600408", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "300965", "name": "创业板300965", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "600579", "name": "沪市600579", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "301235", "name": "创业板301235", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "600302", "name": "沪市600302", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "001216", "name": "深市001216", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "605507", "name": "沪市605507", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "605268", "name": "沪市605268", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "600276", "name": "沪市600276", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.0, "news_level": "❌ 回避", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "热门股和消息面双重验证；消息面回避", "investment_advice": "❌ 不建议买入"}, {"code": "300600", "name": "创业板300600", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "605099", "name": "沪市605099", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "603530", "name": "沪市603530", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002594", "name": "深市002594", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002931", "name": "深市002931", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "600062", "name": "沪市600062", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "600196", "name": "沪市600196", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "300877", "name": "创业板300877", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "001234", "name": "深市001234", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002896", "name": "深市002896", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "001331", "name": "深市001331", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "000617", "name": "深市000617", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002611", "name": "深市002611", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "300149", "name": "量子生物", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "601727", "name": "沪市601727", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "005002", "name": "深市005002", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "300476", "name": "创业板300476", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "605277", "name": "沪市605277", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "001267", "name": "深市001267", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "000802", "name": "深市000802", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "600977", "name": "沪市600977", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "601929", "name": "沪市601929", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "605138", "name": "沪市605138", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "603716", "name": "沪市603716", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0, "news_level": "", "has_hot_data": true, "has_news_data": false, "change": "", "price": "", "ai_score": 52.5, "risk_level": "中高风险", "ai_reason": "同时出现在人气榜+飙升榜", "investment_advice": "❌ 不建议买入"}, {"code": "002739", "name": "股票002739", "hot_sources": [], "hot_score": 0, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": false, "has_news_data": true, "mention_count": 1, "ai_score": 30.39, "risk_level": "高风险", "ai_reason": "消息面情感分析发现；消息面谨慎", "investment_advice": "❌ 不建议买入"}, {"code": "600990", "name": "股票600990", "hot_sources": [], "hot_score": 0, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": false, "has_news_data": true, "mention_count": 1, "ai_score": 30.39, "risk_level": "高风险", "ai_reason": "消息面情感分析发现；消息面谨慎", "investment_advice": "❌ 不建议买入"}, {"code": "600435", "name": "股票600435", "hot_sources": [], "hot_score": 0, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": false, "has_news_data": true, "mention_count": 1, "ai_score": 30.39, "risk_level": "高风险", "ai_reason": "消息面情感分析发现；消息面谨慎", "investment_advice": "❌ 不建议买入"}, {"code": "603103", "name": "股票603103", "hot_sources": [], "hot_score": 0, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": false, "has_news_data": true, "mention_count": 1, "ai_score": 30.39, "risk_level": "高风险", "ai_reason": "消息面情感分析发现；消息面谨慎", "investment_advice": "❌ 不建议买入"}, {"code": "600257", "name": "股票600257", "hot_sources": [], "hot_score": 0, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": false, "has_news_data": true, "mention_count": 1, "ai_score": 30.39, "risk_level": "高风险", "ai_reason": "消息面情感分析发现；消息面谨慎", "investment_advice": "❌ 不建议买入"}, {"code": "600372", "name": "股票600372", "hot_sources": [], "hot_score": 0, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": false, "has_news_data": true, "mention_count": 1, "ai_score": 30.39, "risk_level": "高风险", "ai_reason": "消息面情感分析发现；消息面谨慎", "investment_advice": "❌ 不建议买入"}, {"code": "300564", "name": "股票300564", "hot_sources": [], "hot_score": 0, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": false, "has_news_data": true, "mention_count": 1, "ai_score": 30.39, "risk_level": "高风险", "ai_reason": "消息面情感分析发现；消息面谨慎", "investment_advice": "❌ 不建议买入"}, {"code": "600882", "name": "股票600882", "hot_sources": [], "hot_score": 0, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": false, "has_news_data": true, "mention_count": 1, "ai_score": 30.39, "risk_level": "高风险", "ai_reason": "消息面情感分析发现；消息面谨慎", "investment_advice": "❌ 不建议买入"}, {"code": "600630", "name": "股票600630", "hot_sources": [], "hot_score": 0, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": false, "has_news_data": true, "mention_count": 1, "ai_score": 30.39, "risk_level": "高风险", "ai_reason": "消息面情感分析发现；消息面谨慎", "investment_advice": "❌ 不建议买入"}, {"code": "600763", "name": "股票600763", "hot_sources": [], "hot_score": 0, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": false, "has_news_data": true, "mention_count": 1, "ai_score": 30.39, "risk_level": "高风险", "ai_reason": "消息面情感分析发现；消息面谨慎", "investment_advice": "❌ 不建议买入"}], "final_recommendations": {"strong_buy": [], "buy": [{"code": "001221", "name": "悍高", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 80, "news_score": 1.0, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 83.25, "risk_level": "低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分优秀；消息面关注", "investment_advice": "⭐ 建议买入"}, {"code": "600010", "name": "包钢股份", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 80, "news_score": 0.67, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 4, "ai_score": 80.36, "risk_level": "低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分优秀；消息面关注", "investment_advice": "⭐ 建议买入"}, {"code": "002570", "name": "贝因美", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 80, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 77.39, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面谨慎", "investment_advice": "⭐ 建议买入"}, {"code": "600117", "name": "西宁特钢", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 80, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 77.39, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面谨慎", "investment_advice": "⭐ 建议买入"}, {"code": "600326", "name": "西藏天路", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 70, "news_score": 0.67, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 2, "ai_score": 76.36, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面关注", "investment_advice": "⭐ 建议买入"}, {"code": "002097", "name": "山河智能", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 70, "news_score": 0.67, "news_level": "⚠️ 观望", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 76.36, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面观望", "investment_advice": "⭐ 建议买入"}], "consider": [{"code": "600111", "name": "沪市600111", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 1.33, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 2, "ai_score": 74.14, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面关注", "investment_advice": "👀 可以考虑"}, {"code": "600895", "name": "沪市600895", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 1.0, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 71.25, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面关注", "investment_advice": "👀 可以考虑"}, {"code": "002265", "name": "深市002265", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 1.0, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 71.25, "risk_level": "中低风险", "ai_reason": "热门股和消息面双重验证；AI综合得分良好；消息面关注", "investment_advice": "👀 可以考虑"}, {"code": "601138", "name": "沪市601138", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.67, "news_level": "⚠️ 观望", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 68.36, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面观望", "investment_advice": "👀 可以考虑"}, {"code": "600570", "name": "恒生电子", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.67, "news_level": "👀 关注", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 2, "ai_score": 68.36, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面关注", "investment_advice": "👀 可以考虑"}, {"code": "002104", "name": "深市002104", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.67, "news_level": "⚠️ 观望", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 68.36, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面观望", "investment_advice": "👀 可以考虑"}, {"code": "601669", "name": "中国电建", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 45, "news_score": 0.67, "news_level": "⚠️ 观望", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 66.36, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面观望", "investment_advice": "👀 可以考虑"}, {"code": "000796", "name": "深市000796", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 65.39, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面谨慎", "investment_advice": "👀 可以考虑"}, {"code": "002370", "name": "深市002370", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 65.39, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面谨慎", "investment_advice": "👀 可以考虑"}, {"code": "601696", "name": "沪市601696", "hot_sources": ["人气榜", "飙升榜"], "hot_score": 50, "news_score": 0.33, "news_level": "🤔 谨慎", "has_hot_data": true, "has_news_data": true, "change": "", "price": "", "mention_count": 1, "ai_score": 65.39, "risk_level": "中等风险", "ai_reason": "热门股和消息面双重验证；AI综合得分中等；消息面谨慎", "investment_advice": "👀 可以考虑"}], "analysis_time": "2025-07-30 21:37:50", "total_analyzed": 110, "high_score_count": 18}}
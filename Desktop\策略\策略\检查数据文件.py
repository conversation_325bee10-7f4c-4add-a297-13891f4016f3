#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据文件
验证智能数据分析报告需要的数据文件是否正确
"""

import csv
import json
import os

def check_data_files():
    """检查所有数据文件"""
    print("🔍 检查股票数据文件")
    print("=" * 50)
    
    files_status = {}
    
    # 检查人气榜数据
    print("\n📊 人气榜数据 (popularity.csv):")
    if os.path.exists('popularity.csv'):
        try:
            with open('popularity.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                data = list(reader)
            
            print(f"  ✅ 文件存在: {len(data)} 条记录")
            if data:
                print(f"  📋 字段: {list(data[0].keys())}")
                print(f"  📝 示例: {data[0]}")
                files_status['popularity'] = len(data)
            else:
                print(f"  ⚠️ 文件为空")
                files_status['popularity'] = 0
        except Exception as e:
            print(f"  ❌ 读取失败: {str(e)}")
            files_status['popularity'] = -1
    else:
        print(f"  ❌ 文件不存在")
        files_status['popularity'] = -1
    
    # 检查飙升榜数据
    print("\n🚀 飙升榜数据 (soaring.csv):")
    if os.path.exists('soaring.csv'):
        try:
            with open('soaring.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                data = list(reader)
            
            print(f"  ✅ 文件存在: {len(data)} 条记录")
            if data:
                print(f"  📋 字段: {list(data[0].keys())}")
                print(f"  📝 示例: {data[0]}")
                files_status['soaring'] = len(data)
            else:
                print(f"  ⚠️ 文件为空")
                files_status['soaring'] = 0
        except Exception as e:
            print(f"  ❌ 读取失败: {str(e)}")
            files_status['soaring'] = -1
    else:
        print(f"  ❌ 文件不存在")
        files_status['soaring'] = -1
    
    # 检查股票代码
    print("\n📋 股票代码 (codes.txt):")
    if os.path.exists('codes.txt'):
        try:
            with open('codes.txt', 'r', encoding='utf-8') as f:
                codes = [line.strip() for line in f if line.strip()]
            
            print(f"  ✅ 文件存在: {len(codes)} 条记录")
            if codes:
                print(f"  📝 示例: {codes[:5]}")
                files_status['codes'] = len(codes)
            else:
                print(f"  ⚠️ 文件为空")
                files_status['codes'] = 0
        except Exception as e:
            print(f"  ❌ 读取失败: {str(e)}")
            files_status['codes'] = -1
    else:
        print(f"  ❌ 文件不存在")
        files_status['codes'] = -1
    
    # 检查名称缓存
    print("\n🏷️ 股票名称缓存 (stock_names_cache.json):")
    if os.path.exists('stock_names_cache.json'):
        try:
            with open('stock_names_cache.json', 'r', encoding='utf-8') as f:
                cache = json.load(f)
                names = cache.get('names', {})
            
            print(f"  ✅ 文件存在: {len(names)} 条记录")
            if names:
                sample_items = list(names.items())[:3]
                print(f"  📝 示例: {sample_items}")
                files_status['names'] = len(names)
            else:
                print(f"  ⚠️ 缓存为空")
                files_status['names'] = 0
        except Exception as e:
            print(f"  ❌ 读取失败: {str(e)}")
            files_status['names'] = -1
    else:
        print(f"  ❌ 文件不存在")
        files_status['names'] = -1
    
    return files_status

def generate_test_data():
    """生成测试数据"""
    print("\n🔧 生成测试数据")
    print("=" * 30)
    
    # 生成测试的人气榜数据
    test_popularity = [
        {'code': '600036', 'name': '招商银行', 'change': '+2.15%'},
        {'code': '000001', 'name': '平安银行', 'change': '-1.23%'},
        {'code': '000002', 'name': '万科A', 'change': '+0.85%'},
        {'code': '600519', 'name': '贵州茅台', 'change': '+1.45%'},
        {'code': '000858', 'name': '五粮液', 'change': '-0.67%'}
    ]
    
    try:
        with open('popularity.csv', 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=['code', 'name', 'change'])
            writer.writeheader()
            writer.writerows(test_popularity)
        print("✅ 测试人气榜数据已生成")
    except Exception as e:
        print(f"❌ 生成人气榜数据失败: {str(e)}")
    
    # 生成测试的飙升榜数据
    test_soaring = [
        {'code': '300015', 'name': '爱尔眼科', 'change': '+4.56%'},
        {'code': '002415', 'name': '海康威视', 'change': '+3.21%'},
        {'code': '000725', 'name': '京东方A', 'change': '+2.89%'},
        {'code': '002594', 'name': '比亚迪', 'change': '+5.67%'},
        {'code': '300059', 'name': '东方财富', 'change': '+1.98%'}
    ]
    
    try:
        with open('soaring.csv', 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=['code', 'name', 'change'])
            writer.writeheader()
            writer.writerows(test_soaring)
        print("✅ 测试飙升榜数据已生成")
    except Exception as e:
        print(f"❌ 生成飙升榜数据失败: {str(e)}")
    
    # 生成测试的股票代码
    test_codes = ['600036', '000001', '000002', '600519', '000858', 
                  '300015', '002415', '000725', '002594', '300059']
    
    try:
        with open('codes.txt', 'w', encoding='utf-8') as f:
            for code in test_codes:
                f.write(f"{code}\n")
        print("✅ 测试股票代码已生成")
    except Exception as e:
        print(f"❌ 生成股票代码失败: {str(e)}")
    
    # 生成测试的名称缓存
    test_names = {
        '600036': '招商银行', '000001': '平安银行', '000002': '万科A',
        '600519': '贵州茅台', '000858': '五粮液', '300015': '爱尔眼科',
        '002415': '海康威视', '000725': '京东方A', '002594': '比亚迪',
        '300059': '东方财富'
    }
    
    try:
        cache_data = {
            'names': test_names,
            'update_time': '2025-01-30 14:00:00'
        }
        with open('stock_names_cache.json', 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)
        print("✅ 测试名称缓存已生成")
    except Exception as e:
        print(f"❌ 生成名称缓存失败: {str(e)}")

def main():
    """主函数"""
    print("🧪 股票数据文件检查工具")
    print("📅 检查时间:", __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 检查现有数据
    files_status = check_data_files()
    
    # 分析结果
    print(f"\n📊 检查结果总结:")
    print(f"=" * 30)
    
    all_good = True
    for file_type, count in files_status.items():
        if count > 0:
            print(f"  ✅ {file_type}: {count} 条记录")
        elif count == 0:
            print(f"  ⚠️ {file_type}: 文件为空")
            all_good = False
        else:
            print(f"  ❌ {file_type}: 文件不存在或读取失败")
            all_good = False
    
    if not all_good:
        print(f"\n❓ 是否生成测试数据？")
        try:
            choice = input("输入 'y' 生成测试数据，其他键跳过: ").strip().lower()
            if choice == 'y':
                generate_test_data()
                print(f"\n🎉 测试数据生成完成！")
                print(f"💡 现在可以运行 'python 智能数据分析报告.py' 测试")
        except KeyboardInterrupt:
            print(f"\n👋 操作已取消")
    else:
        print(f"\n🎉 所有数据文件检查通过！")
        print(f"💡 可以正常运行智能数据分析报告")

if __name__ == "__main__":
    main()

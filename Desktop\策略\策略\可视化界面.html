<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票数据采集系统 - 可视化界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-icon {
            font-size: 2em;
            margin-right: 15px;
        }

        .card-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .control-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .btn-info {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .data-table {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success {
            background: #28a745;
        }

        .status-warning {
            background: #ffc107;
        }

        .status-error {
            background: #dc3545;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }

        .log-panel {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-info {
            color: #3498db;
        }

        .log-success {
            color: #2ecc71;
        }

        .log-error {
            color: #e74c3c;
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 股票数据采集系统</h1>
            <p>智能化股票数据采集与分析平台</p>
        </div>

        <div class="dashboard">
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🎯</div>
                    <div class="card-title">采集统计</div>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="totalStocks">98</div>
                        <div class="stat-label">总股票数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="popularityStocks">50</div>
                        <div class="stat-label">人气榜</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="soaringStocks">50</div>
                        <div class="stat-label">飙升榜</div>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 90%"></div>
                </div>
                <p style="text-align: center; color: #666; margin-top: 10px;">数据完整性: 90%</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon">📈</div>
                    <div class="card-title">市场分布</div>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" style="color: #e74c3c;">45</div>
                        <div class="stat-label">沪市 (SH)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" style="color: #2ecc71;">53</div>
                        <div class="stat-label">深市 (SZ)</div>
                    </div>
                </div>
                <canvas id="marketChart" width="300" height="150" style="margin-top: 15px;"></canvas>
            </div>
        </div>

        <div class="control-panel">
            <div class="card-header">
                <div class="card-icon">🎮</div>
                <div class="card-title">控制面板</div>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="startCollection()">
                    🚀 开始采集
                </button>
                <button class="btn btn-success" onclick="processData()">
                    ⚙️ 处理数据
                </button>
                <button class="btn btn-info" onclick="generateReport()">
                    📊 生成报告
                </button>
                <button class="btn btn-primary" onclick="exportData()">
                    💾 导出数据
                </button>
            </div>
        </div>

        <div class="data-table">
            <div class="card-header">
                <div class="card-icon">📋</div>
                <div class="card-title">实时数据</div>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>状态</th>
                        <th>股票代码</th>
                        <th>股票名称</th>
                        <th>市场</th>
                        <th>涨跌幅</th>
                        <th>更新时间</th>
                    </tr>
                </thead>
                <tbody id="dataTableBody">
                    <!-- 数据将通过JavaScript动态填充 -->
                </tbody>
            </table>
        </div>

        <div class="log-panel">
            <div style="margin-bottom: 15px; font-weight: bold;">📝 系统日志</div>
            <div id="logContainer">
                <div class="log-entry log-success">[2025-07-30 08:13:25] ✅ 系统初始化完成</div>
                <div class="log-entry log-info">[2025-07-30 08:13:26] 🔍 开始股票代码提取...</div>
                <div class="log-entry log-success">[2025-07-30 08:13:45] ✅ 成功提取98只股票代码</div>
                <div class="log-entry log-info">[2025-07-30 08:13:46] 📊 开始人气榜数据采集...</div>
                <div class="log-entry log-success">[2025-07-30 08:14:15] ✅ 人气榜数据采集完成 (50只)</div>
                <div class="log-entry log-info">[2025-07-30 08:14:16] 🚀 开始飙升榜数据采集...</div>
                <div class="log-entry log-success">[2025-07-30 08:14:45] ✅ 飙升榜数据采集完成 (50只)</div>
                <div class="log-entry log-success">[2025-07-30 08:14:46] 🎉 所有采集任务完成！</div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const sampleData = [
            { code: '000003', name: '万科A', market: 'SZ', change: '+2.34%', status: 'success' },
            { code: '600570', name: '恒生电子', market: 'SH', change: '*****%', status: 'success' },
            { code: '002097', name: '山河智能', market: 'SZ', change: '*****%', status: 'success' },
            { code: '603259', name: '药明康德', market: 'SH', change: '-0.45%', status: 'warning' },
            { code: '300149', name: '量子生物', market: 'SZ', change: '*****%', status: 'success' }
        ];

        // 填充数据表格
        function populateTable() {
            const tbody = document.getElementById('dataTableBody');
            tbody.innerHTML = '';
            
            sampleData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><span class="status-indicator status-${item.status}"></span></td>
                    <td>${item.code}</td>
                    <td>${item.name}</td>
                    <td>${item.market}</td>
                    <td style="color: ${item.change.startsWith('+') ? '#2ecc71' : '#e74c3c'}">${item.change}</td>
                    <td>${new Date().toLocaleString()}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // 控制面板功能
        function startCollection() {
            addLog('🚀 开始执行数据采集任务...', 'info');
            setTimeout(() => addLog('✅ 数据采集完成！', 'success'), 2000);
        }

        function processData() {
            addLog('⚙️ 开始处理数据...', 'info');
            setTimeout(() => addLog('✅ 数据处理完成！', 'success'), 1500);
        }

        function generateReport() {
            addLog('📊 生成分析报告...', 'info');
            setTimeout(() => addLog('✅ 报告生成完成！', 'success'), 1000);
        }

        function exportData() {
            addLog('💾 导出数据文件...', 'info');
            setTimeout(() => addLog('✅ 数据导出完成！', 'success'), 800);
        }

        // 添加日志
        function addLog(message, type) {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 绘制市场分布图
        function drawMarketChart() {
            const canvas = document.getElementById('marketChart');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制饼图
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = 60;
            
            const shPercent = 45 / 98;
            const szPercent = 53 / 98;
            
            // 沪市部分
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI * shPercent);
            ctx.lineTo(centerX, centerY);
            ctx.fillStyle = '#e74c3c';
            ctx.fill();
            
            // 深市部分
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 2 * Math.PI * shPercent, 2 * Math.PI);
            ctx.lineTo(centerX, centerY);
            ctx.fillStyle = '#2ecc71';
            ctx.fill();
            
            // 添加标签
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('沪市 46%', 10, 20);
            ctx.fillText('深市 54%', 10, 35);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            populateTable();
            drawMarketChart();
            
            // 定时更新数据
            setInterval(() => {
                populateTable();
            }, 30000);
        });
    </script>
</body>
</html>

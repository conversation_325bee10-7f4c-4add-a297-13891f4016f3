#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行业板块分类系统
根据股票代码和名称判断所属行业
"""

import re

class IndustryClassifier:
    def __init__(self):
        # 行业关键词映射
        self.industry_keywords = {
            '银行': ['银行', '农商', '农信', '信用社', '村镇银行'],
            '保险': ['保险', '人寿', '财险', '太保', '平安', '新华保险'],
            '证券': ['证券', '信托', '期货', '基金', '投资', '资管'],
            
            '房地产': ['地产', '房地产', '置业', '发展', '建设', '城建', '万科', '恒大', '碧桂园'],
            '建筑建材': ['建筑', '建材', '水泥', '钢构', '装饰', '园林', '基建'],
            '建筑装饰': ['装饰', '装修', '设计', '园林', '景观'],
            
            '钢铁': ['钢铁', '特钢', '不锈钢', '钢管', '钢丝', '铁矿'],
            '有色金属': ['有色', '铝业', '铜业', '锌业', '铅锌', '稀土', '黄金', '白银'],
            '煤炭': ['煤炭', '煤业', '焦炭', '焦煤'],
            
            '石油石化': ['石油', '石化', '化工', '炼化', '油气'],
            '化工': ['化工', '化学', '农药', '化肥', '塑料', '橡胶', '涂料'],
            '新材料': ['新材料', '材料', '复合材料', '纳米', '石墨烯'],
            
            '电力': ['电力', '发电', '水电', '火电', '核电', '新能源发电'],
            '公用事业': ['燃气', '水务', '供水', '污水', '环保', '垃圾处理'],
            '环保': ['环保', '节能', '减排', '治污', '废物处理'],
            
            '汽车': ['汽车', '客车', '货车', '乘用车', '商用车', '汽配'],
            '汽车零部件': ['汽配', '轮胎', '玻璃', '座椅', '发动机'],
            '新能源汽车': ['新能源汽车', '电动车', '动力电池', '充电桩'],
            
            '机械设备': ['机械', '设备', '重工', '装备', '工程机械', '机床'],
            '电气设备': ['电气', '电器', '变压器', '开关', '电缆'],
            '仪器仪表': ['仪器', '仪表', '测试', '检测', '计量'],
            
            '电子': ['电子', '半导体', '芯片', '集成电路', 'PCB', '显示器'],
            '计算机': ['计算机', '软件', '信息技术', 'IT', '云计算', '大数据'],
            '通信': ['通信', '通讯', '网络', '光纤', '5G', '物联网'],
            '人工智能': ['人工智能', 'AI', '机器人', '自动化', '智能'],
            
            '医药生物': ['医药', '生物', '制药', '药业', '疫苗', '医疗器械'],
            '医疗器械': ['医疗器械', '医疗设备', '诊断', '影像', '手术'],
            '医疗服务': ['医疗服务', '医院', '体检', '康复', '养老'],
            
            '食品饮料': ['食品', '饮料', '乳业', '酒类', '调味品', '休闲食品'],
            '农林牧渔': ['农业', '林业', '牧业', '渔业', '种植', '养殖'],
            '纺织服装': ['纺织', '服装', '服饰', '鞋帽', '家纺'],
            
            '商业贸易': ['商业', '贸易', '百货', '超市', '零售', '批发'],
            '社会服务': ['旅游', '酒店', '餐饮', '教育', '培训', '文化'],
            '传媒': ['传媒', '广告', '影视', '出版', '游戏', '动漫'],
            
            '交通运输': ['交通', '运输', '物流', '快递', '航空', '港口', '公路', '铁路'],
            '航空航天': ['航空', '航天', '飞机', '卫星', '导航'],
            '船舶制造': ['船舶', '造船', '海工', '海洋工程'],
            
            '轻工制造': ['轻工', '造纸', '包装', '印刷', '文具', '玩具'],
            '家用电器': ['家电', '空调', '冰箱', '洗衣机', '厨电'],
            '家具': ['家具', '家居', '定制家具', '办公家具'],
            
            '综合': ['综合', '多元化', '控股', '集团'],
        }
        
        # 特殊股票代码映射（知名公司）
        self.special_codes = {
            # 银行
            '600036': '银行', '000001': '银行', '601318': '银行', '600000': '银行',
            '601328': '银行', '000002': '银行', '601166': '银行', '600015': '银行',
            
            # 保险
            '601318': '保险', '601601': '保险', '601628': '保险',
            
            # 证券
            '000166': '证券', '600030': '证券', '000776': '证券', '600837': '证券',
            
            # 地产
            '000002': '房地产', '000069': '房地产', '600048': '房地产', '001979': '房地产',
            
            # 白酒
            '600519': '食品饮料', '000858': '食品饮料', '000596': '食品饮料',
            
            # 科技
            '000063': '通信', '600570': '电子', '002415': '电子', '300059': '计算机',
            
            # 医药
            '600276': '医药生物', '000423': '医药生物', '300015': '医药生物',
            
            # 汽车
            '002594': '新能源汽车', '000625': '汽车', '600104': '汽车',
            
            # 钢铁
            '600010': '钢铁', '000709': '钢铁', '600581': '钢铁',
        }
    
    def classify_by_name(self, name):
        """根据股票名称分类行业"""
        if not name:
            return '其他'
        
        # 遍历行业关键词
        for industry, keywords in self.industry_keywords.items():
            for keyword in keywords:
                if keyword in name:
                    return industry
        
        return '其他'
    
    def classify_by_code(self, code):
        """根据股票代码分类行业"""
        if code in self.special_codes:
            return self.special_codes[code]
        return None
    
    def get_industry(self, code, name):
        """获取股票所属行业"""
        # 优先使用代码映射
        industry = self.classify_by_code(code)
        if industry:
            return industry
        
        # 使用名称分类
        industry = self.classify_by_name(name)
        return industry

def test_classifier():
    """测试分类器"""
    classifier = IndustryClassifier()
    
    test_stocks = [
        ('600036', '招商银行'),
        ('600519', '贵州茅台'),
        ('000002', '万科A'),
        ('002594', '比亚迪'),
        ('600276', '恒瑞医药'),
        ('000063', '中兴通讯'),
        ('600010', '包钢股份'),
        ('300015', '爱尔眼科'),
        ('002415', '海康威视'),
        ('000858', '五粮液'),
    ]
    
    print("🧪 行业分类测试:")
    print("-" * 40)
    for code, name in test_stocks:
        industry = classifier.get_industry(code, name)
        print(f"{code} {name:<10} -> {industry}")

if __name__ == "__main__":
    test_classifier()

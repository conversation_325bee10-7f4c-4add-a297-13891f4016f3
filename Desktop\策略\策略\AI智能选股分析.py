#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI智能选股分析系统
综合分析热门股和消息面选股结果，生成最终购买建议
"""

import json
import csv
import pandas as pd
from datetime import datetime
import os
import re

# 导入Web实时显示功能
try:
    from AI分析实时显示 import start_ai_web_monitor, log_ai_analysis
    WEB_MONITOR_AVAILABLE = True
except ImportError:
    WEB_MONITOR_AVAILABLE = False
    def log_ai_analysis(message, level="INFO"):
        print(f"[{level}] {message}")

class AIStockAnalyzer:
    def __init__(self):
        self.hot_stocks = []  # 热门股数据
        self.news_stocks = []  # 消息面股票数据
        self.combined_analysis = []  # 综合分析结果
        self.final_recommendations = []  # 最终购买建议
        
    def load_hot_stocks_data(self):
        """加载热门股数据"""
        try:
            print("📊 加载热门股数据...")
            log_ai_analysis("📊 开始加载热门股数据...", "INFO")
            hot_stocks = []
            
            # 加载人气榜数据
            if os.path.exists('popularity.csv'):
                with open('popularity.csv', 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        row['source'] = '人气榜'
                        row['hot_score'] = self.calculate_popularity_score(row)
                        hot_stocks.append(row)
                print(f"  ✅ 人气榜: {len([r for r in hot_stocks if r['source'] == '人气榜'])} 只")
            
            # 加载飙升榜数据
            if os.path.exists('soaring.csv'):
                with open('soaring.csv', 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        row['source'] = '飙升榜'
                        row['hot_score'] = self.calculate_soaring_score(row)
                        hot_stocks.append(row)
                print(f"  ✅ 飙升榜: {len([r for r in hot_stocks if r['source'] == '飙升榜'])} 只")
            
            self.hot_stocks = hot_stocks
            print(f"📊 热门股总计: {len(hot_stocks)} 只")
            log_ai_analysis(f"✅ 热门股数据加载完成: {len(hot_stocks)} 只", "SUCCESS")
            return True
            
        except Exception as e:
            print(f"❌ 加载热门股数据失败: {str(e)}")
            return False
    
    def load_news_stocks_data(self):
        """加载消息面股票数据"""
        try:
            print("📰 加载消息面股票数据...")
            
            if os.path.exists('消息面选股结果.json'):
                with open('消息面选股结果.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.news_stocks = data.get('recommendations', [])
                print(f"📰 消息面股票: {len(self.news_stocks)} 只")
                return True
            else:
                print("⚠️ 未找到消息面选股结果文件")
                return False
                
        except Exception as e:
            print(f"❌ 加载消息面数据失败: {str(e)}")
            return False
    
    def calculate_popularity_score(self, stock):
        """计算人气得分"""
        try:
            # 基于涨跌幅、成交量等计算人气得分
            change_str = stock.get('change', '0%').replace('%', '')
            change = float(change_str) if change_str.replace('-', '').replace('+', '').replace('.', '').isdigit() else 0
            
            # 人气得分算法
            score = 50  # 基础分
            
            # 涨跌幅影响 (±30分)
            if change > 5:
                score += 30
            elif change > 2:
                score += 20
            elif change > 0:
                score += 10
            elif change > -2:
                score += 0
            elif change > -5:
                score -= 10
            else:
                score -= 20
            
            # 排名影响 (±20分)
            try:
                rank = int(stock.get('rank', 999))
                if rank <= 10:
                    score += 20
                elif rank <= 30:
                    score += 10
                elif rank <= 50:
                    score += 5
            except:
                pass
            
            return max(0, min(100, score))
            
        except Exception:
            return 50
    
    def calculate_soaring_score(self, stock):
        """计算飙升得分"""
        try:
            # 飙升榜通常表示短期爆发力
            change_str = stock.get('change', '0%').replace('%', '')
            change = float(change_str) if change_str.replace('-', '').replace('+', '').replace('.', '').isdigit() else 0
            
            score = 60  # 飙升榜基础分更高
            
            # 涨跌幅影响更大
            if change > 8:
                score += 40
            elif change > 5:
                score += 30
            elif change > 2:
                score += 20
            elif change > 0:
                score += 10
            else:
                score -= 15
            
            return max(0, min(100, score))
            
        except Exception:
            return 60
    
    def combine_stock_data(self):
        """合并股票数据"""
        try:
            print("\n🔄 合并股票数据...")
            combined = {}
            
            # 处理热门股数据
            for stock in self.hot_stocks:
                code = stock.get('code', '')
                if code:
                    if code not in combined:
                        combined[code] = {
                            'code': code,
                            'name': stock.get('name', ''),
                            'hot_sources': [],
                            'hot_score': 0,
                            'news_score': 0,
                            'news_level': '',
                            'has_hot_data': False,
                            'has_news_data': False
                        }
                    
                    combined[code]['hot_sources'].append(stock.get('source', ''))
                    combined[code]['hot_score'] = max(combined[code]['hot_score'], stock.get('hot_score', 0))
                    combined[code]['has_hot_data'] = True
                    combined[code]['change'] = stock.get('change', '0%')
                    combined[code]['price'] = stock.get('price', '')
            
            # 处理消息面数据
            for stock in self.news_stocks:
                code = stock.get('code', '')
                if code:
                    if code not in combined:
                        combined[code] = {
                            'code': code,
                            'name': stock.get('name', ''),
                            'hot_sources': [],
                            'hot_score': 0,
                            'news_score': 0,
                            'news_level': '',
                            'has_hot_data': False,
                            'has_news_data': False
                        }
                    
                    combined[code]['news_score'] = stock.get('sentiment_score', 0)
                    combined[code]['news_level'] = stock.get('recommend_level', '')
                    combined[code]['mention_count'] = stock.get('mention_count', 0)
                    combined[code]['has_news_data'] = True
            
            self.combined_analysis = list(combined.values())
            print(f"🔄 合并完成: {len(self.combined_analysis)} 只股票")
            
            # 统计数据来源
            both_sources = len([s for s in self.combined_analysis if s['has_hot_data'] and s['has_news_data']])
            only_hot = len([s for s in self.combined_analysis if s['has_hot_data'] and not s['has_news_data']])
            only_news = len([s for s in self.combined_analysis if not s['has_hot_data'] and s['has_news_data']])
            
            print(f"  📊 数据分布:")
            print(f"    🔥 热门+消息面: {both_sources} 只")
            print(f"    📈 仅热门股: {only_hot} 只")
            print(f"    📰 仅消息面: {only_news} 只")
            
            return True
            
        except Exception as e:
            print(f"❌ 合并数据失败: {str(e)}")
            return False
    
    def ai_comprehensive_analysis(self):
        """AI综合分析"""
        try:
            print("\n🤖 AI综合分析中...")
            
            for stock in self.combined_analysis:
                # 计算综合得分
                stock['ai_score'] = self.calculate_ai_score(stock)
                
                # AI风险评估
                stock['risk_level'] = self.assess_risk_level(stock)
                
                # 生成AI分析理由
                stock['ai_reason'] = self.generate_ai_reason(stock)
                
                # 投资建议
                stock['investment_advice'] = self.generate_investment_advice(stock)
            
            # 按AI得分排序
            self.combined_analysis.sort(key=lambda x: x['ai_score'], reverse=True)
            
            print(f"🤖 AI分析完成: {len(self.combined_analysis)} 只股票")
            return True
            
        except Exception as e:
            print(f"❌ AI分析失败: {str(e)}")
            return False
    
    def calculate_ai_score(self, stock):
        """计算AI综合得分"""
        try:
            score = 0
            
            # 热门股得分权重 40%
            hot_score = stock.get('hot_score', 0)
            score += hot_score * 0.4
            
            # 消息面得分权重 35%
            news_score = stock.get('news_score', 0)
            # 将消息面得分标准化到0-100
            normalized_news = max(0, min(100, (news_score + 2) * 25))
            score += normalized_news * 0.35
            
            # 数据源加分 25%
            source_bonus = 0
            if stock['has_hot_data'] and stock['has_news_data']:
                source_bonus = 25  # 双重验证加分
            elif stock['has_hot_data']:
                source_bonus = 15  # 热门股加分
            elif stock['has_news_data']:
                source_bonus = 10  # 消息面加分
            
            score += source_bonus
            
            # 特殊情况调整
            news_level = stock.get('news_level', '')
            if '强烈推荐' in news_level:
                score += 10
            elif '推荐' in news_level:
                score += 5
            elif '回避' in news_level:
                score -= 10
            
            return round(max(0, min(100, score)), 2)
            
        except Exception:
            return 50
    
    def assess_risk_level(self, stock):
        """评估风险等级"""
        try:
            ai_score = stock.get('ai_score', 50)
            has_both = stock['has_hot_data'] and stock['has_news_data']
            news_level = stock.get('news_level', '')
            
            if ai_score >= 80 and has_both:
                return "低风险"
            elif ai_score >= 70:
                return "中低风险"
            elif ai_score >= 60:
                return "中等风险"
            elif ai_score >= 50:
                return "中高风险"
            else:
                return "高风险"
                
        except Exception:
            return "未知风险"
    
    def generate_ai_reason(self, stock):
        """生成AI分析理由"""
        try:
            reasons = []
            
            # 数据源分析
            if stock['has_hot_data'] and stock['has_news_data']:
                reasons.append("热门股和消息面双重验证")
            elif stock['has_hot_data']:
                sources = stock.get('hot_sources', [])
                if len(sources) > 1:
                    reasons.append(f"同时出现在{'+'.join(sources)}")
                else:
                    reasons.append(f"入选{sources[0] if sources else '热门榜'}")
            elif stock['has_news_data']:
                reasons.append("消息面情感分析发现")
            
            # 得分分析
            ai_score = stock.get('ai_score', 0)
            if ai_score >= 80:
                reasons.append("AI综合得分优秀")
            elif ai_score >= 70:
                reasons.append("AI综合得分良好")
            elif ai_score >= 60:
                reasons.append("AI综合得分中等")
            
            # 消息面分析
            news_level = stock.get('news_level', '')
            if news_level:
                level_name = news_level.split(' ')[-1] if ' ' in news_level else news_level
                reasons.append(f"消息面{level_name}")
            
            return "；".join(reasons) if reasons else "综合分析结果"
            
        except Exception:
            return "AI分析"
    
    def generate_investment_advice(self, stock):
        """生成投资建议"""
        try:
            ai_score = stock.get('ai_score', 50)
            risk_level = stock.get('risk_level', '')
            
            if ai_score >= 85:
                return "🔥 强烈建议买入"
            elif ai_score >= 75:
                return "⭐ 建议买入"
            elif ai_score >= 65:
                return "👀 可以考虑"
            elif ai_score >= 55:
                return "⚠️ 谨慎观望"
            else:
                return "❌ 不建议买入"
                
        except Exception:
            return "🤔 需要更多分析"
    
    def generate_final_recommendations(self):
        """生成最终购买建议"""
        try:
            print("\n🎯 生成最终购买建议...")
            
            # 筛选高分股票
            high_score_stocks = [s for s in self.combined_analysis if s.get('ai_score', 0) >= 65]
            
            # 按投资建议分组
            buy_strong = [s for s in high_score_stocks if '强烈建议买入' in s.get('investment_advice', '')]
            buy_normal = [s for s in high_score_stocks if '建议买入' in s.get('investment_advice', '')]
            consider = [s for s in high_score_stocks if '可以考虑' in s.get('investment_advice', '')]
            
            self.final_recommendations = {
                'strong_buy': buy_strong[:5],  # 最多5只强烈推荐
                'buy': buy_normal[:8],         # 最多8只一般推荐
                'consider': consider[:10],     # 最多10只考虑
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_analyzed': len(self.combined_analysis),
                'high_score_count': len(high_score_stocks)
            }
            
            print(f"🎯 最终建议:")
            print(f"  🔥 强烈建议买入: {len(buy_strong)} 只")
            print(f"  ⭐ 建议买入: {len(buy_normal)} 只")
            print(f"  👀 可以考虑: {len(consider)} 只")
            
            return True
            
        except Exception as e:
            print(f"❌ 生成最终建议失败: {str(e)}")
            return False

    def save_results(self):
        """保存分析结果"""
        try:
            print("\n💾 保存分析结果...")

            # 保存完整分析结果
            complete_data = {
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'method': 'AI智能选股分析',
                'data_sources': {
                    'hot_stocks_count': len(self.hot_stocks),
                    'news_stocks_count': len(self.news_stocks),
                    'combined_count': len(self.combined_analysis)
                },
                'complete_analysis': self.combined_analysis,
                'final_recommendations': self.final_recommendations
            }

            with open('AI智能选股分析结果.json', 'w', encoding='utf-8') as f:
                json.dump(complete_data, f, ensure_ascii=False, indent=2)

            # 保存购买建议CSV
            with open('AI最终购买建议.csv', 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['建议等级', '排名', '股票代码', '股票名称', 'AI得分', '风险等级', '数据来源', 'AI分析理由'])

                # 强烈推荐
                for i, stock in enumerate(self.final_recommendations['strong_buy'], 1):
                    sources = self.get_data_sources(stock)
                    writer.writerow([
                        '🔥 强烈建议买入', i, stock['code'], stock['name'],
                        stock['ai_score'], stock['risk_level'], sources, stock['ai_reason']
                    ])

                # 一般推荐
                for i, stock in enumerate(self.final_recommendations['buy'], 1):
                    sources = self.get_data_sources(stock)
                    writer.writerow([
                        '⭐ 建议买入', i, stock['code'], stock['name'],
                        stock['ai_score'], stock['risk_level'], sources, stock['ai_reason']
                    ])

                # 可以考虑
                for i, stock in enumerate(self.final_recommendations['consider'], 1):
                    sources = self.get_data_sources(stock)
                    writer.writerow([
                        '👀 可以考虑', i, stock['code'], stock['name'],
                        stock['ai_score'], stock['risk_level'], sources, stock['ai_reason']
                    ])

            print("✅ 结果已保存:")
            print("  📄 AI智能选股分析结果.json")
            print("  📊 AI最终购买建议.csv")

            return True

        except Exception as e:
            print(f"❌ 保存结果失败: {str(e)}")
            return False

    def get_data_sources(self, stock):
        """获取数据来源描述"""
        sources = []
        if stock['has_hot_data']:
            hot_sources = stock.get('hot_sources', [])
            sources.extend(hot_sources)
        if stock['has_news_data']:
            sources.append('消息面')
        return '+'.join(sources) if sources else '未知'

    def display_results(self):
        """显示分析结果"""
        try:
            print("\n" + "="*80)
            print("🤖 AI智能选股分析结果")
            print("="*80)

            # 显示数据概览
            print(f"\n📊 数据概览:")
            print(f"  📈 热门股数据: {len(self.hot_stocks)} 只")
            print(f"  📰 消息面数据: {len(self.news_stocks)} 只")
            print(f"  🔄 合并分析: {len(self.combined_analysis)} 只")
            print(f"  🎯 高分股票: {self.final_recommendations['high_score_count']} 只")

            # 显示最终购买建议
            print(f"\n🎯 AI最终购买建议:")
            print("-"*80)

            # 强烈推荐
            strong_buy = self.final_recommendations['strong_buy']
            if strong_buy:
                print(f"\n🔥 强烈建议买入 ({len(strong_buy)} 只):")
                print(f"{'排名':<4} {'代码':<8} {'名称':<12} {'AI得分':<8} {'风险等级':<10} {'数据来源':<15}")
                print("-"*70)
                for i, stock in enumerate(strong_buy, 1):
                    sources = self.get_data_sources(stock)
                    print(f"{i:<4} {stock['code']:<8} {stock['name']:<12} {stock['ai_score']:<8} "
                          f"{stock['risk_level']:<10} {sources:<15}")

            # 一般推荐
            buy = self.final_recommendations['buy']
            if buy:
                print(f"\n⭐ 建议买入 ({len(buy)} 只):")
                print(f"{'排名':<4} {'代码':<8} {'名称':<12} {'AI得分':<8} {'风险等级':<10} {'数据来源':<15}")
                print("-"*70)
                for i, stock in enumerate(buy, 1):
                    sources = self.get_data_sources(stock)
                    print(f"{i:<4} {stock['code']:<8} {stock['name']:<12} {stock['ai_score']:<8} "
                          f"{stock['risk_level']:<10} {sources:<15}")

            # 可以考虑
            consider = self.final_recommendations['consider']
            if consider:
                print(f"\n👀 可以考虑 ({len(consider)} 只):")
                print(f"{'排名':<4} {'代码':<8} {'名称':<12} {'AI得分':<8} {'风险等级':<10} {'数据来源':<15}")
                print("-"*70)
                for i, stock in enumerate(consider, 1):
                    sources = self.get_data_sources(stock)
                    print(f"{i:<4} {stock['code']:<8} {stock['name']:<12} {stock['ai_score']:<8} "
                          f"{stock['risk_level']:<10} {sources:<15}")

            print("\n" + "="*80)
            print("💡 投资建议:")
            print("  • 强烈建议买入：AI得分85+，双重数据验证，低风险")
            print("  • 建议买入：AI得分75+，数据支撑良好，中低风险")
            print("  • 可以考虑：AI得分65+，有一定投资价值，需谨慎")
            print("  • 请结合个人风险承受能力和资金情况做最终决策")
            print("="*80)

            return True

        except Exception as e:
            print(f"❌ 显示结果失败: {str(e)}")
            return False

    def run_analysis(self):
        """运行完整AI分析"""
        try:
            print("🤖 AI智能选股分析系统")
            print("综合分析热门股和消息面数据，生成最终购买建议")
            print("="*80)

            # 1. 加载数据
            if not self.load_hot_stocks_data():
                print("❌ 加载热门股数据失败")
                return False

            if not self.load_news_stocks_data():
                print("❌ 加载消息面数据失败")
                return False

            # 2. 合并数据
            if not self.combine_stock_data():
                print("❌ 合并数据失败")
                return False

            # 3. AI分析
            if not self.ai_comprehensive_analysis():
                print("❌ AI分析失败")
                return False

            # 4. 生成最终建议
            if not self.generate_final_recommendations():
                print("❌ 生成最终建议失败")
                return False

            # 5. 保存结果
            if not self.save_results():
                print("❌ 保存结果失败")
                return False

            # 6. 显示结果
            if not self.display_results():
                print("❌ 显示结果失败")
                return False

            print(f"\n🎉 AI智能选股分析完成！")
            return True

        except Exception as e:
            print(f"❌ AI分析系统运行失败: {str(e)}")
            return False

def main():
    """主函数"""
    analyzer = AIStockAnalyzer()
    success = analyzer.run_analysis()

    if success:
        print(f"\n✅ AI智能选股分析成功完成")
    else:
        print(f"\n❌ AI智能选股分析失败")

if __name__ == "__main__":
    main()

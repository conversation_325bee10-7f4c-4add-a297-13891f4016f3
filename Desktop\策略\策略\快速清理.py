#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速清理脚本
自动删除测试和临时文件
"""

import os
import glob

def quick_cleanup():
    """快速清理"""
    print("🗑️ 快速清理多余文件")
    print("=" * 40)
    
    # 要删除的文件模式
    delete_patterns = [
        '*测试*.py',
        '*诊断*.py', 
        '*验证*.py',
        '*检查*.py',
        '简单*.py',
        '快速*.py',
        '最终*.py',
        '生成大量*.py',
        '修改测试*.py',
        'codes.txt',
        'stock_cache.json',
        '股票板块整合报告.html',
        '可视化界面_实时版.html',
        '测试报告.html',
        '股票分析报告_图表版.html'
    ]
    
    # 收集要删除的文件
    files_to_delete = []
    for pattern in delete_patterns:
        matching_files = glob.glob(pattern)
        files_to_delete.extend(matching_files)
    
    # 去重
    files_to_delete = list(set(files_to_delete))
    
    if not files_to_delete:
        print("✅ 没有找到需要清理的文件")
        return
    
    print(f"📋 找到 {len(files_to_delete)} 个文件需要清理:")
    total_size = 0
    
    for file in sorted(files_to_delete):
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024
            total_size += size
            print(f"  🗑️ {file:<30} ({size:.1f} KB)")
    
    print(f"\n📊 总计释放空间: {total_size:.1f} KB")
    
    # 确认删除
    choice = input(f"\n❓ 确认删除这 {len(files_to_delete)} 个文件？(y/n): ").strip().lower()
    
    if choice != 'y':
        print("❌ 清理已取消")
        return
    
    # 执行删除
    print(f"\n🗑️ 开始删除文件...")
    deleted_count = 0
    
    for file in files_to_delete:
        try:
            if os.path.exists(file):
                os.remove(file)
                print(f"  ✅ 已删除: {file}")
                deleted_count += 1
        except Exception as e:
            print(f"  ❌ 删除失败: {file} - {str(e)}")
    
    print(f"\n🎉 清理完成！")
    print(f"✅ 成功删除 {deleted_count} 个文件")
    print(f"💾 释放空间 {total_size:.1f} KB")
    
    # 显示保留的核心文件
    print(f"\n📋 保留的核心文件:")
    core_files = [
        '可视化主程序.py',
        '后台采集.py',
        '智能选股系统.py', 
        '选股报告生成器.py',
        '行业板块分类.py',
        '增强数据显示.py',
        '一键选股.py'
    ]
    
    for file in core_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (缺失)")
    
    # 显示数据和报告文件
    print(f"\n📊 数据和报告文件:")
    data_files = [
        'popularity.csv',
        'soaring.csv', 
        '选股结果.json',
        '推荐股票汇总.csv',
        '智能选股分析报告.html',
        '股票行业整合报告.html',
        '股票分析报告.html'
    ]
    
    for file in data_files:
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024
            print(f"  📁 {file:<30} ({size:.1f} KB)")

def create_readme():
    """创建README文件"""
    readme_content = """# 股票数据采集与智能选股系统

## 🎯 系统简介
基于热门股票数据的多维度智能选股分析系统，提供数据采集、选股分析、报告生成等完整功能。

## 🚀 核心功能
- 📊 **数据采集**: 自动采集人气榜和飙升榜股票数据
- 🎯 **智能选股**: 6种选股策略多维度分析
- 📋 **报告生成**: 美观的HTML分析报告
- 🏭 **行业分析**: 27个行业智能分类
- 🖥️ **GUI界面**: 友好的可视化操作界面

## 📁 核心文件
- `可视化主程序.py` - GUI主界面
- `后台采集.py` - 股票数据采集
- `智能选股系统.py` - 多策略选股分析
- `选股报告生成器.py` - HTML报告生成
- `行业板块分类.py` - 行业智能分类
- `增强数据显示.py` - 数据整合显示
- `一键选股.py` - 完整选股流程

## 🎯 使用方法
1. 运行 `python 可视化主程序.py` 启动GUI界面
2. 点击 "🔄 开始数据采集" 获取最新数据
3. 点击 "🎯 智能选股分析" 进行选股
4. 点击 "🏆 查看选股结果" 查看结果
5. 或使用 "⚡ 一键选股流程" 执行完整流程

## 📊 选股策略
- 🚀 **动量选股**: 选择强势上涨股票
- 💎 **价值选股**: 选择相对低估股票
- 🌱 **成长选股**: 选择高成长潜力股票
- ⚖️ **均衡选股**: 综合考虑多个因素
- 🔥 **热门板块**: 选择表现最佳行业股票
- 🛡️ **风险控制**: 选择相对安全股票

## 📋 生成报告
- `智能选股分析报告.html` - 选股分析报告
- `股票行业整合报告.html` - 行业分析报告
- `选股结果.json` - 详细选股数据
- `推荐股票汇总.csv` - 推荐股票表格

## ⚠️ 风险提示
股市有风险，投资需谨慎。本系统仅供参考，不构成投资建议。
"""
    
    try:
        with open('README.md', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"\n📝 已创建 README.md 文件")
    except Exception as e:
        print(f"❌ 创建README失败: {str(e)}")

def main():
    """主函数"""
    quick_cleanup()
    create_readme()
    
    print(f"\n🏁 快速清理完成！")
    print(f"💡 项目目录已整理，保留了核心功能文件")

if __name__ == "__main__":
    main()

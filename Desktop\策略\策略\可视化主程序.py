#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化主程序 - 优化版
去掉确认对话框，直接执行功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import sys
import os
import threading
import webbrowser
import csv
import json
from datetime import datetime
import time
from 行业板块分类 import IndustryClassifier

class StockVisualizationSystem:
    def __init__(self, root):
        self.root = root
        self.industry_classifier = IndustryClassifier()
        self.setup_window()
        self.create_widgets()
        self.load_initial_data()
        self.start_auto_refresh()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("🚀 股票数据采集系统 - 可视化主程序")
        self.root.geometry("1400x900")
        self.root.resizable(True, True)
        self.root.configure(bg="#f0f0f0")
        
        # 窗口居中
        self.center_window()
        
        # 设置样式
        self.setup_styles()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 1400
        height = 900
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_styles(self):
        """设置样式"""
        self.style = ttk.Style()
        
        # 配置样式
        self.style.configure("Title.TLabel",
                           font=("Microsoft YaHei", 18, "bold"),
                           foreground="#2c3e50",
                           background="#f0f0f0")
        
        self.style.configure("Header.TLabel",
                           font=("Microsoft YaHei", 12, "bold"),
                           foreground="#34495e",
                           background="#ecf0f1")
        
        self.style.configure("Action.TButton",
                           font=("Microsoft YaHei", 10, "bold"),
                           padding=(15, 8))
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = tk.Frame(self.root, bg="#f0f0f0")
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 顶部标题区域
        self.create_header(main_container)

        # 主要内容区域
        content_frame = tk.Frame(main_container, bg="#f0f0f0")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))

        # 左侧系统信息面板
        self.create_system_info_panel(content_frame)

        # 中间功能菜单区域
        self.create_center_menu_panel(content_frame)

        # 右侧主显示区域
        self.create_main_display_area(content_frame)

        # 底部状态栏
        self.create_status_bar(main_container)
    
    def create_header(self, parent):
        """创建顶部标题区域"""
        header_frame = tk.Frame(parent, bg="#2c3e50", height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # 主标题
        title_label = tk.Label(header_frame,
                              text="🚀 股票数据采集系统 - 可视化主程序",
                              font=("Microsoft YaHei", 20, "bold"),
                              bg="#2c3e50",
                              fg="white")
        title_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # 系统信息
        info_frame = tk.Frame(header_frame, bg="#2c3e50")
        info_frame.pack(side=tk.RIGHT, padx=20, pady=20)
        
        self.time_label = tk.Label(info_frame,
                                  text=f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                                  font=("Microsoft YaHei", 10),
                                  bg="#2c3e50",
                                  fg="#bdc3c7")
        self.time_label.pack()
        
        self.status_label = tk.Label(info_frame,
                                    text="● 系统运行中",
                                    font=("Microsoft YaHei", 10, "bold"),
                                    bg="#2c3e50",
                                    fg="#27ae60")
        self.status_label.pack()
    
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_frame = tk.Frame(parent, bg="#ecf0f1", width=350)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 15))
        control_frame.pack_propagate(False)
        
        # 控制面板标题
        tk.Label(control_frame,
                text="🎮 功能控制面板",
                font=("Microsoft YaHei", 14, "bold"),
                bg="#ecf0f1",
                fg="#2c3e50").pack(pady=(20, 25))
        
        # 数据采集区域
        self.create_collection_section(control_frame)
        
        # 数据处理区域
        self.create_processing_section(control_frame)
        
        # 系统管理区域
        self.create_management_section(control_frame)
        
        # 系统状态显示
        self.create_system_status(control_frame)
    
    def create_collection_section(self, parent):
        """创建数据采集区域"""
        section_frame = tk.LabelFrame(parent,
                                     text="📊 数据采集",
                                     font=("Microsoft YaHei", 11, "bold"),
                                     bg="#ecf0f1",
                                     fg="#2c3e50",
                                     padx=10,
                                     pady=10)
        section_frame.pack(fill=tk.X, padx=20, pady=10)
        
        buttons = [
            ("🚀 后台数据采集", self.run_background_collection, "#3498db"),
            ("📊 获取股票名称", self.get_stock_names, "#9b59b6"),
            ("📈 获取涨跌幅数据", self.get_price_changes, "#e67e22")
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(section_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=10,
                           pady=6,
                           cursor="hand2",
                           width=25)
            btn.pack(pady=5, fill=tk.X)
    
    def create_processing_section(self, parent):
        """创建数据处理区域"""
        section_frame = tk.LabelFrame(parent,
                                     text="📄 数据处理",
                                     font=("Microsoft YaHei", 11, "bold"),
                                     bg="#ecf0f1",
                                     fg="#2c3e50",
                                     padx=10,
                                     pady=10)
        section_frame.pack(fill=tk.X, padx=20, pady=10)
        
        buttons = [
            ("📄 生成分析报告", self.generate_report, "#27ae60"),
            ("🎯 智能选股分析", self.start_stock_selection, "#e74c3c"),
            ("🏆 查看选股结果", self.show_selection_results, "#f39c12"),
            ("📰 消息面选股", self.start_news_selection, "#9b59b6"),
            ("🤖 AI智能选股", self.start_ai_selection, "#8e44ad"),
            ("🌐 启动Web可视化", self.start_web_visualization, "#3498db")
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(section_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=10,
                           pady=6,
                           cursor="hand2",
                           width=25)
            btn.pack(pady=5, fill=tk.X)
    
    def create_management_section(self, parent):
        """创建系统管理区域"""
        section_frame = tk.LabelFrame(parent,
                                     text="⚙️ 系统管理",
                                     font=("Microsoft YaHei", 11, "bold"),
                                     bg="#ecf0f1",
                                     fg="#2c3e50",
                                     padx=10,
                                     pady=10)
        section_frame.pack(fill=tk.X, padx=20, pady=10)
        
        buttons = [
            ("🔄 刷新数据显示", self.refresh_data, "#34495e"),
            ("⚡ 一键选股流程", self.one_click_selection, "#e67e22"),
            ("⚙️ 系统状态检查", self.check_system_status, "#95a5a6")
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(section_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=10,
                           pady=6,
                           cursor="hand2",
                           width=25)
            btn.pack(pady=5, fill=tk.X)
    
    def create_system_status(self, parent):
        """创建系统状态显示"""
        status_frame = tk.LabelFrame(parent,
                                    text="📊 系统状态",
                                    font=("Microsoft YaHei", 11, "bold"),
                                    bg="#ecf0f1",
                                    fg="#2c3e50",
                                    padx=10,
                                    pady=10)
        status_frame.pack(fill=tk.X, padx=20, pady=(10, 20))
        
        # 状态信息
        status_items = [
            ("Python版本:", "检查中..."),
            ("依赖包状态:", "检查中..."),
            ("核心文件:", "检查中..."),
            ("数据文件:", "检查中...")
        ]
        
        self.status_labels = {}
        for label_text, initial_value in status_items:
            frame = tk.Frame(status_frame, bg="#ecf0f1")
            frame.pack(fill=tk.X, pady=2)
            
            tk.Label(frame,
                    text=label_text,
                    font=("Microsoft YaHei", 9),
                    bg="#ecf0f1",
                    fg="#2c3e50").pack(side=tk.LEFT)
            
            value_label = tk.Label(frame,
                                  text=initial_value,
                                  font=("Microsoft YaHei", 9),
                                  bg="#ecf0f1",
                                  fg="#27ae60")
            value_label.pack(side=tk.RIGHT)
            
            self.status_labels[label_text] = value_label
    
    def create_data_display(self, parent):
        """创建右侧数据显示区域"""
        display_frame = tk.Frame(parent, bg="#ffffff")
        display_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 数据显示标题
        title_frame = tk.Frame(display_frame, bg="#34495e", height=40)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        tk.Label(title_frame,
                text="📊 实时股票数据显示",
                font=("Microsoft YaHei", 14, "bold"),
                bg="#34495e",
                fg="white").pack(side=tk.LEFT, padx=20, pady=10)
        
        # 清空按钮
        tk.Button(title_frame,
                 text="🗑️ 清空",
                 command=self.clear_display,
                 font=("Microsoft YaHei", 9),
                 bg="#e74c3c",
                 fg="white",
                 relief="raised",
                 bd=1,
                 padx=10,
                 pady=2,
                 cursor="hand2").pack(side=tk.RIGHT, padx=20, pady=8)
        
        # 标签页控件
        self.notebook = ttk.Notebook(display_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 创建标签页
        self.create_display_tabs()
    
    def create_display_tabs(self):
        """创建数据显示标签页"""
        # 双列数据显示标签页
        self.dual_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.dual_frame, text="📊 双列数据显示")
        
        # 创建双列布局 - 使用Grid确保等宽
        dual_container = tk.Frame(self.dual_frame, bg="white")
        dual_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 配置Grid权重，确保两列等宽
        dual_container.grid_columnconfigure(0, weight=1)
        dual_container.grid_columnconfigure(1, weight=1)
        dual_container.grid_rowconfigure(0, weight=1)
        
        # 人气榜列 - 左侧
        popularity_frame = tk.LabelFrame(dual_container,
                                        text="🔥 人气榜完整名单",
                                        font=("Microsoft YaHei", 12, "bold"),
                                        fg="#e74c3c")
        popularity_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        
        self.popularity_text = scrolledtext.ScrolledText(popularity_frame,
                                                        font=("Microsoft YaHei", 10),
                                                        wrap=tk.WORD,
                                                        bg="#f8f9fa",
                                                        fg="#2c3e50")
        self.popularity_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 飙升榜列 - 右侧
        soaring_frame = tk.LabelFrame(dual_container,
                                     text="🚀 飙升榜完整名单",
                                     font=("Microsoft YaHei", 12, "bold"),
                                     fg="#3498db")
        soaring_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        
        self.soaring_text = scrolledtext.ScrolledText(soaring_frame,
                                                     font=("Microsoft YaHei", 10),
                                                     wrap=tk.WORD,
                                                     bg="#f8f9fa",
                                                     fg="#2c3e50")
        self.soaring_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 操作日志标签页
        self.log_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.log_frame, text="📝 操作日志")
        
        self.log_text = scrolledtext.ScrolledText(self.log_frame,
                                                 font=("Consolas", 10),
                                                 wrap=tk.WORD,
                                                 bg="#f8f9fa",
                                                 fg="#2c3e50")
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 数据统计标签页
        self.stats_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.stats_frame, text="📈 数据统计")
        
        self.stats_text = scrolledtext.ScrolledText(self.stats_frame,
                                                   font=("Microsoft YaHei", 10),
                                                   wrap=tk.WORD,
                                                   bg="#f8f9fa",
                                                   fg="#2c3e50")
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_status_bar(self, parent):
        """创建底部状态栏"""
        status_frame = tk.Frame(parent, bg="#bdc3c7", height=35)
        status_frame.pack(fill=tk.X, pady=(15, 0))
        status_frame.pack_propagate(False)
        
        # 状态指示器
        self.main_status_label = tk.Label(status_frame,
                                         text="● 系统就绪",
                                         font=("Microsoft YaHei", 10, "bold"),
                                         bg="#bdc3c7",
                                         fg="#27ae60")
        self.main_status_label.pack(side=tk.LEFT, padx=20, pady=8)
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate', length=200)
        self.progress.pack(side=tk.LEFT, padx=20, pady=8)
        
        # 自动刷新状态
        self.auto_refresh_label = tk.Label(status_frame,
                                          text="🔄 自动刷新: 每30分钟",
                                          font=("Microsoft YaHei", 9),
                                          bg="#bdc3c7",
                                          fg="#2c3e50")
        self.auto_refresh_label.pack(side=tk.LEFT, padx=20, pady=8)
        
        # 退出按钮
        tk.Button(status_frame,
                 text="🚪 退出系统",
                 command=self.exit_application,
                 font=("Microsoft YaHei", 9),
                 bg="#e74c3c",
                 fg="white",
                 relief="raised",
                 bd=1,
                 padx=15,
                 pady=3,
                 cursor="hand2").pack(side=tk.RIGHT, padx=20, pady=5)
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 根据级别设置颜色
        if level == "ERROR":
            self.log_text.tag_add("error", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("error", foreground="#e74c3c")
        elif level == "SUCCESS":
            self.log_text.tag_add("success", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("success", foreground="#27ae60")
        elif level == "WARNING":
            self.log_text.tag_add("warning", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("warning", foreground="#f39c12")
    
    def show_data(self, title, data):
        """在统计标签页显示数据"""
        self.stats_text.insert(tk.END, f"\n{'='*60}\n")
        self.stats_text.insert(tk.END, f"📊 {title} - {datetime.now().strftime('%H:%M:%S')}\n")
        self.stats_text.insert(tk.END, f"{'='*60}\n")
        self.stats_text.insert(tk.END, f"{data}\n")
        self.stats_text.see(tk.END)
        
        # 切换到统计标签页
        self.notebook.select(self.stats_frame)
    
    def clear_display(self):
        """清空所有显示区域"""
        self.log_text.delete(1.0, tk.END)
        self.popularity_text.delete(1.0, tk.END)
        self.soaring_text.delete(1.0, tk.END)
        self.stats_text.delete(1.0, tk.END)
        self.log_message("所有显示区域已清空", "INFO")
    
    def set_status(self, message, color="#27ae60"):
        """设置主状态"""
        self.main_status_label.config(text=f"● {message}", fg=color)
        self.status_label.config(text=f"● {message}", fg=color)
        self.root.update()
    
    def start_progress(self):
        """开始进度条"""
        self.progress.start(10)
    
    def stop_progress(self):
        """停止进度条"""
        self.progress.stop()
    
    def load_initial_data(self):
        """加载初始数据"""
        self.log_message("🚀 可视化主程序启动完成", "SUCCESS")
        self.log_message("系统初始化完成，开始加载数据", "INFO")
        self.log_message("⏰ 自动刷新频率: 每30分钟更新一次数据", "INFO")
        self.log_message("💡 提示: 可手动点击'后台数据采集'获取最新数据", "INFO")

        # 检查系统状态
        self.check_system_status_silent()

        # 加载现有数据
        self.load_existing_data()
    
    def check_system_status_silent(self):
        """静默检查系统状态"""
        try:
            # Python版本
            python_version = f"{sys.version.split()[0]}"
            self.status_labels["Python版本:"].config(text=python_version, fg="#27ae60")
            
            # 检查依赖包
            required_modules = ["selenium", "requests", "matplotlib", "pandas", "numpy"]
            missing_modules = []
            
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError:
                    missing_modules.append(module)
            
            if missing_modules:
                self.status_labels["依赖包状态:"].config(text=f"缺少 {len(missing_modules)} 个", fg="#e74c3c")
            else:
                self.status_labels["依赖包状态:"].config(text="全部已安装", fg="#27ae60")
            
            # 检查核心文件
            core_files = [
                "后台采集.py", "股票名称获取工具.py", "股票涨跌幅获取工具.py",
                "智能数据分析报告.py", "数据服务器.py"
            ]
            
            missing_files = [f for f in core_files if not os.path.exists(f)]
            
            if missing_files:
                self.status_labels["核心文件:"].config(text=f"缺少 {len(missing_files)} 个", fg="#e74c3c")
            else:
                self.status_labels["核心文件:"].config(text="全部存在", fg="#27ae60")
            
            # 检查数据文件
            data_files = ["popularity.csv", "soaring.csv", "codes.txt"]
            existing_data_files = [f for f in data_files if os.path.exists(f)]
            
            if len(existing_data_files) == len(data_files):
                self.status_labels["数据文件:"].config(text="全部存在", fg="#27ae60")
            else:
                self.status_labels["数据文件:"].config(text=f"存在 {len(existing_data_files)}/{len(data_files)} 个", fg="#f39c12")
                
        except Exception as e:
            self.log_message(f"系统状态检查出错: {str(e)}", "ERROR")
    
    def load_existing_data(self):
        """加载现有数据文件"""
        try:
            # 加载人气榜数据
            self.load_csv_data("popularity.csv", "人气榜", self.popularity_text)
            
            # 加载飙升榜数据
            self.load_csv_data("soaring.csv", "飙升榜", self.soaring_text)
            
            # 生成数据统计
            self.generate_data_statistics()
            
        except Exception as e:
            self.log_message(f"加载数据时出错: {str(e)}", "ERROR")
    
    def load_csv_data(self, filename, title, text_widget):
        """加载CSV数据到指定的文本控件 - 增强版"""
        try:
            if os.path.exists(filename):
                # 获取文件修改时间
                file_mtime = os.path.getmtime(filename)
                file_time = datetime.fromtimestamp(file_mtime)

                with open(filename, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    data = list(reader)

                # 缓存数据到实例变量
                if title == "人气榜":
                    self.popularity_data = data
                elif title == "飙升榜":
                    self.soaring_data = data

                if data:
                    # 计算数据新鲜度
                    now = datetime.now()
                    time_diff = now - file_time
                    hours_ago = time_diff.total_seconds() / 3600

                    if hours_ago < 0.5:
                        freshness = "🟢 最新"
                    elif hours_ago < 1:
                        freshness = "🟡 较新"
                    elif hours_ago < 6:
                        freshness = "🟠 需更新"
                    else:
                        freshness = "🔴 过期"

                    content = f"📊 {title}数据 (共 {len(data)} 只股票) {freshness}\n"
                    content += f"📅 文件更新: {file_time.strftime('%Y-%m-%d %H:%M:%S')} ({hours_ago:.1f}小时前)\n"
                    content += f"🔄 刷新时间: {now.strftime('%Y-%m-%d %H:%M:%S')}\n"
                    content += "=" * 60 + "\n\n"

                    # 统计涨跌情况
                    up_count = 0
                    down_count = 0
                    flat_count = 0

                    for i, stock in enumerate(data, 1):
                        code = stock.get('code', 'N/A')
                        name = stock.get('name', '未知')
                        change = stock.get('change', 'N/A')
                        price = stock.get('price', '')  # 获取价格信息

                        # 统计涨跌
                        if change.startswith('+'):
                            change_display = f"📈 {change}"
                            up_count += 1
                        elif change.startswith('-'):
                            change_display = f"📉 {change}"
                            down_count += 1
                        else:
                            change_display = f"📊 {change}"
                            flat_count += 1

                        # 格式化价格显示
                        if price and price != '':
                            try:
                                price_val = float(price)
                                price_display = f"💰{price_val:.2f}"
                            except:
                                price_display = f"💰{price}"
                        else:
                            price_display = "💰--"

                        # 获取行业信息
                        industry = self.get_stock_sector(code, name)

                        # 显示格式：序号. 代码 名称 价格 涨跌幅 [行业]
                        content += f"{i:3d}. {code} {name:<10} {price_display:<8} {change_display:<12} [{industry}]\n"

                    # 添加统计信息
                    content += f"\n📊 涨跌统计: 上涨 {up_count} 只 | 下跌 {down_count} 只 | 平盘 {flat_count} 只\n"

                    text_widget.delete(1.0, tk.END)
                    text_widget.insert(1.0, content)

                    self.log_message(f"✅ {title}数据刷新完成: {len(data)} 只股票 (上涨{up_count}, 下跌{down_count})", "SUCCESS")
                else:
                    text_widget.delete(1.0, tk.END)
                    text_widget.insert(1.0, f"📭 {title}数据文件为空\n请先进行数据采集\n\n🔄 刷新时间: {datetime.now().strftime('%H:%M:%S')}")
                    self.log_message(f"⚠️ {title}数据文件为空", "WARNING")
            else:
                text_widget.delete(1.0, tk.END)
                text_widget.insert(1.0, f"❌ {title}数据文件不存在\n文件: {filename}\n请先进行数据采集\n\n🔄 刷新时间: {datetime.now().strftime('%H:%M:%S')}")
                self.log_message(f"❌ {title}数据文件不存在: {filename}", "ERROR")

        except Exception as e:
            text_widget.delete(1.0, tk.END)
            text_widget.insert(1.0, f"❌ 加载{title}数据失败\n错误: {str(e)}\n\n🔄 刷新时间: {datetime.now().strftime('%H:%M:%S')}")
            self.log_message(f"❌ 加载{title}数据失败: {str(e)}", "ERROR")
    
    def generate_data_statistics(self):
        """生成数据统计"""
        try:
            stats_content = "📊 股票数据统计分析\n"
            stats_content += f"📅 统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            stats_content += "=" * 60 + "\n\n"
            
            # 统计各个数据文件
            data_files = [
                ("popularity.csv", "人气榜数据"),
                ("soaring.csv", "飙升榜数据"),
                ("codes.txt", "股票代码库"),
                ("stock_names_cache.json", "股票名称缓存"),
                ("股票分析报告.html", "分析报告")
            ]
            
            total_records = 0
            
            for filename, description in data_files:
                if os.path.exists(filename):
                    size = os.path.getsize(filename)
                    mod_time = datetime.fromtimestamp(os.path.getmtime(filename))
                    
                    if filename.endswith('.csv'):
                        try:
                            with open(filename, 'r', encoding='utf-8') as f:
                                reader = csv.reader(f)
                                rows = list(reader)
                                record_count = len(rows) - 1 if len(rows) > 1 else 0
                                total_records += record_count
                                stats_content += f"✅ {description}: {record_count} 条记录 ({size/1024:.1f} KB)\n"
                        except:
                            stats_content += f"⚠️ {description}: 读取失败 ({size/1024:.1f} KB)\n"
                    elif filename.endswith('.txt'):
                        try:
                            with open(filename, 'r', encoding='utf-8') as f:
                                lines = [line.strip() for line in f if line.strip()]
                                stats_content += f"✅ {description}: {len(lines)} 条记录 ({size/1024:.1f} KB)\n"
                        except:
                            stats_content += f"⚠️ {description}: 读取失败 ({size/1024:.1f} KB)\n"
                    else:
                        stats_content += f"✅ {description}: {size/1024:.1f} KB\n"
                    
                    stats_content += f"   📅 更新时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                else:
                    stats_content += f"❌ {description}: 文件不存在\n\n"
            
            stats_content += f"📊 数据总览:\n"
            stats_content += f"   总记录数: {total_records} 条\n"
            stats_content += f"   系统状态: 正常运行\n"
            stats_content += f"   Python版本: {sys.version.split()[0]}\n"
            
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_content)
            
        except Exception as e:
            self.log_message(f"生成统计数据失败: {str(e)}", "ERROR")
    
    def start_auto_refresh(self):
        """启动自动刷新"""
        def auto_refresh():
            while True:
                try:
                    time.sleep(1800)  # 每30分钟（1800秒）刷新一次
                    self.root.after(0, self.refresh_data_silent)
                except:
                    break

        refresh_thread = threading.Thread(target=auto_refresh, daemon=True)
        refresh_thread.start()
        
        # 更新时间显示
        def update_time():
            self.time_label.config(text=f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.root.after(1000, update_time)
        
        update_time()
    
    def get_stock_sector(self, code, name=""):
        """获取股票所属行业"""
        try:
            return self.industry_classifier.get_industry(code, name)
        except:
            return "其他"

    def refresh_data_silent(self):
        """静默刷新数据"""
        try:
            self.load_existing_data()
            self.log_message("🔄 数据自动刷新完成 (每30分钟)", "INFO")
        except Exception as e:
            self.log_message(f"自动刷新失败: {str(e)}", "ERROR")
    
    def run_script_async(self, script_name, description):
        """异步运行脚本"""
        def run():
            self.set_status("运行中...", "#f39c12")
            self.start_progress()
            self.log_message(f"🚀 开始执行: {description}", "INFO")
            
            try:
                if not os.path.exists(script_name):
                    raise FileNotFoundError(f"脚本文件不存在: {script_name}")
                
                # 运行脚本
                process = subprocess.Popen([sys.executable, script_name], cwd=os.getcwd())
                
                self.log_message(f"✅ {description} 已启动", "SUCCESS")
                
                # 等待一段时间后重新加载数据
                self.root.after(5000, self.load_existing_data)
                
            except Exception as e:
                error_msg = f"{description} 执行出错: {str(e)}"
                self.log_message(error_msg, "ERROR")
                messagebox.showerror("执行错误", error_msg)
            
            finally:
                self.stop_progress()
                self.set_status("就绪", "#27ae60")
        
        thread = threading.Thread(target=run, daemon=True)
        thread.start()
    
    # 功能按钮回调函数 - 保留确认对话框的功能
    def run_background_collection(self):
        """后台数据采集"""
        if messagebox.askyesno("确认操作", "开始后台数据采集？\n这将启动浏览器采集股票数据。"):
            self.run_script_async("后台采集.py", "后台数据采集")
    
    def get_stock_names(self):
        """获取股票名称"""
        if messagebox.askyesno("确认操作", "开始获取股票名称？\n这将从网络获取真实股票名称。"):
            self.run_script_async("股票名称获取工具.py", "股票名称获取")
    
    def get_price_changes(self):
        """获取涨跌幅数据"""
        if messagebox.askyesno("确认操作", "开始获取涨跌幅数据？\n这将从网络获取实时涨跌幅。"):
            self.run_script_async("股票涨跌幅获取工具.py", "涨跌幅数据获取")
    
    # 去掉确认对话框的功能
    def generate_report(self):
        """生成分析报告 - 直接执行"""
        self.log_message("📄 开始生成智能分析报告", "INFO")
        
        def on_complete():
            if messagebox.askyesno("报告生成完成", "智能分析报告生成完成！\n是否在浏览器中打开查看？"):
                self.open_html_report()
        
        self.run_script_async("智能数据分析报告.py", "智能分析报告生成")
        self.root.after(10000, on_complete)
    
    def start_web_visualization(self):
        """启动Web可视化 - 直接执行"""
        self.log_message("🌐 开始启动Web可视化界面", "INFO")
        
        def start_and_open():
            self.set_status("启动Web服务...", "#f39c12")
            self.start_progress()
            
            try:
                # 检查服务器是否已经运行
                import requests
                try:
                    response = requests.get("http://localhost:8080/api/status", timeout=3)
                    server_running = response.status_code == 200
                    self.log_message("✅ 检测到数据服务器已在运行", "SUCCESS")
                except:
                    server_running = False
                    self.log_message("ℹ️ 数据服务器未运行，正在启动...", "INFO")
                
                # 如果服务器未运行，则启动它
                if not server_running:
                    if not os.path.exists("数据服务器.py"):
                        raise FileNotFoundError("数据服务器.py 文件不存在")
                    
                    subprocess.Popen([sys.executable, "数据服务器.py"], cwd=os.getcwd())
                    self.log_message("🌐 数据服务器已启动", "SUCCESS")
                    
                    # 等待服务器启动
                    import time
                    time.sleep(3)
                
                # 打开浏览器
                url = "http://localhost:8080/"
                webbrowser.open(url)
                
                self.log_message("🎉 Web可视化界面已启动", "SUCCESS")
                self.show_data("Web可视化启动成功", 
                             f"✅ Web可视化界面已成功启动\n\n"
                             f"🌐 访问地址: {url}\n"
                             f"📊 功能特点:\n"
                             f"  • 双列等宽显示人气榜和飙升榜\n"
                             f"  • 实时数据自动刷新\n"
                             f"  • 支持在线生成分析报告\n"
                             f"  • 响应式设计，支持各种屏幕\n\n"
                             f"💡 提示: 浏览器界面已自动打开，您可以：\n"
                             f"  1. 查看双列股票数据\n"
                             f"  2. 点击'生成报告'按钮创建分析报告\n"
                             f"  3. 使用自动刷新功能获取最新数据")
                
            except Exception as e:
                error_msg = f"启动Web可视化失败: {str(e)}"
                self.log_message(error_msg, "ERROR")
                messagebox.showerror("启动失败", 
                                   f"Web可视化启动失败:\n\n{str(e)}\n\n"
                                   f"请检查:\n"
                                   f"• 数据服务器.py 文件是否存在\n"
                                   f"• 端口8080是否被占用\n"
                                   f"• 网络连接是否正常")
            
            finally:
                self.stop_progress()
                self.set_status("就绪", "#27ae60")
        
        # 在新线程中执行
        thread = threading.Thread(target=start_and_open, daemon=True)
        thread.start()

    def start_ai_selection(self):
        """启动AI智能选股"""
        self.log_message("🤖 开始AI智能选股分析", "INFO")

        # 确认对话框
        if not messagebox.askyesno("确认操作",
                                 "开始AI智能选股分析？\n\n"
                                 "🤖 AI智能选股功能:\n"
                                 "• 综合分析热门股和消息面数据\n"
                                 "• 使用AI算法计算综合得分\n"
                                 "• 评估投资风险等级\n"
                                 "• 生成最终购买建议\n\n"
                                 "⏱️ 预计耗时: 10-30秒\n"
                                 "📊 需要先运行热门股和消息面选股"):
            return

        def run_ai_analysis():
            try:
                # 检查前置条件
                required_files = [
                    ('popularity.csv', '人气榜数据'),
                    ('soaring.csv', '飙升榜数据'),
                    ('消息面选股结果.json', '消息面选股结果')
                ]

                missing_files = []
                for filename, description in required_files:
                    if not os.path.exists(filename):
                        missing_files.append(f"• {description} ({filename})")

                if missing_files:
                    error_msg = "缺少必要的数据文件:\n\n" + "\n".join(missing_files) + "\n\n请先运行相应的选股功能"
                    messagebox.showerror("数据文件缺失", error_msg)
                    self.log_message("❌ AI选股分析失败: 缺少数据文件", "ERROR")
                    return

                # 检查AI分析脚本
                if not os.path.exists("AI智能选股分析.py"):
                    raise FileNotFoundError("AI智能选股分析.py 文件不存在")

                # 启动Web监控
                try:
                    from AI分析实时显示 import start_ai_web_monitor
                    web_server = start_ai_web_monitor()
                    self.log_message("🌐 AI分析Web监控已启动: http://localhost:8080", "INFO")

                    # 询问是否打开Web监控页面
                    if messagebox.askyesno("Web监控", "是否打开Web页面实时查看AI分析过程？"):
                        import webbrowser
                        webbrowser.open("http://localhost:8080")

                except Exception as e:
                    self.log_message(f"Web监控启动失败: {str(e)}", "WARNING")

                # 运行AI分析
                self.log_message("🤖 正在运行AI智能选股分析...", "INFO")
                result = subprocess.run([sys.executable, "AI智能选股分析.py"],
                                      cwd=os.getcwd())

                if result.returncode == 0:
                    self.log_message("✅ AI智能选股分析完成", "SUCCESS")

                    # 检查结果文件
                    result_files = []
                    if os.path.exists('AI智能选股分析结果.json'):
                        result_files.append('AI智能选股分析结果.json')
                    if os.path.exists('AI最终购买建议.csv'):
                        result_files.append('AI最终购买建议.csv')

                    if result_files:
                        # 自动生成AI报告
                        try:
                            self.log_message("📊 正在生成AI选股报告...", "INFO")
                            report_result = subprocess.run([sys.executable, "AI选股报告生成器.py"],
                                                         cwd=os.getcwd())
                            if report_result.returncode == 0:
                                self.log_message("✅ AI选股报告生成完成", "SUCCESS")
                        except:
                            pass

                        # 读取分析结果
                        try:
                            with open('AI智能选股分析结果.json', 'r', encoding='utf-8') as f:
                                ai_data = json.load(f)

                            final_rec = ai_data.get('final_recommendations', {})
                            strong_buy = len(final_rec.get('strong_buy', []))
                            buy = len(final_rec.get('buy', []))
                            consider = len(final_rec.get('consider', []))

                            self.show_data("AI智能选股完成",
                                         f"🤖 AI智能选股分析完成\n\n"
                                         f"📊 分析结果:\n"
                                         f"  🔥 强烈建议买入: {strong_buy} 只\n"
                                         f"  ⭐ 建议买入: {buy} 只\n"
                                         f"  👀 可以考虑: {consider} 只\n\n"
                                         f"🎯 AI分析方法:\n"
                                         f"  • 综合热门股和消息面数据\n"
                                         f"  • 多维度加权评分算法\n"
                                         f"  • 智能风险等级评估\n"
                                         f"  • 双重数据源验证加分\n\n"
                                         f"📄 生成文件:\n"
                                         f"  • AI智能选股分析结果.json\n"
                                         f"  • AI最终购买建议.csv\n"
                                         f"  • AI智能选股分析报告.html\n\n"
                                         f"💡 AI建议仅供参考，请理性投资\n"
                                         f"🎯 点击确定后可选择打开HTML报告")
                        except:
                            self.show_data("AI智能选股完成",
                                         f"🤖 AI智能选股分析完成\n\n"
                                         f"📄 已生成分析结果文件\n"
                                         f"💡 请查看生成的报告文件")

                        # 显示AI分析结果窗口
                        self.show_ai_analysis_results(ai_data)

                        # 询问是否打开web报告
                        if messagebox.askyesno("打开Web报告", "是否在浏览器中查看详细的AI分析报告？"):
                            try:
                                import webbrowser
                                report_path = os.path.abspath("AI智能选股分析报告.html")
                                webbrowser.open(f"file:///{report_path}")
                                self.log_message("📋 已打开AI智能选股分析报告", "INFO")
                            except Exception as e:
                                self.log_message(f"打开报告失败: {str(e)}", "WARNING")
                    else:
                        self.log_message("⚠️ 未生成结果文件", "WARNING")
                else:
                    raise Exception(f"AI智能选股执行失败，返回码: {result.returncode}")

            except Exception as e:
                error_msg = f"AI智能选股失败: {str(e)}"
                self.log_message(error_msg, "ERROR")
                messagebox.showerror("分析失败",
                                   f"AI智能选股分析失败:\n\n{str(e)}\n\n"
                                   f"可能原因:\n"
                                   f"• 缺少必要的数据文件\n"
                                   f"• 数据文件格式错误\n"
                                   f"• AI分析脚本文件缺失\n"
                                   f"• 系统资源不足\n\n"
                                   f"建议先运行热门股和消息面选股")

        # 在新线程中执行
        thread = threading.Thread(target=run_ai_analysis, daemon=True)
        thread.start()

    def show_ai_analysis_results(self, ai_data):
        """显示AI分析结果窗口"""
        try:
            # 创建新窗口
            result_window = tk.Toplevel(self.root)
            result_window.title("🤖 AI智能选股分析结果")
            result_window.geometry("1000x700")
            result_window.configure(bg='#f0f0f0')

            # 设置窗口图标和属性
            result_window.resizable(True, True)
            result_window.grab_set()  # 模态窗口

            # 创建主框架
            main_frame = ttk.Frame(result_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 标题
            title_label = tk.Label(main_frame,
                                 text="🤖 AI智能选股分析结果",
                                 font=("Microsoft YaHei", 16, "bold"),
                                 bg='#f0f0f0', fg='#2c3e50')
            title_label.pack(pady=(0, 10))

            # 创建笔记本控件（标签页）
            notebook = ttk.Notebook(main_frame)
            notebook.pack(fill=tk.BOTH, expand=True)

            # 获取数据
            final_rec = ai_data.get('final_recommendations', {})
            data_sources = ai_data.get('data_sources', {})
            complete_analysis = ai_data.get('complete_analysis', [])

            # 1. 概览标签页
            self.create_overview_tab(notebook, final_rec, data_sources)

            # 2. 强烈推荐标签页
            strong_buy = final_rec.get('strong_buy', [])
            if strong_buy:
                self.create_recommendation_tab(notebook, "🔥 强烈建议买入", strong_buy, "#e74c3c")

            # 3. 建议买入标签页
            buy = final_rec.get('buy', [])
            if buy:
                self.create_recommendation_tab(notebook, "⭐ 建议买入", buy, "#f39c12")

            # 4. 可以考虑标签页
            consider = final_rec.get('consider', [])
            if consider:
                self.create_recommendation_tab(notebook, "👀 可以考虑", consider, "#3498db")

            # 5. 完整分析标签页
            self.create_complete_analysis_tab(notebook, complete_analysis)

            # 底部按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            # 导出按钮
            export_btn = ttk.Button(button_frame,
                                  text="📊 导出CSV",
                                  command=lambda: self.export_ai_results(ai_data))
            export_btn.pack(side=tk.LEFT, padx=(0, 10))

            # 打开Web报告按钮
            web_btn = ttk.Button(button_frame,
                               text="🌐 查看Web报告",
                               command=self.open_ai_web_report)
            web_btn.pack(side=tk.LEFT, padx=(0, 10))

            # 关闭按钮
            close_btn = ttk.Button(button_frame,
                                 text="❌ 关闭",
                                 command=result_window.destroy)
            close_btn.pack(side=tk.RIGHT)

            # 居中显示窗口
            result_window.transient(self.root)
            result_window.update_idletasks()
            x = (result_window.winfo_screenwidth() // 2) - (result_window.winfo_width() // 2)
            y = (result_window.winfo_screenheight() // 2) - (result_window.winfo_height() // 2)
            result_window.geometry(f"+{x}+{y}")

        except Exception as e:
            self.log_message(f"显示AI分析结果失败: {str(e)}", "ERROR")
            messagebox.showerror("显示错误", f"显示AI分析结果失败:\n{str(e)}")

    def create_overview_tab(self, notebook, final_rec, data_sources):
        """创建概览标签页"""
        overview_frame = ttk.Frame(notebook)
        notebook.add(overview_frame, text="📊 分析概览")

        # 创建滚动框架
        canvas = tk.Canvas(overview_frame, bg='white')
        scrollbar = ttk.Scrollbar(overview_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 数据统计
        stats_frame = ttk.LabelFrame(scrollable_frame, text="📊 数据统计", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=5)

        stats_text = f"""
热门股数据: {data_sources.get('hot_stocks_count', 0)} 只
消息面数据: {data_sources.get('news_stocks_count', 0)} 只
合并分析: {data_sources.get('combined_count', 0)} 只
高分股票: {final_rec.get('high_score_count', 0)} 只
        """

        stats_label = tk.Label(stats_frame, text=stats_text.strip(),
                             font=("Microsoft YaHei", 10),
                             justify=tk.LEFT, bg='white')
        stats_label.pack(anchor=tk.W)

        # 推荐分布
        rec_frame = ttk.LabelFrame(scrollable_frame, text="🎯 推荐分布", padding=10)
        rec_frame.pack(fill=tk.X, padx=10, pady=5)

        strong_buy_count = len(final_rec.get('strong_buy', []))
        buy_count = len(final_rec.get('buy', []))
        consider_count = len(final_rec.get('consider', []))

        rec_text = f"""
🔥 强烈建议买入: {strong_buy_count} 只
⭐ 建议买入: {buy_count} 只
👀 可以考虑: {consider_count} 只
📊 总推荐股票: {strong_buy_count + buy_count + consider_count} 只
        """

        rec_label = tk.Label(rec_frame, text=rec_text.strip(),
                           font=("Microsoft YaHei", 10),
                           justify=tk.LEFT, bg='white')
        rec_label.pack(anchor=tk.W)

        # AI分析说明
        info_frame = ttk.LabelFrame(scrollable_frame, text="🤖 AI分析方法", padding=10)
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        info_text = """
• 综合评分算法: 热门股得分(40%) + 消息面得分(35%) + 数据源加分(25%)
• 风险评估: 基于AI得分和数据完整性的5级风险评估
• 投资建议: 85+强烈推荐, 75+建议买入, 65+可以考虑
• 双重验证: 同时出现在热门股和消息面的股票获得额外加分
• 智能过滤: 自动过滤低质量和高风险股票
        """

        info_label = tk.Label(info_frame, text=info_text.strip(),
                            font=("Microsoft YaHei", 9),
                            justify=tk.LEFT, bg='white')
        info_label.pack(anchor=tk.W)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_recommendation_tab(self, notebook, title, stocks, color):
        """创建推荐标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text=f"{title} ({len(stocks)})")

        # 创建树形视图
        columns = ("排名", "代码", "名称", "AI得分", "风险等级", "数据来源", "投资建议")
        tree = ttk.Treeview(frame, columns=columns, show="headings", height=15)

        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100, anchor=tk.CENTER)

        # 调整列宽
        tree.column("排名", width=50)
        tree.column("代码", width=80)
        tree.column("名称", width=120)
        tree.column("AI得分", width=80)
        tree.column("风险等级", width=100)
        tree.column("数据来源", width=120)
        tree.column("投资建议", width=120)

        # 添加数据
        for i, stock in enumerate(stocks, 1):
            sources = self.get_stock_sources(stock)
            tree.insert("", "end", values=(
                i,
                stock['code'],
                stock['name'],
                f"{stock['ai_score']:.2f}",
                stock['risk_level'],
                sources,
                stock['investment_advice']
            ))

        # 添加滚动条
        scrollbar_tree = ttk.Scrollbar(frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar_tree.set)

        # 布局
        tree.pack(side="left", fill="both", expand=True)
        scrollbar_tree.pack(side="right", fill="y")

        # 详情框架
        detail_frame = ttk.Frame(frame)
        detail_frame.pack(side="bottom", fill="x", padx=5, pady=5)

        detail_text = tk.Text(detail_frame, height=4, wrap=tk.WORD)
        detail_text.pack(fill="x")

        def on_select(event):
            selection = tree.selection()
            if selection:
                item = tree.item(selection[0])
                values = item['values']
                if values:
                    rank = values[0]
                    stock = stocks[int(rank) - 1]
                    detail_info = f"AI分析理由: {stock.get('ai_reason', '无')}\n"
                    detail_info += f"热门股得分: {stock.get('hot_score', 0)}\n"
                    detail_info += f"消息面得分: {stock.get('news_score', 0)}\n"
                    detail_info += f"提及次数: {stock.get('mention_count', 0)}"

                    detail_text.delete(1.0, tk.END)
                    detail_text.insert(1.0, detail_info)

        tree.bind("<<TreeviewSelect>>", on_select)

    def create_complete_analysis_tab(self, notebook, complete_analysis):
        """创建完整分析标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text=f"📋 完整分析 ({len(complete_analysis)})")

        # 创建树形视图
        columns = ("排名", "代码", "名称", "AI得分", "热门得分", "消息得分", "风险等级", "数据来源")
        tree = ttk.Treeview(frame, columns=columns, show="headings", height=20)

        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=90, anchor=tk.CENTER)

        # 添加数据
        for i, stock in enumerate(complete_analysis, 1):
            sources = self.get_stock_sources(stock)
            tree.insert("", "end", values=(
                i,
                stock['code'],
                stock['name'],
                f"{stock.get('ai_score', 0):.2f}",
                f"{stock.get('hot_score', 0):.1f}",
                f"{stock.get('news_score', 0):.2f}",
                stock.get('risk_level', '未知'),
                sources
            ))

        # 添加滚动条
        scrollbar_complete = ttk.Scrollbar(frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar_complete.set)

        # 布局
        tree.pack(side="left", fill="both", expand=True)
        scrollbar_complete.pack(side="right", fill="y")

    def get_stock_sources(self, stock):
        """获取股票数据来源"""
        sources = []
        if stock.get('has_hot_data'):
            hot_sources = stock.get('hot_sources', [])
            sources.extend(hot_sources)
        if stock.get('has_news_data'):
            sources.append('消息面')
        return '+'.join(sources) if sources else '未知'

    def export_ai_results(self, ai_data):
        """导出AI分析结果"""
        try:
            from tkinter import filedialog

            # 选择保存位置
            filename = filedialog.asksaveasfilename(
                title="导出AI分析结果",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )

            if filename:
                # 导出数据
                import csv
                with open(filename, 'w', encoding='utf-8-sig', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(['建议等级', '排名', '股票代码', '股票名称', 'AI得分',
                                   '热门得分', '消息得分', '风险等级', '数据来源', 'AI分析理由'])

                    final_rec = ai_data.get('final_recommendations', {})

                    # 强烈推荐
                    for i, stock in enumerate(final_rec.get('strong_buy', []), 1):
                        sources = self.get_stock_sources(stock)
                        writer.writerow([
                            '强烈建议买入', i, stock['code'], stock['name'],
                            stock['ai_score'], stock.get('hot_score', 0),
                            stock.get('news_score', 0), stock['risk_level'],
                            sources, stock['ai_reason']
                        ])

                    # 建议买入
                    for i, stock in enumerate(final_rec.get('buy', []), 1):
                        sources = self.get_stock_sources(stock)
                        writer.writerow([
                            '建议买入', i, stock['code'], stock['name'],
                            stock['ai_score'], stock.get('hot_score', 0),
                            stock.get('news_score', 0), stock['risk_level'],
                            sources, stock['ai_reason']
                        ])

                    # 可以考虑
                    for i, stock in enumerate(final_rec.get('consider', []), 1):
                        sources = self.get_stock_sources(stock)
                        writer.writerow([
                            '可以考虑', i, stock['code'], stock['name'],
                            stock['ai_score'], stock.get('hot_score', 0),
                            stock.get('news_score', 0), stock['risk_level'],
                            sources, stock['ai_reason']
                        ])

                messagebox.showinfo("导出成功", f"AI分析结果已导出到:\n{filename}")
                self.log_message(f"AI分析结果已导出: {filename}", "SUCCESS")

        except Exception as e:
            self.log_message(f"导出AI分析结果失败: {str(e)}", "ERROR")
            messagebox.showerror("导出失败", f"导出AI分析结果失败:\n{str(e)}")

    def open_ai_web_report(self):
        """打开AI分析Web报告"""
        try:
            import webbrowser
            report_path = os.path.abspath("AI智能选股分析报告.html")

            if os.path.exists(report_path):
                webbrowser.open(f"file:///{report_path}")
                self.log_message("📋 已打开AI智能选股Web报告", "INFO")
            else:
                messagebox.showwarning("文件不存在", "AI智能选股分析报告.html 文件不存在")

        except Exception as e:
            self.log_message(f"打开AI Web报告失败: {str(e)}", "ERROR")
            messagebox.showerror("打开失败", f"打开AI Web报告失败:\n{str(e)}")

    def start_news_selection(self):
        """启动消息面选股"""
        self.log_message("📰 开始消息面智能选股", "INFO")

        # 确认对话框
        if not messagebox.askyesno("确认操作",
                                 "开始消息面智能选股分析？\n\n"
                                 "📰 功能说明:\n"
                                 "• 爬取东方财富股吧热门话题\n"
                                 "• 自动点击'加载更多'获取更多消息\n"
                                 "• 识别话题中提及的股票\n"
                                 "• 计算市场情感得分\n"
                                 "• 生成基于消息面的选股推荐\n\n"
                                 "⏱️ 预计耗时: 2-5分钟\n"
                                 "🌐 需要网络连接和Chrome浏览器\n"
                                 "💡 如果网络失败会自动使用备用方案"):
            return

        def run_news_analysis():
            self.set_status("消息面分析中...", "#9b59b6")
            self.start_progress()

            try:
                # 获取脚本文件的绝对路径
                script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "消息面智能选股.py")
                
                # 检查脚本文件
                if not os.path.exists(script_path):
                    raise FileNotFoundError(f"消息面智能选股.py 文件不存在\n期望路径: {script_path}")

                # 运行消息面选股
                result = subprocess.run([sys.executable, script_path],
                                      cwd=os.path.dirname(script_path))

                if result.returncode == 0:
                    self.log_message("✅ 消息面选股分析完成", "SUCCESS")

                    # 检查结果文件
                    result_files = []
                    if os.path.exists('消息面选股结果.json'):
                        result_files.append('消息面选股结果.json')
                    if os.path.exists('消息面推荐股票.csv'):
                        result_files.append('消息面推荐股票.csv')

                    if result_files:
                        # 自动生成消息面报告
                        try:
                            report_result = subprocess.run([sys.executable, "消息面选股报告生成器.py"],
                                                         cwd=os.getcwd())
                            if report_result.returncode == 0:
                                self.log_message("✅ 消息面选股报告生成完成", "SUCCESS")
                        except:
                            pass

                        self.show_data("消息面选股完成",
                                     f"✅ 消息面智能选股分析完成\n\n"
                                     f"📊 分析方法:\n"
                                     f"  • 爬取东方财富股吧热门话题\n"
                                     f"  • 自动点击加载更多获取数据\n"
                                     f"  • 识别话题中的股票提及\n"
                                     f"  • 计算市场情感得分\n"
                                     f"  • 智能生成投资推荐\n\n"
                                     f"📄 生成文件:\n"
                                     f"  • 消息面选股结果.json\n"
                                     f"  • 消息面推荐股票.csv\n"
                                     f"  • 消息面选股分析报告.html\n\n"
                                     f"💡 建议结合技术面分析进行投资决策\n"
                                     f"🎯 点击确定后可选择打开HTML报告")

                        # 询问是否打开报告
                        if messagebox.askyesno("打开报告", "是否打开消息面选股分析报告？"):
                            try:
                                import webbrowser
                                report_path = os.path.abspath("消息面选股分析报告.html")
                                webbrowser.open(f"file:///{report_path}")
                                self.log_message("📋 已打开消息面选股分析报告", "INFO")
                            except Exception as e:
                                self.log_message(f"打开报告失败: {str(e)}", "WARNING")
                    else:
                        self.log_message("⚠️ 未生成结果文件", "WARNING")
                else:
                    raise Exception(f"消息面选股执行失败，返回码: {result.returncode}")

            except Exception as e:
                error_msg = f"消息面选股失败: {str(e)}"
                self.log_message(error_msg, "ERROR")
                messagebox.showerror("分析失败",
                                   f"消息面选股分析失败:\n\n{str(e)}\n\n"
                                   f"可能原因:\n"
                                   f"• 网络连接问题\n"
                                   f"• Chrome浏览器或驱动问题\n"
                                   f"• 目标网站访问限制\n"
                                   f"• 脚本文件缺失或损坏\n\n"
                                   f"建议:\n"
                                   f"• 检查网络连接\n"
                                   f"• 确保Chrome浏览器已安装\n"
                                   f"• 更新ChromeDriver到最新版本\n"
                                   f"• 稍后重试或使用其他选股方法")

            finally:
                self.stop_progress()
                self.set_status("就绪", "#27ae60")

        # 在新线程中执行
        thread = threading.Thread(target=run_news_analysis, daemon=True)
        thread.start()

    def open_html_report(self):
        """打开HTML报告"""
        try:
            report_file = "股票分析报告.html"
            
            # 检查文件是否存在
            if not os.path.exists(report_file):
                self.log_message("报告文件不存在，请先生成报告", "ERROR")
                messagebox.showerror("文件不存在", 
                                   f"找不到报告文件: {report_file}\n\n"
                                   f"请先点击'生成分析报告'按钮生成报告。")
                return
            
            # 获取绝对路径并转换为正确的URL格式
            report_path = os.path.abspath(report_file)
            
            # Windows路径处理
            if os.name == 'nt':  # Windows系统
                report_url = f"file:///{report_path.replace(os.sep, '/')}"
            else:
                report_url = f"file://{report_path}"
            
            # 打开浏览器
            webbrowser.open(report_url)
            
            self.log_message(f"📄 分析报告已在浏览器中打开: {report_file}", "SUCCESS")
            
        except Exception as e:
            error_msg = f"打开报告失败: {str(e)}"
            self.log_message(error_msg, "ERROR")
            messagebox.showerror("打开失败", f"无法打开HTML报告:\n{str(e)}\n\n请检查浏览器设置或手动打开文件。")
    
    def refresh_data(self):
        """刷新数据显示 - 增强版"""
        def refresh_task():
            try:
                # 开始刷新
                self.set_status("正在刷新数据...", "#f39c12")
                self.start_progress()
                self.log_message("🔄 开始手动刷新数据显示...", "INFO")

                # 清空当前显示
                self.popularity_text.delete(1.0, tk.END)
                self.soaring_text.delete(1.0, tk.END)

                # 显示加载提示
                self.popularity_text.insert(1.0, "🔄 正在刷新人气榜数据...\n请稍候...")
                self.soaring_text.insert(1.0, "🔄 正在刷新飙升榜数据...\n请稍候...")

                # 强制更新界面
                self.root.update()

                # 模拟加载时间，让用户看到刷新过程
                import time
                time.sleep(0.5)

                # 重新加载数据
                self.load_existing_data()

                # 更新系统状态
                self.check_system_status_silent()

                # 生成新的统计数据
                self.generate_data_statistics()

                # 完成刷新
                self.log_message("✅ 数据显示刷新完成", "SUCCESS")
                self.log_message(f"📊 刷新时间: {datetime.now().strftime('%H:%M:%S')}", "INFO")

                # 显示刷新结果统计
                pop_count = len(self.popularity_data) if hasattr(self, 'popularity_data') else 0
                soar_count = len(self.soaring_data) if hasattr(self, 'soaring_data') else 0
                self.log_message(f"📈 数据统计: 人气榜 {pop_count} 只, 飙升榜 {soar_count} 只", "SUCCESS")

                # 切换到双列数据显示标签页，让用户看到刷新结果
                self.notebook.select(self.dual_frame)

            except Exception as e:
                error_msg = f"刷新数据失败: {str(e)}"
                self.log_message(error_msg, "ERROR")
                messagebox.showerror("刷新失败", error_msg)

            finally:
                # 停止进度条和恢复状态
                self.stop_progress()
                self.set_status("就绪", "#27ae60")

        # 在新线程中执行刷新任务，避免阻塞界面
        refresh_thread = threading.Thread(target=refresh_task, daemon=True)
        refresh_thread.start()
    
    def check_system_status(self):
        """检查系统状态"""
        self.log_message("⚙️ 执行系统状态检查...", "INFO")
        
        # 更新状态显示
        self.check_system_status_silent()
        
        # 生成详细报告
        status_report = "⚙️ 系统状态详细报告\n\n"
        
        # Python信息
        status_report += f"🐍 Python版本: {sys.version}\n"
        status_report += f"📍 Python路径: {sys.executable}\n\n"
        
        # 依赖包检查
        required_modules = ["selenium", "requests", "matplotlib", "pandas", "numpy"]
        status_report += "📦 依赖包状态:\n"
        
        for module in required_modules:
            try:
                mod = __import__(module)
                version = getattr(mod, '__version__', '未知版本')
                status_report += f"  ✅ {module}: {version}\n"
            except ImportError:
                status_report += f"  ❌ {module}: 未安装\n"
        
        # 文件检查
        core_files = [
            "后台采集.py", "股票名称获取工具.py", "股票涨跌幅获取工具.py",
            "智能数据分析报告.py", "数据服务器.py"
        ]
        
        status_report += "\n📁 核心文件状态:\n"
        for file in core_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                status_report += f"  ✅ {file}: {size/1024:.1f} KB\n"
            else:
                status_report += f"  ❌ {file}: 不存在\n"
        
        self.show_data("系统状态检查", status_report)
        self.log_message("✅ 系统状态检查完成", "SUCCESS")

    def start_stock_selection(self):
        """启动智能选股分析"""
        def selection_task():
            try:
                self.set_status("正在进行智能选股分析...", "#e74c3c")
                self.start_progress()
                self.log_message("🎯 开始智能选股分析...", "INFO")

                # 检查数据文件
                if not os.path.exists('popularity.csv') or not os.path.exists('soaring.csv'):
                    self.log_message("❌ 缺少股票数据文件，请先进行数据采集", "ERROR")
                    messagebox.showwarning("数据不足", "请先进行股票数据采集，然后再进行选股分析")
                    return

                # 运行选股系统
                self.log_message("📊 正在执行多策略选股分析...", "INFO")
                result = subprocess.run([sys.executable, '智能选股系统.py'],
                                      cwd=os.getcwd())

                if result.returncode == 0:
                    self.log_message("✅ 智能选股分析完成", "SUCCESS")

                    # 生成选股报告
                    self.log_message("📋 正在生成选股报告...", "INFO")
                    report_result = subprocess.run([sys.executable, '选股报告生成器.py'],
                                                 cwd=os.getcwd())

                    if report_result.returncode == 0:
                        self.log_message("✅ 选股报告生成完成", "SUCCESS")

                        # 询问是否打开报告
                        if messagebox.askyesno("选股完成", "智能选股分析完成！\n是否打开选股报告？"):
                            self.open_selection_report()
                    else:
                        self.log_message("❌ 选股报告生成失败", "ERROR")

                else:
                    self.log_message("❌ 智能选股分析失败", "ERROR")
                    messagebox.showerror("选股失败", "智能选股分析失败，请检查数据文件")

            except Exception as e:
                error_msg = f"选股分析出错: {str(e)}"
                self.log_message(error_msg, "ERROR")
                messagebox.showerror("选股出错", error_msg)
            finally:
                self.stop_progress()
                self.set_status("就绪", "#27ae60")

        # 在后台线程中执行
        threading.Thread(target=selection_task, daemon=True).start()

    def show_selection_results(self):
        """显示选股结果"""
        try:
            if not os.path.exists('选股结果.json'):
                messagebox.showinfo("提示", "没有找到选股结果文件\n请先运行智能选股分析")
                return

            # 读取选股结果
            import json
            with open('选股结果.json', 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 构建显示内容
            content = f"🎯 智能选股结果摘要\n"
            content += f"{'='*50}\n\n"
            content += f"📅 分析时间: {data.get('timestamp', '未知')}\n"
            content += f"📊 分析股票总数: {data.get('total_stocks', 0)} 只\n"
            content += f"🎯 选股策略数量: {len(data.get('strategies', {}))} 个\n\n"

            # 各策略推荐数量
            strategy_names = {
                'momentum': '🚀 动量选股',
                'value': '💎 价值选股',
                'growth': '🌱 成长选股',
                'balanced': '⚖️ 均衡选股',
                'hot_sector': '🔥 热门板块选股',
                'risk_control': '🛡️ 风险控制选股'
            }

            content += "📋 各策略推荐数量:\n"
            for strategy, stocks in data.get('strategies', {}).items():
                name = strategy_names.get(strategy, strategy)
                content += f"  {name}: {len(stocks)} 只\n"

            # 计算共识推荐
            stock_votes = {}
            for strategy, stocks in data.get('strategies', {}).items():
                for stock in stocks:
                    code = stock['code']
                    if code not in stock_votes:
                        stock_votes[code] = {'stock': stock, 'votes': 0}
                    stock_votes[code]['votes'] += 1

            consensus_picks = [item for item in stock_votes.values() if item['votes'] >= 2]
            consensus_picks.sort(key=lambda x: x['votes'], reverse=True)

            content += f"\n🏆 多策略共识推荐: {len(consensus_picks)} 只\n"
            if consensus_picks:
                content += "前10只共识股票:\n"
                for i, item in enumerate(consensus_picks[:10], 1):
                    stock = item['stock']
                    votes = item['votes']
                    content += f"  {i:2d}. {stock['code']} {stock['name']:<10} "
                    content += f"{stock['change']:<8} [{stock['industry']}] (推荐:{votes}次)\n"

            content += f"\n💡 投资建议:\n"
            content += f"  • 重点关注多策略共同推荐的股票\n"
            content += f"  • 根据个人风险偏好选择合适策略\n"
            content += f"  • 建议分散投资，控制单只股票仓位\n"
            content += f"  • 定期重新分析，及时调整投资组合\n"

            self.show_data("智能选股结果", content)
            self.log_message("✅ 选股结果显示完成", "SUCCESS")

        except Exception as e:
            error_msg = f"显示选股结果失败: {str(e)}"
            self.log_message(error_msg, "ERROR")
            messagebox.showerror("显示失败", error_msg)

    def one_click_selection(self):
        """一键选股流程"""
        def one_click_task():
            try:
                self.set_status("正在执行一键选股流程...", "#e67e22")
                self.start_progress()
                self.log_message("⚡ 开始一键选股流程...", "INFO")

                # 步骤1: 检查数据文件
                self.log_message("📋 步骤1: 检查数据文件", "INFO")
                if not os.path.exists('popularity.csv') or not os.path.exists('soaring.csv'):
                    self.log_message("⚠️ 数据文件不存在，开始数据采集...", "WARNING")

                    # 运行数据采集
                    result = subprocess.run([sys.executable, '后台采集.py'],
                                          capture_output=True, text=True, encoding='gbk', errors='ignore')
                    if result.returncode != 0:
                        self.log_message("❌ 数据采集失败", "ERROR")
                        messagebox.showerror("流程失败", "数据采集失败，无法继续")
                        return

                    self.log_message("✅ 数据采集完成", "SUCCESS")

                # 步骤2: 智能选股分析
                self.log_message("📋 步骤2: 智能选股分析", "INFO")
                result = subprocess.run([sys.executable, '智能选股系统.py'],
                                      cwd=os.getcwd())
                if result.returncode != 0:
                    self.log_message("❌ 选股分析失败", "ERROR")
                    messagebox.showerror("流程失败", "智能选股分析失败")
                    return

                self.log_message("✅ 智能选股分析完成", "SUCCESS")

                # 步骤3: 生成选股报告
                self.log_message("📋 步骤3: 生成选股报告", "INFO")
                result = subprocess.run([sys.executable, '选股报告生成器.py'],
                                      cwd=os.getcwd())
                if result.returncode == 0:
                    self.log_message("✅ 选股报告生成完成", "SUCCESS")

                # 步骤4: 生成行业整合报告
                self.log_message("📋 步骤4: 生成行业整合报告", "INFO")
                result = subprocess.run([sys.executable, '增强数据显示.py'],
                                      cwd=os.getcwd())
                if result.returncode == 0:
                    self.log_message("✅ 行业整合报告生成完成", "SUCCESS")

                # 刷新数据显示
                self.load_existing_data()

                self.log_message("🎉 一键选股流程完成！", "SUCCESS")

                # 询问是否查看结果
                if messagebox.askyesno("流程完成", "一键选股流程完成！\n是否查看选股结果？"):
                    self.show_selection_results()

                # 询问是否打开报告
                if messagebox.askyesno("打开报告", "是否打开HTML选股报告？"):
                    self.open_selection_report()

            except Exception as e:
                error_msg = f"一键选股流程出错: {str(e)}"
                self.log_message(error_msg, "ERROR")
                messagebox.showerror("流程出错", error_msg)
            finally:
                self.stop_progress()
                self.set_status("就绪", "#27ae60")

        # 在后台线程中执行
        threading.Thread(target=one_click_task, daemon=True).start()

    def open_selection_report(self):
        """打开选股报告"""
        try:
            report_file = '智能选股分析报告.html'
            if not os.path.exists(report_file):
                messagebox.showinfo("提示", "选股报告文件不存在\n请先运行智能选股分析")
                return

            # 获取文件绝对路径
            report_path = os.path.abspath(report_file)

            # 构建文件URL
            if os.name == 'nt':  # Windows系统
                report_url = f"file:///{report_path.replace(os.sep, '/')}"
            else:
                report_url = f"file://{report_path}"

            # 打开浏览器
            webbrowser.open(report_url)
            self.log_message(f"🌐 已打开选股报告: {report_file}", "SUCCESS")

        except Exception as e:
            error_msg = f"打开选股报告失败: {str(e)}"
            self.log_message(error_msg, "ERROR")
            messagebox.showerror("打开失败", f"无法打开选股报告:\n{str(e)}")

    def exit_application(self):
        """退出应用程序"""
        if messagebox.askyesno("确认退出", "确定要退出股票数据采集系统吗？"):
            self.log_message("🚪 正在退出系统...", "INFO")
            self.root.quit()
            self.root.destroy()

def main():
    """主函数"""
    root = tk.Tk()
    app = StockVisualizationSystem(root)
    root.mainloop()

if __name__ == "__main__":
    main()

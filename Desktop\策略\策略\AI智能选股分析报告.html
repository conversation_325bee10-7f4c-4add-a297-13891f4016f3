
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能选股分析报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #3498db;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-top: 4px solid #3498db;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .recommendation-section {
            margin-bottom: 30px;
        }
        
        .recommendation-header {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px 10px 0 0;
            font-size: 1.3em;
            font-weight: bold;
        }
        
        .stock-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            padding: 20px;
            background: white;
            border-radius: 0 0 10px 10px;
        }
        
        .stock-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stock-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .stock-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .stock-code {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }
        
        .ai-score {
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .stock-details {
            font-size: 0.9em;
            color: #7f8c8d;
        }
        
        .detail-item {
            margin-bottom: 5px;
        }
        
        .risk-low { border-left-color: #27ae60; }
        .risk-medium-low { border-left-color: #f39c12; }
        .risk-medium { border-left-color: #e67e22; }
        .risk-medium-high { border-left-color: #e74c3c; }
        .risk-high { border-left-color: #c0392b; }
        
        .footer {
            background: #34495e;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 0.9em;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .stock-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI智能选股分析报告</h1>
            <p>基于热门股和消息面的综合AI分析 | 生成时间: 2025-07-30 21:37:50</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📊 数据概览</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">200</div>
                        <div class="stat-label">热门股数据</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">34</div>
                        <div class="stat-label">消息面股票</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">110</div>
                        <div class="stat-label">合并分析</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">18</div>
                        <div class="stat-label">高分股票</div>
                    </div>
                </div>
            </div>
        
            <div class="section">
                <div class="recommendation-section">
                    <div class="recommendation-header" style="background: #f39c12;">
                        ⭐ 建议买入 (6 只)
                    </div>
                    <div class="stock-grid">
                
                        <div class="stock-card risk-low">
                            <div class="stock-header">
                                <div class="stock-code">#1 001221 悍高</div>
                                <div class="ai-score">AI: 83.25</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 低风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分优秀；消息面关注</div>
                                <div class="detail-item">💡 投资建议: ⭐ 建议买入</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-low">
                            <div class="stock-header">
                                <div class="stock-code">#2 600010 包钢股份</div>
                                <div class="ai-score">AI: 80.36</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 低风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分优秀；消息面关注</div>
                                <div class="detail-item">💡 投资建议: ⭐ 建议买入</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium-low">
                            <div class="stock-header">
                                <div class="stock-code">#3 002570 贝因美</div>
                                <div class="ai-score">AI: 77.39</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中低风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分良好；消息面谨慎</div>
                                <div class="detail-item">💡 投资建议: ⭐ 建议买入</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium-low">
                            <div class="stock-header">
                                <div class="stock-code">#4 600117 西宁特钢</div>
                                <div class="ai-score">AI: 77.39</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中低风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分良好；消息面谨慎</div>
                                <div class="detail-item">💡 投资建议: ⭐ 建议买入</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium-low">
                            <div class="stock-header">
                                <div class="stock-code">#5 600326 西藏天路</div>
                                <div class="ai-score">AI: 76.36</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中低风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分良好；消息面关注</div>
                                <div class="detail-item">💡 投资建议: ⭐ 建议买入</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium-low">
                            <div class="stock-header">
                                <div class="stock-code">#6 002097 山河智能</div>
                                <div class="ai-score">AI: 76.36</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中低风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分良好；消息面观望</div>
                                <div class="detail-item">💡 投资建议: ⭐ 建议买入</div>
                            </div>
                        </div>
                    
                    </div>
                </div>
            </div>
                
            <div class="section">
                <div class="recommendation-section">
                    <div class="recommendation-header" style="background: #3498db;">
                        👀 可以考虑 (10 只)
                    </div>
                    <div class="stock-grid">
                
                        <div class="stock-card risk-medium-low">
                            <div class="stock-header">
                                <div class="stock-code">#1 600111 沪市600111</div>
                                <div class="ai-score">AI: 74.14</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中低风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分良好；消息面关注</div>
                                <div class="detail-item">💡 投资建议: 👀 可以考虑</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium-low">
                            <div class="stock-header">
                                <div class="stock-code">#2 600895 沪市600895</div>
                                <div class="ai-score">AI: 71.25</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中低风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分良好；消息面关注</div>
                                <div class="detail-item">💡 投资建议: 👀 可以考虑</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium-low">
                            <div class="stock-header">
                                <div class="stock-code">#3 002265 深市002265</div>
                                <div class="ai-score">AI: 71.25</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中低风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分良好；消息面关注</div>
                                <div class="detail-item">💡 投资建议: 👀 可以考虑</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium">
                            <div class="stock-header">
                                <div class="stock-code">#4 601138 沪市601138</div>
                                <div class="ai-score">AI: 68.36</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中等风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分中等；消息面观望</div>
                                <div class="detail-item">💡 投资建议: 👀 可以考虑</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium">
                            <div class="stock-header">
                                <div class="stock-code">#5 600570 恒生电子</div>
                                <div class="ai-score">AI: 68.36</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中等风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分中等；消息面关注</div>
                                <div class="detail-item">💡 投资建议: 👀 可以考虑</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium">
                            <div class="stock-header">
                                <div class="stock-code">#6 002104 深市002104</div>
                                <div class="ai-score">AI: 68.36</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中等风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分中等；消息面观望</div>
                                <div class="detail-item">💡 投资建议: 👀 可以考虑</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium">
                            <div class="stock-header">
                                <div class="stock-code">#7 601669 中国电建</div>
                                <div class="ai-score">AI: 66.36</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中等风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分中等；消息面观望</div>
                                <div class="detail-item">💡 投资建议: 👀 可以考虑</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium">
                            <div class="stock-header">
                                <div class="stock-code">#8 000796 深市000796</div>
                                <div class="ai-score">AI: 65.39</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中等风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分中等；消息面谨慎</div>
                                <div class="detail-item">💡 投资建议: 👀 可以考虑</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium">
                            <div class="stock-header">
                                <div class="stock-code">#9 002370 深市002370</div>
                                <div class="ai-score">AI: 65.39</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中等风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分中等；消息面谨慎</div>
                                <div class="detail-item">💡 投资建议: 👀 可以考虑</div>
                            </div>
                        </div>
                    
                        <div class="stock-card risk-medium">
                            <div class="stock-header">
                                <div class="stock-code">#10 601696 沪市601696</div>
                                <div class="ai-score">AI: 65.39</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: 中等风险</div>
                                <div class="detail-item">📊 数据来源: 人气榜 + 飙升榜 + 消息面</div>
                                <div class="detail-item">🤖 AI分析: 热门股和消息面双重验证；AI综合得分中等；消息面谨慎</div>
                                <div class="detail-item">💡 投资建议: 👀 可以考虑</div>
                            </div>
                        </div>
                    
                    </div>
                </div>
            </div>
                
            <div class="warning">
                <strong>⚠️ 投资风险提示:</strong><br>
                • 本报告基于AI算法分析，仅供参考，不构成投资建议<br>
                • 股市有风险，投资需谨慎，请根据个人风险承受能力做决策<br>
                • 建议结合更多信息和专业分析师意见进行投资决策<br>
                • 过往表现不代表未来收益，请理性投资
            </div>
        </div>
        
        <div class="footer">
            <p>AI智能选股分析系统 | 数据更新时间: 2025-07-30 21:37:50</p>
            <p>本系统综合分析热门股和消息面数据，使用AI算法生成投资建议</p>
        </div>
    </div>
</body>
</html>
        
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单直接采集脚本
1. 打开页面后直接采集当前显示的榜单
2. 只点击一次另一个榜单标签进行切换
3. 切换后再采集另一个榜单
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time
import csv
import re

def setup_driver():
    """配置并启动Chrome浏览器"""
    print("🚀 启动浏览器...")
    
    chrome_options = Options()
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--log-level=3')
    chrome_options.add_argument('--silent')
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    return driver

def extract_stock_data_with_names(driver, target_count=100):
    """从页面提取股票代码和名称"""
    print(f"📊 开始提取股票数据（包含名称），目标 {target_count} 条...")

    # 等待页面稳定
    time.sleep(3)

    stock_data = []

    # 方法1: 尝试从表格结构中提取
    print("🔍 方法1: 从表格结构提取...")
    try:
        # 尝试多种表格选择器
        table_selectors = [
            ".el-table__body-wrapper tbody tr",
            ".el-table tbody tr",
            "table tbody tr",
            "tbody tr"
        ]

        for selector in table_selectors:
            try:
                rows = driver.find_elements(By.CSS_SELECTOR, selector)
                if rows and len(rows) >= 10:  # 至少10行
                    print(f"  ✅ 使用选择器找到 {len(rows)} 行: {selector}")

                    for i, row in enumerate(rows):
                        if len(stock_data) >= target_count:
                            break

                        try:
                            # 获取行中的所有单元格
                            cells = row.find_elements(By.TAG_NAME, "td")
                            if not cells:
                                cells = row.find_elements(By.TAG_NAME, "div")

                            if len(cells) >= 2:
                                # 提取文本内容
                                texts = [cell.text.strip() for cell in cells if cell.text.strip()]

                                code = ""
                                name = ""
                                change = ""

                                # 智能解析
                                for text in texts:
                                    # 股票代码
                                    if len(text) == 6 and text.isdigit() and text.startswith(('00', '30', '60')):
                                        code = text
                                    # 涨跌幅
                                    elif '%' in text or (text.startswith(('+', '-')) and any(c.isdigit() for c in text)):
                                        change = text
                                    # 股票名称（中文字符，2-8个字符）
                                    elif (len(text) >= 2 and len(text) <= 8 and
                                          re.search(r'[\u4e00-\u9fa5]', text) and
                                          not text.isdigit()):
                                        if not name:  # 取第一个符合条件的作为名称
                                            name = text

                                if code:
                                    if not name:
                                        name = f"股票{len(stock_data)+1}"
                                    stock_data.append([code, name, change])
                                    print(f"  📈 {len(stock_data):3d}. {code} {name} {change}")

                        except Exception as e:
                            continue

                    if stock_data:
                        break  # 如果找到数据就停止尝试其他选择器

            except Exception as e:
                continue

    except Exception as e:
        print(f"  ❌ 表格提取失败: {str(e)[:50]}")

    # 方法2: 如果表格提取失败，从页面源码提取
    if len(stock_data) < target_count * 0.3:  # 如果少于30%
        print("🔍 方法2: 从页面源码提取...")

        page_source = driver.page_source
        print(f"📄 页面源码长度: {len(page_source)}")

        # 更精确的正则表达式模式
        patterns = [
            # 模式1: 股票代码紧跟中文名称
            r'(\d{6})\s*([^\x00-\x7F]{2,8})',
            # 模式2: 带引号的JSON格式
            r'"code":"(\d{6})"[^}]*?"name":"([^"]+)"',
            # 模式3: HTML标签中的数据
            r'>(\d{6})<[^>]*>[^<]*<[^>]*>([^\x00-\x7F]{2,8})<'
        ]

        found_pairs = set()
        existing_codes = {item[0] for item in stock_data}

        for i, pattern in enumerate(patterns):
            try:
                matches = re.findall(pattern, page_source)
                print(f"  模式 {i+1} 找到 {len(matches)} 个匹配")

                for code, name in matches:
                    if (len(code) == 6 and code.isdigit() and
                        code.startswith(('00', '30', '60')) and
                        code not in existing_codes):

                        # 清理名称
                        clean_name = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', name)

                        if len(clean_name) >= 2 and len(clean_name) <= 8:
                            pair = (code, clean_name)
                            if pair not in found_pairs:
                                found_pairs.add(pair)
                                stock_data.append([code, clean_name, ""])
                                existing_codes.add(code)
                                print(f"  📈 {len(stock_data):3d}. {code} {clean_name}")

                                if len(stock_data) >= target_count:
                                    break

                if len(stock_data) >= target_count:
                    break

            except Exception as e:
                continue

    # 方法3: 补充纯代码数据
    if len(stock_data) < target_count:
        print(f"🔍 方法3: 补充纯代码数据...")

        page_source = driver.page_source
        all_codes = re.findall(r'\b(\d{6})\b', page_source)
        existing_codes = {item[0] for item in stock_data}

        for code in all_codes:
            if (code.startswith(('00', '30', '60')) and
                code not in existing_codes and
                len(stock_data) < target_count):

                # 生成描述性名称
                if code.startswith('60'):
                    name = f"沪市{code}"
                elif code.startswith('00'):
                    name = f"深市{code}"
                else:
                    name = f"创业板{code}"

                stock_data.append([code, name, ""])
                existing_codes.add(code)

    print(f"✅ 提取完成，共获得 {len(stock_data)} 条股票数据")

    # 显示前10条数据预览
    print("📋 前10条数据预览:")
    for i, (code, name, change) in enumerate(stock_data[:10]):
        print(f"  {i+1:2d}. {code} {name} {change}")

    return stock_data[:target_count]

def detect_current_tab(driver):
    """检测当前是哪个榜单"""
    print("🔍 检测当前榜单...")
    
    page_source = driver.page_source
    
    # 简单检测：看页面内容中哪个关键词出现得更多
    renqi_count = page_source.count('人气')
    biaosheng_count = page_source.count('飙升')
    
    if renqi_count > biaosheng_count:
        current_tab = "人气榜"
        other_tab = "飙升榜"
    else:
        current_tab = "飙升榜" 
        other_tab = "人气榜"
    
    print(f"✅ 检测结果：当前可能是 {current_tab}")
    return current_tab, other_tab

def simple_click_tab(driver, tab_name):
    """简单点击标签页，只尝试一次"""
    print(f"🎯 尝试点击 {tab_name} 标签...")

    try:
        # 查找包含目标文本的元素
        elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{tab_name}')]")

        if not elements:
            print(f"❌ 未找到 {tab_name} 标签")
            return False

        print(f"✅ 找到 {len(elements)} 个包含 '{tab_name}' 的元素")

        # 只尝试第一个可见的元素
        for element in elements:
            try:
                if element.is_displayed() and element.is_enabled():
                    print(f"📍 尝试点击元素: {element.text[:20]}")

                    # 滚动到元素位置
                    driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    time.sleep(1)

                    # 先尝试JavaScript点击（避免被拦截）
                    try:
                        driver.execute_script("arguments[0].click();", element)
                        print(f"✅ JavaScript点击成功")
                        return True
                    except:
                        # 如果JavaScript点击失败，再尝试直接点击
                        try:
                            element.click()
                            print(f"✅ 直接点击成功")
                            return True
                        except Exception as e:
                            print(f"❌ 直接点击也失败: {str(e)[:30]}")
                            continue

            except Exception as e:
                print(f"❌ 处理元素失败: {str(e)[:50]}")
                continue

        return False

    except Exception as e:
        print(f"❌ 查找 {tab_name} 时出错: {str(e)[:50]}")
        return False

def save_data(data, filename, tab_name):
    """保存数据到CSV文件"""
    if data:
        with open(filename, "w", newline="", encoding="utf-8") as f:
            writer = csv.writer(f)
            writer.writerow(["code", "name", "change"])
            writer.writerows(data)
        print(f"✅ {filename} 已保存，共 {len(data)} 条数据")
        return True
    else:
        print(f"❌ {tab_name} 未提取到有效数据")
        return False

def main():
    """主函数"""
    driver = setup_driver()
    
    try:
        # 访问网站
        print("🌐 访问东方财富VIP网站...")
        driver.get("https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock")
        print("⏳ 页面加载中，请不要操作...")
        time.sleep(20)  # 等待页面完全加载
        
        # 检测当前榜单
        current_tab, other_tab = detect_current_tab(driver)
        
        # 第一步：采集当前显示的榜单
        print(f"\n{'='*50}")
        print(f"📊 第一步：采集当前榜单 ({current_tab})")
        print(f"{'='*50}")
        
        first_data = extract_stock_data_with_names(driver, 100)
        
        if current_tab == "人气榜":
            save_data(first_data, "popularity.csv", "人气榜")
        else:
            save_data(first_data, "soaring.csv", "飙升榜")
        
        print(f"✅ {current_tab} 采集完成：{len(first_data)} 条记录")
        
        # 第二步：点击切换到另一个榜单
        print(f"\n{'='*50}")
        print(f"🔄 第二步：切换到 {other_tab}")
        print(f"{'='*50}")
        
        # 只点击一次
        click_success = simple_click_tab(driver, other_tab)
        
        if click_success:
            print(f"✅ 已点击 {other_tab}，等待页面切换...")
            time.sleep(5)  # 等待页面切换
            
            # 采集另一个榜单的数据
            print(f"📊 开始采集 {other_tab} 数据...")
            second_data = extract_stock_data_with_names(driver, 100)
            
            if other_tab == "人气榜":
                save_data(second_data, "popularity.csv", "人气榜")
            else:
                save_data(second_data, "soaring.csv", "飙升榜")
            
            print(f"✅ {other_tab} 采集完成：{len(second_data)} 条记录")
            
            # 简单验证数据差异
            if first_data and second_data:
                first_codes = set(item[0] for item in first_data)
                second_codes = set(item[0] for item in second_data)
                common_codes = first_codes & second_codes
                
                print(f"\n📊 数据对比:")
                print(f"  {current_tab}: {len(first_codes)} 只股票")
                print(f"  {other_tab}: {len(second_codes)} 只股票")
                print(f"  重复股票: {len(common_codes)} 只")
                
                if len(common_codes) < len(first_codes) * 0.9:
                    print("✅ 两个榜单数据有差异，切换成功")
                else:
                    print("⚠️ 两个榜单数据相似，可能切换不完全")
        else:
            print(f"❌ 无法点击 {other_tab}，只采集了一个榜单")
        
        print(f"\n🎉 采集任务完成！")
        
        # 显示最终结果
        files_created = []
        if current_tab == "人气榜" or click_success:
            files_created.append("popularity.csv")
        if current_tab == "飙升榜" or click_success:
            files_created.append("soaring.csv")
        
        print(f"📁 生成的文件: {', '.join(files_created)}")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print(f"\n🔚 正在关闭浏览器...")
        driver.quit()
        print("✅ 浏览器已关闭")

if __name__ == "__main__":
    main()

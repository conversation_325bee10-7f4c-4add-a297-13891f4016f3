#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息面选股报告生成器
生成基于消息面分析的HTML选股报告
"""

import json
import os
from datetime import datetime

def generate_news_report():
    """生成消息面选股HTML报告"""
    
    # 检查数据文件
    if not os.path.exists('消息面选股结果.json'):
        print("❌ 消息面选股结果文件不存在")
        return False
    
    try:
        # 加载数据
        with open('消息面选股结果.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        recommendations = data.get('recommendations', [])
        hot_topics = data.get('hot_topics', [])
        total_topics = data.get('total_topics', 0)
        mentioned_stocks = data.get('mentioned_stocks', 0)
        analysis_time = data.get('analysis_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        # 生成HTML报告
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📰 消息面智能选股分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
        }}
        
        .content {{
            padding: 30px;
        }}
        
        .summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .summary-card {{
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        
        .summary-number {{
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        
        .section {{
            margin-bottom: 40px;
        }}
        
        .section h2 {{
            color: #2c3e50;
            border-bottom: 2px solid #9b59b6;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }}
        
        .recommendations {{
            display: grid;
            gap: 15px;
        }}
        
        .stock-card {{
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #9b59b6;
            transition: transform 0.3s ease;
        }}
        
        .stock-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}
        
        .stock-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }}
        
        .stock-code {{
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
        }}
        
        .sentiment-score {{
            background: #27ae60;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: bold;
        }}
        
        .stock-details {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }}
        
        .detail-item {{
            font-size: 0.9em;
            color: #7f8c8d;
        }}
        
        .topics-list {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }}
        
        .topic-card {{
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #3498db;
        }}
        
        .warning {{
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }}
        
        .footer {{
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        
        @media (max-width: 768px) {{
            .summary {{
                grid-template-columns: 1fr;
            }}
            
            .stock-header {{
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }}
            
            .stock-details {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📰 消息面智能选股分析报告</h1>
            <p>基于东方财富股吧热门话题的智能选股分析</p>
            <p>📅 分析时间: {analysis_time}</p>
        </div>
        
        <div class="content">
            <div class="summary">
                <div class="summary-card">
                    <div class="summary-number">{total_topics}</div>
                    <div>热门话题</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">{mentioned_stocks}</div>
                    <div>提及股票</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">{len(recommendations)}</div>
                    <div>推荐股票</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">{len([r for r in recommendations if r['sentiment_score'] > 1])}</div>
                    <div>高分推荐</div>
                </div>
            </div>
            
            <div class="section">
                <h2>🎯 智能选股推荐</h2>
                <p style="color: #7f8c8d; margin-bottom: 20px;">基于消息面情感分析的股票推荐，按推荐等级分组显示</p>
                <div class="recommendations">
        """
        
        # 添加推荐股票 - 按等级分组显示
        level_groups = {}
        for rec in recommendations:
            level = rec.get('recommend_level', '未分级')
            # 提取等级名称（去掉图标）
            level_name = level.split(' ')[-1] if ' ' in level else level
            if level_name not in level_groups:
                level_groups[level_name] = []
            level_groups[level_name].append(rec)

        # 按等级顺序显示
        level_order = ['强烈推荐', '推荐', '关注', '观望', '谨慎', '回避', '未分级']

        for level_name in level_order:
            if level_name in level_groups:
                stocks = level_groups[level_name]
                level_icons = {
                    "强烈推荐": "🔥",
                    "推荐": "⭐",
                    "关注": "👀",
                    "观望": "⚠️",
                    "谨慎": "🤔",
                    "回避": "❌",
                    "未分级": "❓"
                }

                icon = level_icons.get(level_name, "❓")
                html_content += f"""
                    <h3 style="color: #2c3e50; margin: 20px 0 10px 0; border-left: 4px solid #9b59b6; padding-left: 10px;">
                        {icon} {level_name} ({len(stocks)} 只)
                    </h3>
                """

                for rec in stocks:
                    # 根据等级设置颜色
                    level_colors = {
                        "强烈推荐": "#e74c3c",
                        "推荐": "#f39c12",
                        "关注": "#3498db",
                        "观望": "#95a5a6",
                        "谨慎": "#7f8c8d",
                        "回避": "#34495e"
                    }

                    card_color = level_colors.get(level_name, "#95a5a6")

                    html_content += f"""
                        <div class="stock-card" style="border-left-color: {card_color};">
                            <div class="stock-header">
                                <div class="stock-code">#{rec['rank']} {rec['code']} {rec['name']}</div>
                                <div class="sentiment-score" style="background: {card_color};">
                                    得分: {rec['sentiment_score']}
                                </div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">📊 提及次数: {rec['mention_count']}</div>
                                <div class="detail-item">💭 平均情感: {rec.get('avg_sentiment', 0)}</div>
                                <div class="detail-item">🏷️ 推荐等级: {icon} {level_name}</div>
                                <div class="detail-item">📝 推荐理由: {rec['reason']}</div>
                            </div>
                        </div>
                    """
        
        # 添加热门话题
        html_content += f"""
                </div>
            </div>
            
            <div class="section">
                <h2>📰 热门话题分析</h2>
                <p style="color: #7f8c8d; margin-bottom: 20px;">从东方财富股吧爬取的热门话题，用于情感分析和股票识别</p>
                <div class="topics-list">
        """
        
        for topic in hot_topics:
            html_content += f"""
                    <div class="topic-card">
                        <div style="font-weight: bold; margin-bottom: 5px;">{topic['title']}</div>
                        <div style="font-size: 0.8em; color: #7f8c8d;">
                            📅 {topic['timestamp']} | 📍 {topic['source']}
                        </div>
                    </div>
            """
        
        html_content += f"""
                </div>
            </div>
            
            <div class="warning">
                <h3>⚠️ 投资风险提示</h3>
                <p>本报告基于消息面分析生成，仅供参考，不构成投资建议。投资有风险，入市需谨慎。</p>
                <ul>
                    <li>消息面分析具有时效性，市场情况可能快速变化</li>
                    <li>建议结合技术面分析和基本面分析进行综合判断</li>
                    <li>注意控制仓位，分散投资风险</li>
                    <li>关注市场整体趋势和政策变化</li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <p>📊 消息面智能选股系统 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>💡 数据来源: 东方财富股吧热门话题 | 分析方法: 情感分析 + 提及频次</p>
        </div>
    </div>
</body>
</html>
        """
        
        # 保存HTML文件
        with open('消息面选股分析报告.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("✅ 消息面选股分析报告已生成: 消息面选股分析报告.html")
        return True
        
    except Exception as e:
        print(f"❌ 生成报告失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("📋 消息面选股报告生成器")
    print("=" * 40)
    
    success = generate_news_report()
    
    if success:
        print("🎉 报告生成完成！")
        print("💡 报告特色:")
        print("  • 基于消息面的智能选股分析")
        print("  • 情感得分和提及频次统计")
        print("  • 热门话题样本展示")
        print("  • 响应式设计，支持移动端")
        print("  • 投资风险提示")
    else:
        print("❌ 报告生成失败")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
页面元素分析工具
用于分析东方财富网站的页面结构，找到正确的标签页元素
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time
import re

def setup_driver():
    """配置并启动Chrome浏览器"""
    print("🚀 启动浏览器...")
    
    chrome_options = Options()
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--log-level=3')
    chrome_options.add_argument('--silent')
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    return driver

def analyze_page_elements(driver):
    """分析页面元素"""
    print("🔍 分析页面元素...")
    
    # 查找所有可能的标签页元素
    potential_selectors = [
        "div[class*='tab']",
        "span[class*='tab']", 
        "a[class*='tab']",
        "li[class*='tab']",
        "div[class*='nav']",
        "span[class*='nav']",
        "div[class*='item']",
        "span[class*='item']",
        "[role='tab']",
        "[role='tablist'] *"
    ]
    
    all_elements = []
    
    for selector in potential_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            for element in elements:
                try:
                    if element.is_displayed():
                        text = element.text.strip()
                        if text and len(text) < 50:  # 过滤掉太长的文本
                            all_elements.append({
                                'text': text,
                                'tag': element.tag_name,
                                'class': element.get_attribute('class') or '',
                                'id': element.get_attribute('id') or '',
                                'selector': selector,
                                'element': element
                            })
                except:
                    continue
        except:
            continue
    
    return all_elements

def find_ranking_tabs(elements):
    """查找排行榜相关的标签页"""
    print("🎯 查找排行榜相关标签页...")
    
    ranking_keywords = ['人气', '飙升', '涨幅', '跌幅', '成交', '换手', '振幅', '排行', '榜']
    
    ranking_tabs = []
    
    for element_info in elements:
        text = element_info['text']
        
        # 检查是否包含排行榜关键词
        for keyword in ranking_keywords:
            if keyword in text:
                ranking_tabs.append(element_info)
                break
    
    return ranking_tabs

def test_click_elements(driver, elements):
    """测试点击元素"""
    print("🖱️ 测试点击元素...")
    
    for i, element_info in enumerate(elements):
        if i >= 10:  # 只测试前10个
            break
            
        try:
            element = element_info['element']
            text = element_info['text']
            
            print(f"\n测试点击第 {i+1} 个元素: '{text}'")
            print(f"  标签: {element_info['tag']}")
            print(f"  类名: {element_info['class']}")
            print(f"  ID: {element_info['id']}")
            
            # 记录点击前的页面内容
            before_url = driver.current_url
            before_content = driver.page_source[:500]
            
            # 尝试点击
            try:
                driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(1)
                
                # 高亮元素
                driver.execute_script("arguments[0].style.border='3px solid red'", element)
                time.sleep(0.5)
                
                # 点击
                element.click()
                time.sleep(3)
                
                # 检查是否有变化
                after_url = driver.current_url
                after_content = driver.page_source[:500]
                
                if after_url != before_url:
                    print(f"  ✅ URL发生变化: {before_url} -> {after_url}")
                elif after_content != before_content:
                    print(f"  ✅ 页面内容发生变化")
                else:
                    print(f"  ⚠️ 点击后无明显变化")
                
                # 移除高亮
                driver.execute_script("arguments[0].style.border=''", element)
                
            except Exception as e:
                print(f"  ❌ 点击失败: {str(e)[:50]}")
                
        except Exception as e:
            print(f"  ❌ 处理元素失败: {str(e)[:50]}")
            continue

def analyze_page_structure(driver):
    """分析页面结构"""
    print("📋 分析页面结构...")
    
    # 查找所有包含"榜"字的元素
    try:
        bang_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '榜')]")
        print(f"\n找到 {len(bang_elements)} 个包含'榜'字的元素:")
        
        for i, element in enumerate(bang_elements[:10]):
            try:
                if element.is_displayed():
                    text = element.text.strip()
                    tag = element.tag_name
                    class_name = element.get_attribute('class') or ''
                    print(f"  {i+1}. <{tag}> '{text}' (class: {class_name[:50]})")
            except:
                continue
                
    except Exception as e:
        print(f"查找'榜'字元素失败: {e}")
    
    # 查找所有包含"人气"的元素
    try:
        renqi_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '人气')]")
        print(f"\n找到 {len(renqi_elements)} 个包含'人气'的元素:")
        
        for i, element in enumerate(renqi_elements[:10]):
            try:
                if element.is_displayed():
                    text = element.text.strip()
                    tag = element.tag_name
                    class_name = element.get_attribute('class') or ''
                    print(f"  {i+1}. <{tag}> '{text}' (class: {class_name[:50]})")
            except:
                continue
                
    except Exception as e:
        print(f"查找'人气'元素失败: {e}")
    
    # 查找所有包含"飙升"的元素
    try:
        biaosheng_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '飙升')]")
        print(f"\n找到 {len(biaosheng_elements)} 个包含'飙升'的元素:")
        
        for i, element in enumerate(biaosheng_elements[:10]):
            try:
                if element.is_displayed():
                    text = element.text.strip()
                    tag = element.tag_name
                    class_name = element.get_attribute('class') or ''
                    print(f"  {i+1}. <{tag}> '{text}' (class: {class_name[:50]})")
            except:
                continue
                
    except Exception as e:
        print(f"查找'飙升'元素失败: {e}")

def save_page_source(driver):
    """保存页面源码用于分析"""
    print("💾 保存页面源码...")
    
    try:
        with open("page_source.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        print("✅ 页面源码已保存到 page_source.html")
    except Exception as e:
        print(f"❌ 保存页面源码失败: {e}")

def main():
    """主函数"""
    driver = setup_driver()
    
    try:
        # 访问网站
        print("🌐 访问东方财富VIP网站...")
        driver.get("https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock")
        print("⏳ 页面加载中...")
        time.sleep(20)
        
        # 保存页面源码
        save_page_source(driver)
        
        # 分析页面结构
        analyze_page_structure(driver)
        
        # 分析页面元素
        all_elements = analyze_page_elements(driver)
        print(f"\n📊 找到 {len(all_elements)} 个潜在的标签页元素")
        
        # 查找排行榜相关标签页
        ranking_tabs = find_ranking_tabs(all_elements)
        print(f"\n🎯 找到 {len(ranking_tabs)} 个排行榜相关标签页:")
        
        for i, tab in enumerate(ranking_tabs):
            print(f"  {i+1}. '{tab['text']}' ({tab['tag']}, class: {tab['class'][:30]})")
        
        # 测试点击排行榜标签页
        if ranking_tabs:
            print(f"\n🖱️ 测试点击排行榜标签页...")
            test_click_elements(driver, ranking_tabs)
        
        print("\n🎉 页面分析完成！")
        print("💡 请查看输出信息，找到正确的标签页选择器")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        input("\n按回车键关闭浏览器...")
        driver.quit()
        print("🔚 浏览器已关闭")

if __name__ == "__main__":
    main()

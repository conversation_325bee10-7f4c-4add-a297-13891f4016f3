#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据采集系统 - 主程序
集成所有功能的统一入口程序
"""

import os
import sys
import subprocess
import time
from datetime import datetime

class StockDataSystem:
    def __init__(self):
        self.system_name = "股票数据采集系统"
        self.version = "v2.0"
        self.author = "AI Assistant"
        
        # 检查必要文件
        self.required_files = [
            "后台采集.py",
            "股票名称获取工具.py", 
            "股票涨跌幅获取工具.py",
            "智能数据分析报告.py",
            "数据服务器.py",
            "可视化界面_实时版.html"
        ]
        
        self.check_system_files()
    
    def check_system_files(self):
        """检查系统文件完整性"""
        print("🔍 检查系统文件...")
        missing_files = []
        
        for file in self.required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print("❌ 缺少以下文件:")
            for file in missing_files:
                print(f"  - {file}")
            print("⚠️ 请确保所有文件都在当前目录中")
        else:
            print("✅ 所有系统文件完整")
    
    def show_banner(self):
        """显示系统横幅"""
        print("=" * 60)
        print(f"🚀 {self.system_name} {self.version}")
        print(f"📅 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"👨‍💻 开发者: {self.author}")
        print("=" * 60)
    
    def show_menu(self):
        """显示主菜单"""
        print("\n📋 功能菜单:")
        print("=" * 40)
        print("1. 🔇 后台数据采集")
        print("2. 📊 获取股票名称")
        print("3. 📈 获取涨跌幅数据")
        print("4. 🔧 修复异常数据")
        print("5. 📄 生成分析报告")
        print("6. 🌐 启动数据服务器")
        print("7. 🖥️ 打开可视化界面")
        print("8. 📁 查看数据文件")
        print("9. 🧹 清理无用文件")
        print("10. ⚙️ 系统状态检查")
        print("0. 🚪 退出系统")
        print("=" * 40)
    
    def run_script(self, script_name, description):
        """运行Python脚本"""
        print(f"\n🚀 {description}...")
        print(f"📝 执行脚本: {script_name}")
        print("-" * 40)
        
        try:
            if not os.path.exists(script_name):
                print(f"❌ 脚本文件不存在: {script_name}")
                return False
            
            # 使用subprocess运行脚本
            result = subprocess.run([sys.executable, script_name], 
                                  capture_output=False, 
                                  text=True, 
                                  cwd=os.getcwd())
            
            if result.returncode == 0:
                print(f"✅ {description}完成")
                return True
            else:
                print(f"❌ {description}失败，返回码: {result.returncode}")
                return False
                
        except Exception as e:
            print(f"❌ 执行脚本时出错: {str(e)}")
            return False
    
    def background_collection(self):
        """后台数据采集"""
        return self.run_script("后台采集.py", "后台数据采集")
    
    def get_stock_names(self):
        """获取股票名称"""
        return self.run_script("股票名称获取工具.py", "获取股票名称")
    
    def get_price_changes(self):
        """获取涨跌幅数据"""
        return self.run_script("股票涨跌幅获取工具.py", "获取涨跌幅数据")
    
    def fix_abnormal_data(self):
        """修复异常数据"""
        return self.run_script("最终修复涨跌幅.py", "修复异常涨跌幅数据")
    
    def generate_report(self):
        """生成分析报告"""
        success = self.run_script("智能数据分析报告.py", "生成智能分析报告")
        if success:
            print("\n📊 报告生成完成！")
            print("📄 HTML报告: 股票分析报告.html")
            print("📈 图表目录: charts/")
            
            # 询问是否打开报告
            choice = input("\n是否打开HTML报告？(y/n): ").lower().strip()
            if choice == 'y':
                self.open_html_report()
        return success
    
    def start_data_server(self):
        """启动数据服务器"""
        print(f"\n🌐 启动数据服务器...")
        print("📝 执行脚本: 数据服务器.py")
        print("-" * 40)
        
        try:
            if not os.path.exists("数据服务器.py"):
                print("❌ 数据服务器脚本不存在")
                return False
            
            print("🚀 正在启动服务器...")
            print("💡 服务器将在后台运行")
            print("🌐 访问地址: http://localhost:8080/可视化界面_实时版.html")
            print("⚠️ 按 Ctrl+C 可停止服务器")
            print("-" * 40)
            
            # 启动服务器（非阻塞）
            subprocess.Popen([sys.executable, "数据服务器.py"], 
                           cwd=os.getcwd())
            
            time.sleep(2)  # 等待服务器启动
            print("✅ 数据服务器已启动")
            
            # 询问是否打开界面
            choice = input("\n是否打开可视化界面？(y/n): ").lower().strip()
            if choice == 'y':
                self.open_web_interface()
            
            return True
            
        except Exception as e:
            print(f"❌ 启动服务器时出错: {str(e)}")
            return False
    
    def open_web_interface(self):
        """打开可视化界面"""
        try:
            import webbrowser
            url = "http://localhost:8080/可视化界面_实时版.html"
            webbrowser.open(url)
            print(f"🌐 已在浏览器中打开: {url}")
        except Exception as e:
            print(f"❌ 打开浏览器失败: {str(e)}")
            print(f"💡 请手动在浏览器中访问: http://localhost:8080/可视化界面_实时版.html")
    
    def open_html_report(self):
        """打开HTML报告"""
        try:
            import webbrowser
            report_path = os.path.abspath("股票分析报告.html")
            webbrowser.open(f"file:///{report_path}")
            print(f"📄 已在浏览器中打开分析报告")
        except Exception as e:
            print(f"❌ 打开报告失败: {str(e)}")
            print(f"💡 请手动打开文件: 股票分析报告.html")
    
    def view_data_files(self):
        """查看数据文件"""
        print(f"\n📁 数据文件状态:")
        print("-" * 40)
        
        data_files = [
            ("popularity.csv", "人气榜数据"),
            ("soaring.csv", "飙升榜数据"),
            ("codes.txt", "股票代码库"),
            ("stock_names_cache.json", "股票名称缓存"),
            ("股票分析报告.html", "分析报告"),
            ("charts/", "图表目录")
        ]
        
        for file_path, description in data_files:
            if os.path.exists(file_path):
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    if size < 1024:
                        size_str = f"{size} B"
                    elif size < 1024 * 1024:
                        size_str = f"{size/1024:.1f} KB"
                    else:
                        size_str = f"{size/(1024*1024):.1f} MB"
                    
                    mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    print(f"✅ {description}: {file_path} ({size_str}, {mod_time.strftime('%Y-%m-%d %H:%M')})")
                else:
                    print(f"✅ {description}: {file_path} (目录)")
            else:
                print(f"❌ {description}: {file_path} (不存在)")
    
    def clean_files(self):
        """清理无用文件"""
        return self.run_script("清理无用文件.py", "清理无用文件")
    
    def system_status(self):
        """系统状态检查"""
        print(f"\n⚙️ 系统状态检查:")
        print("-" * 40)
        
        # 检查Python版本
        print(f"🐍 Python版本: {sys.version.split()[0]}")
        
        # 检查必要模块
        required_modules = [
            ("selenium", "网页自动化"),
            ("requests", "HTTP请求"),
            ("matplotlib", "图表生成"),
            ("pandas", "数据处理"),
            ("numpy", "数值计算")
        ]
        
        for module, description in required_modules:
            try:
                __import__(module)
                print(f"✅ {description}: {module} 已安装")
            except ImportError:
                print(f"❌ {description}: {module} 未安装")
        
        # 检查Chrome浏览器
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            driver = webdriver.Chrome(options=options)
            driver.quit()
            print("✅ Chrome浏览器: 可用")
        except Exception as e:
            print(f"❌ Chrome浏览器: 不可用 ({str(e)[:50]})")
        
        # 检查数据文件
        self.view_data_files()
    
    def run(self):
        """运行主程序"""
        self.show_banner()
        
        while True:
            try:
                self.show_menu()
                choice = input("\n请选择功能 (0-10): ").strip()
                
                if choice == '0':
                    print("\n👋 感谢使用股票数据采集系统！")
                    break
                elif choice == '1':
                    self.background_collection()
                elif choice == '2':
                    self.get_stock_names()
                elif choice == '3':
                    self.get_price_changes()
                elif choice == '4':
                    self.fix_abnormal_data()
                elif choice == '5':
                    self.generate_report()
                elif choice == '6':
                    self.start_data_server()
                elif choice == '7':
                    self.open_web_interface()
                elif choice == '8':
                    self.view_data_files()
                elif choice == '9':
                    self.clean_files()
                elif choice == '10':
                    self.system_status()
                else:
                    print("❌ 无效选择，请输入 0-10 之间的数字")
                
                # 等待用户确认
                if choice != '0':
                    input("\n按回车键继续...")
                    
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，退出系统")
                break
            except Exception as e:
                print(f"\n❌ 程序出错: {str(e)}")
                input("按回车键继续...")

def main():
    """主函数"""
    try:
        system = StockDataSystem()
        system.run()
    except Exception as e:
        print(f"❌ 系统启动失败: {str(e)}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()

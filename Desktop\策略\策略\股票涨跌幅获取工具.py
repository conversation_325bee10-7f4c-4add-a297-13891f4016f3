#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票涨跌幅获取工具
从多个数据源在线获取实时股票涨跌幅数据
"""

import requests
import json
import csv
import time
import os
from datetime import datetime

class StockPriceFetcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'http://finance.sina.com.cn/'
        })
    
    def get_price_from_sina(self, code):
        """从新浪财经获取股票价格和涨跌幅"""
        try:
            # 根据股票代码确定市场
            if code.startswith('6'):
                full_code = f"sh{code}"
            else:
                full_code = f"sz{code}"

            url = f"http://hq.sinajs.cn/list={full_code}"
            response = self.session.get(url, timeout=5)
            response.encoding = 'gbk'

            if response.status_code == 200 and response.text:
                data = response.text.strip()
                if 'var hq_str_' in data and '=""' not in data:
                    # 解析数据：股票名称,今开,昨收,现价,最高,最低,买一,卖一,成交量,成交额,买一量,买一价,...
                    start = data.find('"') + 1
                    end = data.rfind('"')
                    if start > 0 and end > start:
                        fields = data[start:end].split(',')
                        if len(fields) >= 4:
                            name = fields[0].strip()
                            current_price = float(fields[3]) if fields[3] and fields[3] != '0' else 0
                            prev_close = float(fields[2]) if fields[2] and fields[2] != '0' else 0

                            if current_price > 0 and prev_close > 0:
                                change = current_price - prev_close
                                change_percent = (change / prev_close) * 100

                                # 验证涨跌幅是否合理（一般股票单日涨跌幅不超过±20%）
                                if abs(change_percent) > 20:
                                    print(f"    ⚠️ {code} 涨跌幅异常: {change_percent:.2f}% (现价:{current_price}, 昨收:{prev_close})")
                                    return None

                                # 格式化涨跌幅
                                if change_percent > 0:
                                    change_str = f"+{change_percent:.2f}%"
                                elif change_percent < 0:
                                    change_str = f"{change_percent:.2f}%"
                                else:
                                    change_str = "0.00%"

                                return {
                                    'name': name,
                                    'current_price': current_price,
                                    'prev_close': prev_close,
                                    'change': change,
                                    'change_percent': change_str
                                }

        except Exception as e:
            pass

        return None
    
    def get_price_from_eastmoney(self, code):
        """从东方财富获取股票价格和涨跌幅"""
        try:
            # 根据股票代码确定市场
            if code.startswith('6'):
                market_code = f"1.{code}"
            else:
                market_code = f"0.{code}"

            url = "http://push2.eastmoney.com/api/qt/stock/get"
            params = {
                'secid': market_code,
                'fields': 'f58,f43,f44,f45,f46,f47,f48,f169,f170'
                # f58:股票名称, f43:现价, f44:最高, f45:最低, f46:今开, f47:成交量, f48:成交额, f169:涨跌额, f170:涨跌幅
            }

            response = self.session.get(url, params=params, timeout=5)

            if response.status_code == 200:
                data = response.json()
                if data.get('rc') == 0 and data.get('data'):
                    stock_data = data['data']
                    name = stock_data.get('f58', '')
                    current_price = stock_data.get('f43', 0)
                    change_percent = stock_data.get('f170', 0)

                    if current_price and change_percent is not None:
                        # 验证涨跌幅是否合理（一般股票单日涨跌幅不超过±20%，ST股票±5%）
                        if abs(change_percent) > 25:  # 放宽一点限制
                            print(f"    ⚠️ {code} 涨跌幅异常: {change_percent:.2f}% (可能是ST股票或新股)")
                            # 对于异常数据，仍然返回，但标记
                            if abs(change_percent) > 100:  # 超过100%的明显异常
                                return None

                        # 格式化涨跌幅
                        if change_percent > 0:
                            change_str = f"+{change_percent:.2f}%"
                        elif change_percent < 0:
                            change_str = f"{change_percent:.2f}%"
                        else:
                            change_str = "0.00%"

                        return {
                            'name': name,
                            'current_price': current_price,
                            'change_percent': change_str
                        }

        except Exception as e:
            pass

        return None
    
    def get_price_from_tencent(self, code):
        """从腾讯财经获取股票价格和涨跌幅"""
        try:
            # 根据股票代码确定市场
            if code.startswith('6'):
                full_code = f"sh{code}"
            else:
                full_code = f"sz{code}"
            
            url = f"http://qt.gtimg.cn/q={full_code}"
            response = self.session.get(url, timeout=5)
            response.encoding = 'gbk'
            
            if response.status_code == 200 and response.text:
                data = response.text.strip()
                if '~' in data:
                    # 腾讯数据格式：代码~名称~现价~涨跌~涨跌幅~成交量~...
                    parts = data.split('~')
                    if len(parts) >= 6:
                        name = parts[1].strip()
                        current_price = float(parts[3]) if parts[3] else 0
                        change_percent = float(parts[32]) if len(parts) > 32 and parts[32] else 0
                        
                        if current_price > 0:
                            # 格式化涨跌幅
                            if change_percent > 0:
                                change_str = f"+{change_percent:.2f}%"
                            elif change_percent < 0:
                                change_str = f"{change_percent:.2f}%"
                            else:
                                change_str = "0.00%"
                            
                            return {
                                'name': name,
                                'current_price': current_price,
                                'change_percent': change_str
                            }
            
        except Exception as e:
            pass
        
        return None
    
    def get_stock_price_info(self, code):
        """获取股票价格信息（尝试多个数据源）"""
        sources = [
            ('东方财富', self.get_price_from_eastmoney),
            ('新浪财经', self.get_price_from_sina),
            ('腾讯财经', self.get_price_from_tencent)
        ]
        
        for source_name, get_func in sources:
            try:
                result = get_func(code)
                if result and result.get('change_percent'):
                    print(f"  📈 {code} {result.get('name', '')} {result['change_percent']} (来源: {source_name})")
                    return result
            except Exception as e:
                continue
            
            # 避免请求过于频繁
            time.sleep(0.1)
        
        print(f"  ❌ {code} 无法获取涨跌幅数据")
        return None
    
    def batch_update_prices(self, codes, max_requests=100):
        """批量更新股票价格信息"""
        print(f"🔄 开始批量获取 {len(codes)} 只股票的涨跌幅...")
        print(f"⚠️ 为避免被限制，最多处理 {max_requests} 只股票")
        
        results = {}
        success_count = 0
        
        for i, code in enumerate(codes[:max_requests]):
            print(f"  🔍 获取 {code} 的涨跌幅... ({i+1}/{min(len(codes), max_requests)})")
            
            price_info = self.get_stock_price_info(code)
            if price_info:
                results[code] = price_info
                success_count += 1
            
            # 每10个请求休息一下
            if (i + 1) % 10 == 0:
                print(f"  📋 进度: {i+1}/{min(len(codes), max_requests)} (成功: {success_count})")
                time.sleep(2)  # 休息2秒
        
        print(f"✅ 批量获取完成!")
        print(f"  📊 总处理: {min(len(codes), max_requests)} 只股票")
        print(f"  🆕 成功获取: {success_count} 只")
        print(f"  ❌ 获取失败: {min(len(codes), max_requests) - success_count} 只")
        
        return results

def update_csv_with_prices(csv_file, fetcher):
    """更新CSV文件中的涨跌幅数据"""
    if not os.path.exists(csv_file):
        print(f"❌ 文件不存在: {csv_file}")
        return
    
    print(f"📝 更新 {csv_file} 中的涨跌幅数据...")
    
    # 读取CSV文件
    data = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        data = list(reader)
    
    # 收集需要更新的股票代码
    codes_to_update = []
    for row in data:
        code = row.get('code', '')
        current_change = row.get('change', '').strip()
        
        # 如果涨跌幅为空或者是无效数据，则需要更新
        if not current_change or current_change in ['', 'N/A', '0.00%']:
            codes_to_update.append(code)
    
    if not codes_to_update:
        print(f"ℹ️ {csv_file} 中所有股票都已有涨跌幅数据，无需更新")
        return
    
    print(f"📊 需要更新涨跌幅的股票: {len(codes_to_update)} 只")
    
    # 批量获取价格信息
    price_results = fetcher.batch_update_prices(codes_to_update)
    
    # 更新数据
    updated_count = 0
    for row in data:
        code = row.get('code', '')
        if code in price_results:
            price_info = price_results[code]
            
            # 更新涨跌幅
            if price_info.get('change_percent'):
                row['change'] = price_info['change_percent']
                updated_count += 1
            
            # 如果名称为空或默认格式，也更新名称
            current_name = row.get('name', '')
            if (price_info.get('name') and 
                (not current_name or 
                 current_name.startswith('沪市') or 
                 current_name.startswith('深市') or 
                 current_name.startswith('创业板'))):
                row['name'] = price_info['name']
    
    # 保存更新后的文件
    if updated_count > 0:
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            if data:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
        
        print(f"✅ {csv_file} 更新完成，更新了 {updated_count} 个股票的涨跌幅")
    else:
        print(f"ℹ️ {csv_file} 无涨跌幅数据需要更新")

def main():
    """主函数"""
    print("🚀 股票涨跌幅获取工具")
    print("=" * 50)
    print("📊 从多个数据源获取实时股票涨跌幅")
    print("🔄 自动更新CSV文件中的涨跌幅数据")
    print("=" * 50)
    
    fetcher = StockPriceFetcher()
    
    # 更新CSV文件
    csv_files = ['popularity.csv', 'soaring.csv']
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"\n📁 处理文件: {csv_file}")
            update_csv_with_prices(csv_file, fetcher)
        else:
            print(f"⚠️ 文件不存在: {csv_file}")
    
    print(f"\n🎉 所有任务完成！")
    print(f"💡 涨跌幅数据已更新到CSV文件")
    print(f"🌐 HTML界面将显示最新的涨跌幅数据")

if __name__ == "__main__":
    main()

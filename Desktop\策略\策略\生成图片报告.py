#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成图片报告
专门用于生成包含图片的HTML报告
"""

import os
from datetime import datetime

def generate_chart_report():
    """生成包含图片的HTML报告"""
    
    # 检查图片文件是否存在
    chart_files = [
        ('charts/market_distribution.png', '📊 市场分布分析', '显示沪市、深市、创业板等不同板块的股票分布情况'),
        ('charts/change_distribution.png', '📈 涨跌幅分布', '展示股票涨跌幅的分布特征和集中趋势'),
        ('charts/sector_distribution.png', '🏢 行业板块对比', '对比人气榜和飙升榜在不同行业板块的分布差异'),
        ('charts/volatility_analysis.png', '📊 波动性分析', '分析股票的波动性模式，识别高风险和低风险股票'),
        ('charts/market_sentiment.png', '💭 市场情绪指标', '反映市场整体情绪，包括上涨下跌比例和强弱势股分布'),
        ('charts/risk_assessment.png', '⚠️ 风险评估雷达', '多维度风险评估，包括波动率、VaR和极端风险指标')
    ]
    
    print("🔍 检查图片文件...")
    existing_charts = []
    for file_path, title, desc in chart_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✅ {file_path}: {size/1024:.1f} KB")
            existing_charts.append((file_path, title, desc))
        else:
            print(f"  ❌ {file_path}: 文件不存在")
    
    if not existing_charts:
        print("❌ 没有找到任何图片文件，请先运行智能数据分析报告生成图片")
        return False
    
    # 生成HTML内容
    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票数据分析报告 - 图表展示</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }}
        
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }}
        
        .header p {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .content {{
            padding: 40px;
        }}
        
        .chart-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }}
        
        .chart-item {{
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }}
        
        .chart-item:hover {{
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }}
        
        .chart-item h3 {{
            color: #2c3e50;
            margin: 0 0 15px 0;
            font-size: 1.3em;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }}
        
        .chart-image {{
            width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            display: block;
        }}
        
        .chart-desc {{
            font-size: 1em;
            color: #6c757d;
            text-align: center;
            line-height: 1.6;
            margin: 0;
        }}
        
        .footer {{
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
        }}
        
        .status-badge {{
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            margin: 10px 5px;
        }}
        
        .error-message {{
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 股票数据分析报告</h1>
            <p>📊 多维度分析图表展示</p>
            <div class="status-badge">✅ 图表已生成</div>
            <div class="status-badge">📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
        </div>
        
        <div class="content">
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">📈 多维度分析图表</h2>
            
            <div class="chart-grid">
"""
    
    # 添加每个图表
    for file_path, title, desc in existing_charts:
        html_content += f"""
                <div class="chart-item">
                    <h3>{title}</h3>
                    <img src="{file_path}" alt="{title}" class="chart-image" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="error-message" style="display: none;">
                        ❌ 图片加载失败<br>
                        文件路径: {file_path}<br>
                        请检查文件是否存在
                    </div>
                    <p class="chart-desc">{desc}</p>
                </div>
"""
    
    html_content += f"""
            </div>
            
            <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 30px 0;">
                <h3 style="color: #27ae60; margin-top: 0;">💡 图表说明</h3>
                <ul style="color: #2c3e50; line-height: 1.8;">
                    <li><strong>如果图片无法显示</strong>，请检查 charts/ 目录是否存在图片文件</li>
                    <li><strong>图片路径</strong>：所有图片都保存在 charts/ 子目录中</li>
                    <li><strong>重新生成</strong>：运行 "python 智能数据分析报告.py" 重新生成图片</li>
                    <li><strong>浏览器兼容</strong>：建议使用 Chrome、Firefox 或 Edge 浏览器查看</li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>📊 图表统计</strong></p>
            <p>共生成 {len(existing_charts)} 张分析图表</p>
            <p>🚀 股票数据采集系统 - 多维度分析报告</p>
            <p>📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>
    
    <script>
        // 检查图片加载状态
        document.addEventListener('DOMContentLoaded', function() {{
            const images = document.querySelectorAll('.chart-image');
            let loadedCount = 0;
            let totalCount = images.length;
            
            images.forEach(function(img) {{
                img.onload = function() {{
                    loadedCount++;
                    console.log('图片加载成功:', img.src);
                    if (loadedCount === totalCount) {{
                        console.log('所有图片加载完成');
                    }}
                }};
                
                img.onerror = function() {{
                    console.error('图片加载失败:', img.src);
                    console.log('文件路径:', img.getAttribute('src'));
                }};
            }});
        }});
    </script>
</body>
</html>"""
    
    # 保存HTML文件
    output_file = "股票分析报告_图表版.html"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"\n✅ 图表报告生成成功: {output_file}")
        print(f"📊 包含 {len(existing_charts)} 张图表")
        print(f"🌐 请在浏览器中打开查看图表显示效果")
        
        # 获取绝对路径
        abs_path = os.path.abspath(output_file)
        print(f"📂 文件路径: {abs_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成报告失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎨 股票数据分析报告 - 图表版生成器")
    print("=" * 50)
    
    # 确保charts目录存在
    if not os.path.exists('charts'):
        print("❌ charts 目录不存在，正在创建...")
        os.makedirs('charts')
        print("✅ charts 目录已创建")
    
    # 生成报告
    success = generate_chart_report()
    
    if success:
        print(f"\n🎉 报告生成完成！")
        print(f"💡 如果图片仍然无法显示，可能的原因：")
        print(f"   1. 图片文件不存在 - 请先运行智能数据分析报告")
        print(f"   2. 路径问题 - 检查 charts/ 目录是否在正确位置")
        print(f"   3. 浏览器缓存 - 尝试刷新页面或清除缓存")
    else:
        print(f"\n❌ 报告生成失败")

if __name__ == "__main__":
    main()

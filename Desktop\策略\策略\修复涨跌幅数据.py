#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复涨跌幅数据工具
检查并修复异常的涨跌幅数据
"""

import requests
import json
import csv
import time
import os
import re
from datetime import datetime

class StockDataFixer:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'http://finance.sina.com.cn/'
        })
    
    def is_abnormal_change(self, change_str):
        """检查涨跌幅是否异常"""
        if not change_str or change_str in ['', 'N/A', '0.00%']:
            return False
        
        try:
            # 提取数字部分
            number_str = change_str.replace('+', '').replace('%', '')
            change_value = float(number_str)
            
            # 检查是否超过合理范围
            # 普通股票：±20%
            # ST股票：±5%
            # 新股首日：可能更高，但一般不超过±50%
            if abs(change_value) > 50:
                return True
            
            return False
            
        except:
            return True
    
    def get_reliable_price_data(self, code):
        """获取可靠的股票价格数据"""
        try:
            # 使用新浪财经API，数据相对稳定
            if code.startswith('6'):
                full_code = f"sh{code}"
            else:
                full_code = f"sz{code}"
            
            url = f"http://hq.sinajs.cn/list={full_code}"
            response = self.session.get(url, timeout=10)
            response.encoding = 'gbk'
            
            if response.status_code == 200 and response.text:
                data = response.text.strip()
                if 'var hq_str_' in data and '=""' not in data:
                    start = data.find('"') + 1
                    end = data.rfind('"')
                    if start > 0 and end > start:
                        fields = data[start:end].split(',')
                        if len(fields) >= 32:
                            name = fields[0].strip()
                            today_open = float(fields[1]) if fields[1] and fields[1] != '0.000' else 0
                            prev_close = float(fields[2]) if fields[2] and fields[2] != '0.000' else 0
                            current_price = float(fields[3]) if fields[3] and fields[3] != '0.000' else 0
                            
                            # 检查数据有效性
                            if current_price > 0 and prev_close > 0:
                                change = current_price - prev_close
                                change_percent = (change / prev_close) * 100
                                
                                # 验证涨跌幅合理性
                                if abs(change_percent) <= 20:  # 普通股票涨跌幅限制
                                    if change_percent > 0:
                                        change_str = f"+{change_percent:.2f}%"
                                    elif change_percent < 0:
                                        change_str = f"{change_percent:.2f}%"
                                    else:
                                        change_str = "0.00%"
                                    
                                    return {
                                        'name': name,
                                        'current_price': current_price,
                                        'prev_close': prev_close,
                                        'change_percent': change_str,
                                        'status': 'normal'
                                    }
                                elif abs(change_percent) <= 50:  # 可能是ST股票或特殊情况
                                    if change_percent > 0:
                                        change_str = f"+{change_percent:.2f}%"
                                    elif change_percent < 0:
                                        change_str = f"{change_percent:.2f}%"
                                    else:
                                        change_str = "0.00%"
                                    
                                    return {
                                        'name': name,
                                        'current_price': current_price,
                                        'prev_close': prev_close,
                                        'change_percent': change_str,
                                        'status': 'special'
                                    }
                                else:
                                    # 涨跌幅过大，可能是数据错误
                                    return {
                                        'name': name,
                                        'status': 'error',
                                        'error_msg': f'涨跌幅异常: {change_percent:.2f}%'
                                    }
                            else:
                                return {
                                    'name': name if name else '',
                                    'status': 'suspended',
                                    'error_msg': '股票可能停牌'
                                }
            
        except Exception as e:
            return {
                'status': 'error',
                'error_msg': f'获取数据失败: {str(e)[:50]}'
            }
        
        return None
    
    def fix_csv_data(self, csv_file):
        """修复CSV文件中的异常数据"""
        if not os.path.exists(csv_file):
            print(f"❌ 文件不存在: {csv_file}")
            return
        
        print(f"🔧 检查并修复 {csv_file} 中的异常数据...")
        
        # 读取CSV文件
        data = []
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            data = list(reader)
        
        # 检查异常数据
        abnormal_stocks = []
        for row in data:
            code = row.get('code', '')
            change = row.get('change', '')
            
            if self.is_abnormal_change(change):
                abnormal_stocks.append((code, change))
        
        if not abnormal_stocks:
            print(f"✅ {csv_file} 中没有发现异常的涨跌幅数据")
            return
        
        print(f"⚠️ 发现 {len(abnormal_stocks)} 只股票的涨跌幅数据异常:")
        for code, change in abnormal_stocks:
            print(f"  📊 {code}: {change}")
        
        # 修复异常数据
        fixed_count = 0
        error_count = 0
        
        for row in data:
            code = row.get('code', '')
            current_change = row.get('change', '')
            
            if self.is_abnormal_change(current_change):
                print(f"  🔧 修复 {code} 的涨跌幅数据...")
                
                # 获取正确的数据
                correct_data = self.get_reliable_price_data(code)
                
                if correct_data:
                    if correct_data['status'] == 'normal':
                        row['change'] = correct_data['change_percent']
                        if correct_data.get('name'):
                            row['name'] = correct_data['name']
                        print(f"    ✅ {code} 修复成功: {correct_data['change_percent']}")
                        fixed_count += 1
                    elif correct_data['status'] == 'special':
                        row['change'] = correct_data['change_percent']
                        if correct_data.get('name'):
                            row['name'] = correct_data['name']
                        print(f"    ⚠️ {code} 特殊情况: {correct_data['change_percent']}")
                        fixed_count += 1
                    elif correct_data['status'] == 'suspended':
                        row['change'] = '停牌'
                        print(f"    🚫 {code} 停牌")
                        fixed_count += 1
                    else:
                        print(f"    ❌ {code} 修复失败: {correct_data.get('error_msg', '未知错误')}")
                        row['change'] = 'N/A'
                        error_count += 1
                else:
                    print(f"    ❌ {code} 无法获取数据")
                    row['change'] = 'N/A'
                    error_count += 1
                
                # 避免请求过于频繁
                time.sleep(0.5)
        
        # 保存修复后的文件
        if fixed_count > 0:
            # 备份原文件
            backup_file = f"{csv_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            import shutil
            shutil.copy2(csv_file, backup_file)
            print(f"📁 原文件已备份为: {backup_file}")
            
            # 保存修复后的数据
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                if data:
                    writer = csv.DictWriter(f, fieldnames=data[0].keys())
                    writer.writeheader()
                    writer.writerows(data)
            
            print(f"✅ {csv_file} 修复完成")
            print(f"  📊 修复成功: {fixed_count} 只股票")
            print(f"  ❌ 修复失败: {error_count} 只股票")
        else:
            print(f"ℹ️ {csv_file} 无数据需要修复")

def main():
    """主函数"""
    print("🔧 股票涨跌幅数据修复工具")
    print("=" * 50)
    print("🎯 检查并修复异常的涨跌幅数据")
    print("📊 使用可靠的数据源重新获取")
    print("=" * 50)
    
    fixer = StockDataFixer()
    
    # 修复CSV文件
    csv_files = ['popularity.csv', 'soaring.csv']
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"\n📁 处理文件: {csv_file}")
            fixer.fix_csv_data(csv_file)
        else:
            print(f"⚠️ 文件不存在: {csv_file}")
    
    print(f"\n🎉 数据修复完成！")
    print(f"💡 异常的涨跌幅数据已修复")
    print(f"🌐 HTML界面将显示正确的涨跌幅数据")

if __name__ == "__main__":
    main()

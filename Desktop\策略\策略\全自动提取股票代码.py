import sys
import subprocess

# 自动安装依赖
def install(package):
    try:
        # 先尝试使用镜像源安装
        subprocess.check_call([sys.executable, "-m", "pip", "install", package, "-i", "https://pypi.tuna.tsinghua.edu.cn/simple/"])
    except:
        try:
            # 如果清华源失败，尝试阿里云源
            subprocess.check_call([sys.executable, "-m", "pip", "install", package, "-i", "https://mirrors.aliyun.com/pypi/simple/"])
        except:
            # 最后尝试默认源
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.chrome.options import Options
    from webdriver_manager.chrome import ChromeDriverManager
except ImportError:
    install("selenium")
    install("webdriver-manager")
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.chrome.options import Options
    from webdriver_manager.chrome import ChromeDriverManager

import time

def get_all_stock_codes():
    chrome_options = Options()
    # chrome_options.add_argument('--headless')  # 先不用无头模式，方便调试
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1920,1080')
    # 隐藏Chrome警告信息
    chrome_options.add_argument('--log-level=3')  # 只显示致命错误
    chrome_options.add_argument('--silent')
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])

    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
    driver.get("https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock")
    print("页面加载中...")
    time.sleep(15)  # 增加等待时间，确保页面完全加载
    
    # 等待页面元素出现
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    
    try:
        # 等待表格加载
        WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "table, .el-table, [class*='table']"))
        )
        print("页面元素已加载完成")
    except:
        print("等待超时，继续执行...")

    codes = set()
    last_count = 0
    scroll_pause_time = 2
    max_attempts = 20
    attempts = 0

    print("开始提取股票代码...")
    
    while attempts < max_attempts:
        attempts += 1
        print(f"第{attempts}次尝试，当前已找到{len(codes)}个代码")
        
        # 滚动到底部
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(scroll_pause_time)

        # 尝试多种选择器来查找股票代码
        selectors = [
            ".el-table__body-wrapper tbody tr",
            "table tbody tr", 
            ".ranking-table tbody tr",
            "[class*='table'] tbody tr",
            ".el-table tbody tr",
            "tbody tr",
            "tr"
        ]
        
        rows = []
        for selector in selectors:
            try:
                rows = driver.find_elements(By.CSS_SELECTOR, selector)
                if rows:
                    print(f"使用选择器 '{selector}' 找到 {len(rows)} 行数据")
                    break
            except:
                continue
        
        if not rows:
            print("未找到表格行，尝试其他方法...")
            # 尝试查找所有包含数字的文本
            page_text = driver.page_source
            import re
            potential_codes = re.findall(r'\b(\d{6})\b', page_text)
            print(f"从页面源码中找到 {len(potential_codes)} 个6位数字")
            for code in potential_codes:
                if code.startswith(('00', '30', '60')):  # 股票代码前缀
                    codes.add(code)
                    print(f"从源码提取到股票代码: {code}")
            
            # 尝试查找所有文本内容
            all_text = driver.find_element(By.TAG_NAME, "body").text
            print(f"页面文本长度: {len(all_text)}")
            text_codes = re.findall(r'\b(\d{6})\b', all_text)
            for code in text_codes:
                if code.startswith(('00', '30', '60')):
                    codes.add(code)
                    print(f"从文本提取到股票代码: {code}")
            break
        
        for row in rows:
            try:
                tds = row.find_elements(By.TAG_NAME, "td")
                if len(tds) > 1:
                    code = tds[1].text.strip()
                    if code.isdigit() and len(code) == 6:
                        codes.add(code)
                        print(f"找到股票代码: {code}")
            except:
                continue

        # 判断是否还有新数据加载
        if len(codes) == last_count:
            print("连续两次未发现新代码，停止搜索")
            break
        last_count = len(codes)

    driver.quit()
    return list(codes)

if __name__ == "__main__":
    try:
        print("开始运行股票代码提取脚本...")
        codes = get_all_stock_codes()
        print(f"共提取到{len(codes)}个股票代码")
        
        if codes:
            with open("codes.txt", "w", encoding="utf-8") as f:
                for code in codes:
                    f.write(code + "\n")
            print("已保存到 codes.txt")
        else:
            print("未提取到任何股票代码，请检查网页结构或网络连接")
            
    except Exception as e:
        print(f"脚本运行出错: {e}")
        import traceback
        traceback.print_exc() 
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
from datetime import datetime
import subprocess
import time

# 页面配置
st.set_page_config(
    page_title="股票数据采集系统",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #667eea;
    }
    
    .success-card {
        border-left-color: #28a745;
    }
    
    .warning-card {
        border-left-color: #ffc107;
    }
    
    .info-card {
        border-left-color: #17a2b8;
    }
    
    .stButton > button {
        width: 100%;
        border-radius: 20px;
        border: none;
        padding: 0.5rem 1rem;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
</style>
""", unsafe_allow_html=True)

# 数据加载函数
@st.cache_data
def load_stock_data():
    """加载股票数据"""
    data = {
        'codes': [],
        'cached_stocks': [],
        'popularity': pd.DataFrame(),
        'soaring': pd.DataFrame()
    }
    
    try:
        # 加载股票代码
        if os.path.exists('codes.txt'):
            with open('codes.txt', 'r', encoding='utf-8') as f:
                data['codes'] = [line.strip() for line in f if line.strip()]
        
        # 加载缓存数据
        if os.path.exists('stock_cache.json'):
            with open('stock_cache.json', 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                data['cached_stocks'] = cache_data.get('stocks', [])
        
        # 加载人气榜数据
        if os.path.exists('popularity.csv'):
            data['popularity'] = pd.read_csv('popularity.csv')
        
        # 加载飙升榜数据
        if os.path.exists('soaring.csv'):
            data['soaring'] = pd.read_csv('soaring.csv')
            
    except Exception as e:
        st.error(f"数据加载失败: {e}")
    
    return data

def create_market_distribution_chart(codes):
    """创建市场分布图"""
    if not codes:
        return go.Figure()
    
    sh_count = len([code for code in codes if code.startswith('6')])
    sz_count = len([code for code in codes if code.startswith(('00', '30'))])
    
    fig = go.Figure(data=[go.Pie(
        labels=['沪市 (SH)', '深市 (SZ)'],
        values=[sh_count, sz_count],
        hole=0.4,
        marker_colors=['#e74c3c', '#2ecc71']
    )])
    
    fig.update_layout(
        title="市场分布",
        font=dict(size=14),
        showlegend=True,
        height=400
    )
    
    return fig

def create_data_comparison_chart(data):
    """创建数据对比图"""
    categories = ['总股票', '人气榜', '飙升榜']
    values = [
        len(data['codes']),
        len(data['popularity']),
        len(data['soaring'])
    ]
    
    fig = go.Figure(data=[
        go.Bar(
            x=categories,
            y=values,
            marker_color=['#3498db', '#f39c12', '#e74c3c'],
            text=values,
            textposition='auto'
        )
    ])
    
    fig.update_layout(
        title="数据量统计",
        xaxis_title="数据类型",
        yaxis_title="数量",
        font=dict(size=14),
        height=400
    )
    
    return fig

def create_stock_trend_chart():
    """创建股票趋势图（模拟数据）"""
    dates = pd.date_range(start='2025-01-01', end='2025-07-30', freq='D')
    stock_counts = [50 + i * 0.5 + (i % 10) * 2 for i in range(len(dates))]
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=dates,
        y=stock_counts,
        mode='lines+markers',
        name='股票数量趋势',
        line=dict(color='#667eea', width=3),
        marker=dict(size=6)
    ))
    
    fig.update_layout(
        title="股票数据采集趋势",
        xaxis_title="日期",
        yaxis_title="股票数量",
        font=dict(size=14),
        height=400,
        hovermode='x unified'
    )
    
    return fig

def main():
    # 主标题
    st.markdown("""
    <div class="main-header">
        <h1>📊 股票数据采集系统</h1>
        <p>智能化股票数据采集与分析平台</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.header("🎮 控制面板")
        
        # 数据采集按钮
        if st.button("🚀 开始采集", type="primary"):
            with st.spinner("正在执行数据采集..."):
                try:
                    # 这里可以调用采集脚本
                    st.success("数据采集完成！")
                    st.rerun()
                except Exception as e:
                    st.error(f"采集失败: {e}")
        
        # 数据处理按钮
        if st.button("⚙️ 处理数据"):
            with st.spinner("正在处理数据..."):
                try:
                    # 这里可以调用处理脚本
                    st.success("数据处理完成！")
                    st.rerun()
                except Exception as e:
                    st.error(f"处理失败: {e}")
        
        # 生成报告按钮
        if st.button("📊 生成报告"):
            with st.spinner("正在生成报告..."):
                try:
                    # 这里可以调用报告脚本
                    st.success("报告生成完成！")
                except Exception as e:
                    st.error(f"报告生成失败: {e}")
        
        # 刷新数据按钮
        if st.button("🔄 刷新数据"):
            st.cache_data.clear()
            st.success("数据已刷新！")
            st.rerun()
        
        st.divider()
        
        # 系统信息
        st.header("ℹ️ 系统信息")
        st.info(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        st.info("系统状态: 🟢 正常运行")
        
        # 文件状态检查
        st.header("📁 文件状态")
        files_to_check = ['codes.txt', 'popularity.csv', 'soaring.csv', 'stock_cache.json']
        for file in files_to_check:
            if os.path.exists(file):
                size = os.path.getsize(file)
                st.success(f"✅ {file} ({size} bytes)")
            else:
                st.error(f"❌ {file} (不存在)")
    
    # 加载数据
    data = load_stock_data()
    
    # 主要指标卡片
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="📈 总股票数",
            value=len(data['codes']),
            delta=f"缓存: {len(data['cached_stocks'])}"
        )
    
    with col2:
        sh_count = len([code for code in data['codes'] if code.startswith('6')])
        st.metric(
            label="🔴 沪市股票",
            value=sh_count,
            delta=f"{sh_count/len(data['codes'])*100:.1f}%" if data['codes'] else "0%"
        )
    
    with col3:
        sz_count = len([code for code in data['codes'] if code.startswith(('00', '30'))])
        st.metric(
            label="🟢 深市股票",
            value=sz_count,
            delta=f"{sz_count/len(data['codes'])*100:.1f}%" if data['codes'] else "0%"
        )
    
    with col4:
        total_rankings = len(data['popularity']) + len(data['soaring'])
        st.metric(
            label="📊 榜单数据",
            value=total_rankings,
            delta=f"人气榜: {len(data['popularity'])}, 飙升榜: {len(data['soaring'])}"
        )
    
    st.divider()
    
    # 图表区域
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📊 市场分布")
        market_chart = create_market_distribution_chart(data['codes'])
        st.plotly_chart(market_chart, use_container_width=True)
    
    with col2:
        st.subheader("📈 数据量统计")
        comparison_chart = create_data_comparison_chart(data)
        st.plotly_chart(comparison_chart, use_container_width=True)
    
    # 趋势图
    st.subheader("📈 采集趋势分析")
    trend_chart = create_stock_trend_chart()
    st.plotly_chart(trend_chart, use_container_width=True)
    
    # 数据表格
    st.subheader("📋 实时数据")
    
    tab1, tab2, tab3 = st.tabs(["股票代码", "人气榜", "飙升榜"])
    
    with tab1:
        if data['codes']:
            # 创建股票代码DataFrame
            codes_df = pd.DataFrame({
                '股票代码': data['codes'][:20],  # 显示前20个
                '市场': ['沪市' if code.startswith('6') else '深市' 
                        for code in data['codes'][:20]],
                '格式化代码': [f"{code}.SH" if code.startswith('6') else f"{code}.SZ" 
                             for code in data['codes'][:20]]
            })
            st.dataframe(codes_df, use_container_width=True)
        else:
            st.info("暂无股票代码数据")
    
    with tab2:
        if not data['popularity'].empty:
            st.dataframe(data['popularity'].head(20), use_container_width=True)
        else:
            st.info("暂无人气榜数据")
    
    with tab3:
        if not data['soaring'].empty:
            st.dataframe(data['soaring'].head(20), use_container_width=True)
        else:
            st.info("暂无飙升榜数据")
    
    # 数据分析摘要
    st.subheader("📊 数据分析摘要")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 📈 采集统计")
        if data['codes']:
            coverage_rate = len(set(data['popularity']['code']) & set(data['codes'])) / len(data['popularity']) * 100 if not data['popularity'].empty else 0
            st.write(f"- 数据覆盖率: {coverage_rate:.1f}%")
            st.write(f"- 沪深比例: {sh_count}:{sz_count}")
            st.write(f"- 缓存命中率: {len(data['cached_stocks'])/len(data['codes'])*100:.1f}%")
        else:
            st.info("暂无数据统计")
    
    with col2:
        st.markdown("### 🎯 系统建议")
        recommendations = []
        
        if len(data['codes']) < 50:
            recommendations.append("📌 建议增加股票代码采集范围")
        
        if data['popularity'].empty or data['soaring'].empty:
            recommendations.append("📌 建议完善榜单数据采集")
        
        if not recommendations:
            recommendations.append("✅ 系统运行良好，数据完整")
        
        for rec in recommendations:
            st.write(rec)
    
    # 页脚
    st.divider()
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 1rem;">
        <p>📊 股票数据采集系统 v1.0 | 最后更新: {}</p>
    </div>
    """.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), unsafe_allow_html=True)

if __name__ == "__main__":
    main()

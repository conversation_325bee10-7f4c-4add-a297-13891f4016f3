#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量清理文件工具
删除多余和无用的文件，保留核心功能文件
"""

import os
import glob
from datetime import datetime

def get_file_categories():
    """定义文件分类"""
    
    # 核心功能文件（必须保留）
    core_files = [
        '可视化主程序.py',
        '后台采集.py', 
        '智能选股系统.py',
        '选股报告生成器.py',
        '行业板块分类.py',
        '增强数据显示.py',
        '一键选股.py'
    ]
    
    # 数据文件（保留最新的）
    data_files = [
        'popularity.csv',
        'soaring.csv',
        'stock_names_cache.json'
    ]
    
    # 报告文件（保留最新的）
    report_files = [
        '智能选股分析报告.html',
        '股票行业整合报告.html',
        '股票分析报告.html'
    ]
    
    # 结果文件（保留最新的）
    result_files = [
        '选股结果.json',
        '推荐股票汇总.csv'
    ]
    
    # 测试和临时文件（可以删除）
    temp_files_patterns = [
        '*测试*.py',
        '*诊断*.py',
        '*验证*.py',
        '*检查*.py',
        '简单*.py',
        '快速*.py',
        '最终*.py',
        'test_*.py',
        'debug_*.py',
        '生成大量测试数据.py',
        '批量清理文件.py'  # 清理完成后删除自己
    ]
    
    # 备份和日志文件（可以删除）
    backup_files_patterns = [
        '*.bak',
        '*.backup',
        '*.old',
        '*.log',
        '*.tmp',
        '*_backup.*',
        '*_old.*'
    ]
    
    # 重复的HTML报告（保留最新的）
    duplicate_reports_patterns = [
        '股票分析报告_*.html',
        '测试报告*.html',
        '可视化界面*.html'
    ]
    
    return {
        'core': core_files,
        'data': data_files, 
        'reports': report_files,
        'results': result_files,
        'temp_patterns': temp_files_patterns,
        'backup_patterns': backup_files_patterns,
        'duplicate_patterns': duplicate_reports_patterns
    }

def scan_files():
    """扫描当前目录的所有文件"""
    all_files = []
    
    # 获取所有Python文件
    py_files = glob.glob('*.py')
    all_files.extend(py_files)
    
    # 获取所有数据文件
    data_files = glob.glob('*.csv') + glob.glob('*.json')
    all_files.extend(data_files)
    
    # 获取所有HTML文件
    html_files = glob.glob('*.html')
    all_files.extend(html_files)
    
    # 获取其他文件
    other_files = glob.glob('*.txt') + glob.glob('*.log') + glob.glob('*.bak') + glob.glob('*.tmp')
    all_files.extend(other_files)
    
    return list(set(all_files))  # 去重

def categorize_files(all_files, categories):
    """对文件进行分类"""
    
    keep_files = set()
    delete_files = set()
    
    # 保留核心文件
    for file in categories['core']:
        if file in all_files:
            keep_files.add(file)
    
    # 保留数据文件
    for file in categories['data']:
        if file in all_files:
            keep_files.add(file)
    
    # 保留报告文件
    for file in categories['reports']:
        if file in all_files:
            keep_files.add(file)
    
    # 保留结果文件
    for file in categories['results']:
        if file in all_files:
            keep_files.add(file)
    
    # 标记临时文件为删除
    for pattern in categories['temp_patterns']:
        matching_files = glob.glob(pattern)
        delete_files.update(matching_files)
    
    # 标记备份文件为删除
    for pattern in categories['backup_patterns']:
        matching_files = glob.glob(pattern)
        delete_files.update(matching_files)
    
    # 标记重复报告为删除
    for pattern in categories['duplicate_patterns']:
        matching_files = glob.glob(pattern)
        delete_files.update(matching_files)
    
    # 其他未分类的文件
    other_files = set(all_files) - keep_files - delete_files
    
    return keep_files, delete_files, other_files

def show_cleanup_plan(keep_files, delete_files, other_files):
    """显示清理计划"""
    print("📋 文件清理计划")
    print("=" * 60)
    
    print(f"\n✅ 保留文件 ({len(keep_files)} 个):")
    for file in sorted(keep_files):
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024
            print(f"  📁 {file:<30} ({size:.1f} KB)")
    
    print(f"\n❌ 删除文件 ({len(delete_files)} 个):")
    total_delete_size = 0
    for file in sorted(delete_files):
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024
            total_delete_size += size
            print(f"  🗑️ {file:<30} ({size:.1f} KB)")
    
    if other_files:
        print(f"\n❓ 其他文件 ({len(other_files)} 个) - 需要手动确认:")
        for file in sorted(other_files):
            if os.path.exists(file):
                size = os.path.getsize(file) / 1024
                print(f"  ❓ {file:<30} ({size:.1f} KB)")
    
    print(f"\n📊 清理统计:")
    print(f"  保留文件: {len(keep_files)} 个")
    print(f"  删除文件: {len(delete_files)} 个")
    print(f"  释放空间: {total_delete_size:.1f} KB")
    
    return total_delete_size

def execute_cleanup(delete_files, confirm=True):
    """执行文件清理"""
    
    if confirm:
        print(f"\n⚠️ 确认删除 {len(delete_files)} 个文件？")
        choice = input("输入 'yes' 确认删除，其他任意键取消: ").strip().lower()
        if choice != 'yes':
            print("❌ 清理已取消")
            return False
    
    print(f"\n🗑️ 开始删除文件...")
    deleted_count = 0
    failed_count = 0
    
    for file in delete_files:
        try:
            if os.path.exists(file):
                os.remove(file)
                print(f"  ✅ 已删除: {file}")
                deleted_count += 1
            else:
                print(f"  ⚠️ 文件不存在: {file}")
        except Exception as e:
            print(f"  ❌ 删除失败: {file} - {str(e)}")
            failed_count += 1
    
    print(f"\n📊 清理结果:")
    print(f"  成功删除: {deleted_count} 个文件")
    print(f"  删除失败: {failed_count} 个文件")
    
    return deleted_count > 0

def create_file_inventory():
    """创建文件清单"""
    print(f"\n📝 创建文件清单...")
    
    try:
        with open('文件清单.txt', 'w', encoding='utf-8') as f:
            f.write(f"股票数据采集系统 - 文件清单\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"=" * 50 + "\n\n")
            
            # 核心功能文件
            f.write("🔧 核心功能文件:\n")
            core_files = [
                '可视化主程序.py - GUI主界面',
                '后台采集.py - 股票数据采集',
                '智能选股系统.py - 多策略选股分析',
                '选股报告生成器.py - HTML报告生成',
                '行业板块分类.py - 行业智能分类',
                '增强数据显示.py - 数据整合显示',
                '一键选股.py - 完整选股流程'
            ]
            
            for file_desc in core_files:
                f.write(f"  • {file_desc}\n")
            
            # 数据文件
            f.write(f"\n📊 数据文件:\n")
            f.write(f"  • popularity.csv - 人气榜股票数据\n")
            f.write(f"  • soaring.csv - 飙升榜股票数据\n")
            f.write(f"  • stock_names_cache.json - 股票名称缓存\n")
            
            # 报告文件
            f.write(f"\n📋 报告文件:\n")
            f.write(f"  • 智能选股分析报告.html - 选股分析报告\n")
            f.write(f"  • 股票行业整合报告.html - 行业分析报告\n")
            f.write(f"  • 股票分析报告.html - 基础分析报告\n")
            
            # 结果文件
            f.write(f"\n📈 结果文件:\n")
            f.write(f"  • 选股结果.json - 详细选股数据\n")
            f.write(f"  • 推荐股票汇总.csv - 推荐股票表格\n")
            
            # 使用说明
            f.write(f"\n💡 使用说明:\n")
            f.write(f"  1. 运行 '可视化主程序.py' 启动GUI界面\n")
            f.write(f"  2. 点击 '🔄 开始数据采集' 获取最新数据\n")
            f.write(f"  3. 点击 '🎯 智能选股分析' 进行选股\n")
            f.write(f"  4. 点击 '🏆 查看选股结果' 查看结果\n")
            f.write(f"  5. 或使用 '⚡ 一键选股流程' 执行完整流程\n")
        
        print("✅ 文件清单已创建: 文件清单.txt")
        
    except Exception as e:
        print(f"❌ 创建文件清单失败: {str(e)}")

def main():
    """主函数"""
    print("🗑️ 批量文件清理工具")
    print("清理多余和无用的文件，保持项目整洁")
    print("=" * 60)
    
    # 获取文件分类规则
    categories = get_file_categories()
    
    # 扫描所有文件
    print("📁 扫描当前目录文件...")
    all_files = scan_files()
    print(f"发现 {len(all_files)} 个文件")
    
    # 对文件进行分类
    keep_files, delete_files, other_files = categorize_files(all_files, categories)
    
    # 显示清理计划
    total_delete_size = show_cleanup_plan(keep_files, delete_files, other_files)
    
    # 处理其他文件 - 自动删除明显的临时文件
    if other_files:
        print(f"\n❓ 发现 {len(other_files)} 个未分类文件，自动处理:")
        auto_delete_patterns = ['codes.txt', 'stock_cache.json', '股票板块整合报告.html']

        for file in sorted(other_files):
            # 自动删除明显的临时文件
            should_delete = any(pattern in file for pattern in auto_delete_patterns)
            should_delete = should_delete or file.endswith('.txt') or 'cache' in file.lower()

            if should_delete:
                print(f"  🗑️ 自动删除: {file}")
                delete_files.add(file)
            else:
                print(f"  ✅ 保留: {file}")
                keep_files.add(file)
    
    # 执行清理
    if delete_files:
        success = execute_cleanup(delete_files, confirm=True)
        
        if success:
            print(f"\n🎉 文件清理完成！")
            print(f"项目目录已整理，保留了核心功能文件")
            
            # 创建文件清单
            create_file_inventory()
            
            print(f"\n📋 当前保留的核心文件:")
            for file in sorted(keep_files):
                if os.path.exists(file):
                    print(f"  ✅ {file}")
        else:
            print(f"\n❌ 文件清理已取消或失败")
    else:
        print(f"\n💡 没有需要删除的文件")
    
    print(f"\n🏁 清理工具运行完成")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 清理过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

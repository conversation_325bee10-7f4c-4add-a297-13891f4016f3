#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化系统启动器
股票数据采集系统的图形化界面启动器
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import sys
import os
import threading
import webbrowser
from datetime import datetime
import json

class StockSystemGUI:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        self.check_system_status()
        
    def setup_window(self):
        """设置窗口"""
        self.root.title("🚀 股票数据采集系统 - 可视化启动器")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 设置窗口居中
        self.center_window()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        
        # 配置按钮样式
        style.configure("Action.TButton", 
                       font=("Microsoft YaHei", 11, "bold"),
                       padding=(10, 8))
        
        # 配置标签样式
        style.configure("Title.TLabel",
                       font=("Microsoft YaHei", 16, "bold"),
                       foreground="#2c3e50")
        
        style.configure("Status.TLabel",
                       font=("Microsoft YaHei", 10),
                       foreground="#27ae60")
        
        style.configure("Error.TLabel",
                       font=("Microsoft YaHei", 10),
                       foreground="#e74c3c")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, 
                               text="🚀 股票数据采集系统",
                               style="Title.TLabel")
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # 版本和时间信息
        info_text = f"版本: v2.0 | 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        info_label = ttk.Label(main_frame, text=info_text, font=("Microsoft YaHei", 9))
        info_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # 左侧功能按钮区域
        self.create_function_buttons(main_frame)
        
        # 右侧状态和日志区域
        self.create_status_area(main_frame)
        
        # 底部状态栏
        self.create_status_bar(main_frame)
    
    def create_function_buttons(self, parent):
        """创建功能按钮"""
        # 功能按钮框架
        button_frame = ttk.LabelFrame(parent, text="📋 系统功能", padding="15")
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 按钮配置
        buttons = [
            ("🔇 后台数据采集", self.run_background_collection, "#3498db"),
            ("📊 获取股票名称", self.get_stock_names, "#9b59b6"),
            ("📈 获取涨跌幅数据", self.get_price_changes, "#e67e22"),
            ("📄 生成分析报告", self.generate_report, "#27ae60"),
            ("🌐 启动数据服务器", self.start_server, "#f39c12"),
            ("🖥️ 打开可视化界面", self.open_web_interface, "#1abc9c"),
            ("📁 查看数据文件", self.view_data_files, "#34495e"),
            ("⚙️ 系统状态检查", self.check_system_status, "#95a5a6")
        ]
        
        self.buttons = {}
        for i, (text, command, color) in enumerate(buttons):
            btn = tk.Button(button_frame, 
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=15,
                           pady=8,
                           cursor="hand2",
                           activebackground=self.darken_color(color))
            btn.grid(row=i, column=0, sticky=(tk.W, tk.E), pady=3)
            button_frame.columnconfigure(0, weight=1)
            
            # 保存按钮引用
            self.buttons[text] = btn
    
    def create_status_area(self, parent):
        """创建状态和日志区域"""
        # 右侧框架
        right_frame = ttk.Frame(parent)
        right_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        
        # 系统状态框架
        status_frame = ttk.LabelFrame(right_frame, text="📊 系统状态", padding="10")
        status_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        # 状态标签
        ttk.Label(status_frame, text="Python版本:").grid(row=0, column=0, sticky=tk.W)
        self.python_version_label = ttk.Label(status_frame, text="检查中...", style="Status.TLabel")
        self.python_version_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="依赖包状态:").grid(row=1, column=0, sticky=tk.W)
        self.dependencies_label = ttk.Label(status_frame, text="检查中...", style="Status.TLabel")
        self.dependencies_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="核心文件:").grid(row=2, column=0, sticky=tk.W)
        self.files_label = ttk.Label(status_frame, text="检查中...", style="Status.TLabel")
        self.files_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(right_frame, text="📝 操作日志", padding="10")
        log_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, 
                                                 height=15, 
                                                 font=("Consolas", 9),
                                                 wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        clear_btn = ttk.Button(log_frame, text="清空日志", command=self.clear_log)
        clear_btn.grid(row=1, column=0, sticky=tk.E, pady=(5, 0))
    
    def create_status_bar(self, parent):
        """创建底部状态栏"""
        status_bar = ttk.Frame(parent)
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(20, 0))
        status_bar.columnconfigure(1, weight=1)
        
        # 状态指示器
        self.status_indicator = tk.Label(status_bar, 
                                        text="● 就绪", 
                                        fg="#27ae60",
                                        font=("Microsoft YaHei", 9, "bold"))
        self.status_indicator.grid(row=0, column=0, sticky=tk.W)
        
        # 进度条
        self.progress = ttk.Progressbar(status_bar, mode='indeterminate')
        self.progress.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(20, 20))
        
        # 退出按钮
        exit_btn = tk.Button(status_bar, 
                            text="🚪 退出系统",
                            command=self.exit_application,
                            font=("Microsoft YaHei", 9),
                            bg="#e74c3c",
                            fg="white",
                            relief="raised",
                            bd=1,
                            padx=10,
                            pady=3,
                            cursor="hand2")
        exit_btn.grid(row=0, column=2, sticky=tk.E)
    
    def darken_color(self, color):
        """使颜色变暗"""
        color_map = {
            "#3498db": "#2980b9",
            "#9b59b6": "#8e44ad",
            "#e67e22": "#d35400",
            "#27ae60": "#229954",
            "#f39c12": "#e67e22",
            "#1abc9c": "#16a085",
            "#34495e": "#2c3e50",
            "#95a5a6": "#7f8c8d"
        }
        return color_map.get(color, color)
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 根据级别设置颜色
        if level == "ERROR":
            self.log_text.tag_add("error", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("error", foreground="#e74c3c")
        elif level == "SUCCESS":
            self.log_text.tag_add("success", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("success", foreground="#27ae60")
        elif level == "WARNING":
            self.log_text.tag_add("warning", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("warning", foreground="#f39c12")
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空", "INFO")
    
    def set_status(self, status, color="#27ae60"):
        """设置状态指示器"""
        self.status_indicator.config(text=f"● {status}", fg=color)
    
    def start_progress(self):
        """开始进度条动画"""
        self.progress.start(10)
    
    def stop_progress(self):
        """停止进度条动画"""
        self.progress.stop()
    
    def run_script_async(self, script_name, description, callback=None):
        """异步运行脚本"""
        def run():
            self.set_status("运行中...", "#f39c12")
            self.start_progress()
            self.log_message(f"开始执行: {description}", "INFO")
            
            try:
                if not os.path.exists(script_name):
                    raise FileNotFoundError(f"脚本文件不存在: {script_name}")
                
                # 运行脚本
                result = subprocess.run([sys.executable, script_name],
                                      capture_output=True,
                                      text=True,
                                      cwd=os.getcwd(),
                                      encoding='gbk',
                                      errors='ignore')
                
                if result.returncode == 0:
                    self.log_message(f"{description} 执行成功", "SUCCESS")
                    if callback:
                        callback(True, result.stdout)
                else:
                    self.log_message(f"{description} 执行失败: {result.stderr}", "ERROR")
                    if callback:
                        callback(False, result.stderr)
                        
            except Exception as e:
                error_msg = f"{description} 执行出错: {str(e)}"
                self.log_message(error_msg, "ERROR")
                if callback:
                    callback(False, str(e))
            
            finally:
                self.stop_progress()
                self.set_status("就绪", "#27ae60")
        
        # 在新线程中运行
        thread = threading.Thread(target=run, daemon=True)
        thread.start()
    
    # 功能按钮回调函数
    def run_background_collection(self):
        """后台数据采集"""
        self.run_script_async("后台采集.py", "后台数据采集")
    
    def get_stock_names(self):
        """获取股票名称"""
        self.run_script_async("股票名称获取工具.py", "股票名称获取")
    
    def get_price_changes(self):
        """获取涨跌幅数据"""
        self.run_script_async("股票涨跌幅获取工具.py", "涨跌幅数据获取")
    
    def generate_report(self):
        """生成分析报告"""
        def on_complete(success, output):
            if success:
                # 询问是否打开报告
                if messagebox.askyesno("报告生成完成", "分析报告已生成完成！\n是否立即打开HTML报告？"):
                    self.open_html_report()
        
        self.run_script_async("智能数据分析报告.py", "智能分析报告生成", on_complete)
    
    def start_server(self):
        """启动数据服务器"""
        try:
            self.log_message("启动数据服务器...", "INFO")
            subprocess.Popen([sys.executable, "数据服务器.py"], cwd=os.getcwd())
            self.log_message("数据服务器已启动", "SUCCESS")
            
            # 询问是否打开界面
            if messagebox.askyesno("服务器启动", "数据服务器已启动！\n是否立即打开可视化界面？"):
                self.open_web_interface()
                
        except Exception as e:
            self.log_message(f"启动服务器失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"启动服务器失败:\n{str(e)}")
    
    def open_web_interface(self):
        """打开可视化界面"""
        try:
            url = "http://localhost:8080/可视化界面_实时版.html"
            webbrowser.open(url)
            self.log_message("已打开可视化界面", "SUCCESS")
        except Exception as e:
            self.log_message(f"打开界面失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"打开界面失败:\n{str(e)}")
    
    def open_html_report(self):
        """打开HTML报告"""
        try:
            report_path = os.path.abspath("股票分析报告.html")
            webbrowser.open(f"file:///{report_path}")
            self.log_message("已打开分析报告", "SUCCESS")
        except Exception as e:
            self.log_message(f"打开报告失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"打开报告失败:\n{str(e)}")
    
    def view_data_files(self):
        """查看数据文件"""
        self.log_message("检查数据文件状态...", "INFO")
        
        data_files = [
            ("popularity.csv", "人气榜数据"),
            ("soaring.csv", "飙升榜数据"),
            ("codes.txt", "股票代码库"),
            ("stock_names_cache.json", "股票名称缓存"),
            ("股票分析报告.html", "分析报告")
        ]
        
        status_info = "📁 数据文件状态:\n" + "="*40 + "\n"
        
        for file_path, description in data_files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                if size < 1024:
                    size_str = f"{size} B"
                elif size < 1024 * 1024:
                    size_str = f"{size/1024:.1f} KB"
                else:
                    size_str = f"{size/(1024*1024):.1f} MB"
                
                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                status_info += f"✅ {description}: {size_str} ({mod_time.strftime('%m-%d %H:%M')})\n"
            else:
                status_info += f"❌ {description}: 不存在\n"
        
        # 显示在消息框中
        messagebox.showinfo("数据文件状态", status_info)
        self.log_message("数据文件状态检查完成", "SUCCESS")
    
    def check_system_status(self):
        """检查系统状态"""
        def check():
            self.log_message("检查系统状态...", "INFO")
            
            # 检查Python版本
            python_version = f"{sys.version.split()[0]}"
            self.python_version_label.config(text=python_version)
            
            # 检查依赖包
            required_modules = ["selenium", "requests", "matplotlib", "pandas", "numpy"]
            missing_modules = []
            
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError:
                    missing_modules.append(module)
            
            if missing_modules:
                deps_status = f"缺少: {', '.join(missing_modules)}"
                self.dependencies_label.config(text=deps_status, style="Error.TLabel")
            else:
                self.dependencies_label.config(text="全部已安装", style="Status.TLabel")
            
            # 检查核心文件
            core_files = [
                "后台采集.py", "股票名称获取工具.py", "股票涨跌幅获取工具.py",
                "智能数据分析报告.py", "数据服务器.py", "可视化界面_实时版.html"
            ]
            
            missing_files = [f for f in core_files if not os.path.exists(f)]
            
            if missing_files:
                files_status = f"缺少 {len(missing_files)} 个文件"
                self.files_label.config(text=files_status, style="Error.TLabel")
            else:
                self.files_label.config(text="全部存在", style="Status.TLabel")
            
            self.log_message("系统状态检查完成", "SUCCESS")
        
        # 在新线程中检查
        thread = threading.Thread(target=check, daemon=True)
        thread.start()
    
    def exit_application(self):
        """退出应用程序"""
        if messagebox.askyesno("确认退出", "确定要退出股票数据采集系统吗？"):
            self.log_message("正在退出系统...", "INFO")
            self.root.quit()
            self.root.destroy()

def main():
    """主函数"""
    root = tk.Tk()
    app = StockSystemGUI(root)
    
    # 添加欢迎消息
    app.log_message("欢迎使用股票数据采集系统！", "SUCCESS")
    app.log_message("点击左侧按钮开始使用各项功能", "INFO")
    
    root.mainloop()

if __name__ == "__main__":
    main()

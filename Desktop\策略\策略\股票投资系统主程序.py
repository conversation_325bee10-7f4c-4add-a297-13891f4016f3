#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票智能投资系统 - 主程序
简洁一体化界面设计
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import sys
import os
import threading
import json
import csv
from datetime import datetime
import time

class StockInvestmentSystem:
    def __init__(self, root):
        self.root = root
        self.current_module = "选股"  # 当前显示的模块
        self.setup_window()
        self.create_main_interface()
        self.load_initial_data()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("🚀 股票智能投资系统")
        self.root.geometry("1600x1000")
        self.root.resizable(True, True)
        self.root.configure(bg="#f8f9fa")
        
        # 窗口居中
        self.center_window()
        
        # 设置样式
        self.setup_styles()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 1600
        height = 1000
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def setup_styles(self):
        """设置界面样式"""
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # 导航按钮样式
        self.style.configure("Nav.TButton",
                           font=("Microsoft YaHei", 12, "bold"),
                           padding=(20, 10))
        
        # 功能按钮样式
        self.style.configure("Action.TButton",
                           font=("Microsoft YaHei", 10, "bold"),
                           padding=(15, 8))
    
    def create_main_interface(self):
        """创建主界面"""
        # 主容器
        main_container = tk.Frame(self.root, bg="#f8f9fa")
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # 顶部导航栏
        self.create_navigation_bar(main_container)
        
        # 主要内容区域
        content_frame = tk.Frame(main_container, bg="#f8f9fa")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 左侧面板（包含快捷操作和系统信息）
        self.create_left_panel(content_frame)

        # 右侧主显示区域
        self.create_main_display_area(content_frame)
        
        # 底部状态栏
        self.create_status_bar(main_container)
    
    def create_navigation_bar(self, parent):
        """创建顶部导航栏"""
        nav_frame = tk.Frame(parent, bg="#2c3e50", height=70)
        nav_frame.pack(fill=tk.X)
        nav_frame.pack_propagate(False)
        
        # 系统标题
        title_label = tk.Label(nav_frame,
                              text="🚀 股票智能投资系统",
                              font=("Microsoft YaHei", 18, "bold"),
                              bg="#2c3e50",
                              fg="white")
        title_label.pack(side=tk.LEFT, padx=20, pady=15)
        
        # 导航按钮区域
        nav_buttons_frame = tk.Frame(nav_frame, bg="#2c3e50")
        nav_buttons_frame.pack(side=tk.RIGHT, padx=20, pady=10)
        
        # 导航按钮
        nav_buttons = [
            ("📊 选股", "选股", "#3498db"),
            ("🔍 分析", "分析", "#e74c3c"),
            ("📋 结果", "结果", "#27ae60"),
            ("💰 交易", "交易", "#f39c12"),
            ("⚙️ 配置", "配置", "#9b59b6")
        ]
        
        self.nav_buttons = {}
        for text, module, color in nav_buttons:
            btn = tk.Button(nav_buttons_frame,
                           text=text,
                           command=lambda m=module: self.switch_module(m),
                           font=("Microsoft YaHei", 11, "bold"),
                           bg=color if module == self.current_module else "#34495e",
                           fg="white",
                           relief="flat",
                           padx=15,
                           pady=8,
                           cursor="hand2")
            btn.pack(side=tk.LEFT, padx=5)
            self.nav_buttons[module] = btn
    
    def create_quick_panel(self, parent):
        """创建左侧快捷面板"""
        self.quick_frame = tk.Frame(parent, bg="#ecf0f1", width=280)
        self.quick_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        self.quick_frame.pack_propagate(False)
        
        # 快捷面板标题
        tk.Label(self.quick_frame,
                text="🎯 快捷操作",
                font=("Microsoft YaHei", 14, "bold"),
                bg="#ecf0f1",
                fg="#2c3e50").pack(pady=(15, 20))
        
        # 根据当前模块显示不同的快捷按钮
        self.update_quick_panel()
    
    def create_work_area(self, parent):
        """创建中央工作区"""
        self.work_frame = tk.Frame(parent, bg="white", relief="solid", bd=1)
        self.work_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # 工作区标题
        self.work_title = tk.Label(self.work_frame,
                                  text="📊 选股模块",
                                  font=("Microsoft YaHei", 16, "bold"),
                                  bg="white",
                                  fg="#2c3e50")
        self.work_title.pack(pady=(15, 10))
        
        # 工作区内容容器
        self.work_content = tk.Frame(self.work_frame, bg="white")
        self.work_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))
        
        # 根据当前模块显示不同内容
        self.update_work_area()
    
    def create_info_panel(self, parent):
        """创建右侧信息面板"""
        self.info_frame = tk.Frame(parent, bg="#ecf0f1", width=300)
        self.info_frame.pack(side=tk.RIGHT, fill=tk.Y)
        self.info_frame.pack_propagate(False)
        
        # 信息面板标题
        tk.Label(self.info_frame,
                text="📊 系统信息",
                font=("Microsoft YaHei", 14, "bold"),
                bg="#ecf0f1",
                fg="#2c3e50").pack(pady=(15, 20))
        
        # 实时时间
        self.time_label = tk.Label(self.info_frame,
                                  text=f"🕐 {datetime.now().strftime('%H:%M:%S')}",
                                  font=("Microsoft YaHei", 12),
                                  bg="#ecf0f1",
                                  fg="#34495e")
        self.time_label.pack(pady=5)
        
        # 系统状态
        status_frame = tk.LabelFrame(self.info_frame,
                                   text="📊 系统状态",
                                   font=("Microsoft YaHei", 10, "bold"),
                                   bg="#ecf0f1",
                                   fg="#2c3e50")
        status_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.status_text = scrolledtext.ScrolledText(status_frame,
                                                   height=8,
                                                   width=30,
                                                   font=("Consolas", 9),
                                                   bg="#2c3e50",
                                                   fg="#00ff00",
                                                   insertbackground="white")
        self.status_text.pack(padx=10, pady=10)
        
        # 快速统计
        stats_frame = tk.LabelFrame(self.info_frame,
                                  text="📈 快速统计",
                                  font=("Microsoft YaHei", 10, "bold"),
                                  bg="#ecf0f1",
                                  fg="#2c3e50")
        stats_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.stats_labels = {}
        stats_items = [
            ("热门股票", "0"),
            ("消息股票", "0"),
            ("AI推荐", "0"),
            ("持仓股票", "0")
        ]
        
        for item, value in stats_items:
            frame = tk.Frame(stats_frame, bg="#ecf0f1")
            frame.pack(fill=tk.X, padx=10, pady=2)
            
            tk.Label(frame, text=f"{item}:", 
                    font=("Microsoft YaHei", 9),
                    bg="#ecf0f1", fg="#34495e").pack(side=tk.LEFT)
            
            label = tk.Label(frame, text=value,
                           font=("Microsoft YaHei", 9, "bold"),
                           bg="#ecf0f1", fg="#e74c3c")
            label.pack(side=tk.RIGHT)
            self.stats_labels[item] = label
    
    def create_status_bar(self, parent):
        """创建底部状态栏"""
        status_frame = tk.Frame(parent, bg="#34495e", height=30)
        status_frame.pack(fill=tk.X)
        status_frame.pack_propagate(False)
        
        # 状态信息
        self.status_label = tk.Label(status_frame,
                                    text="● 系统就绪",
                                    font=("Microsoft YaHei", 9),
                                    bg="#34495e",
                                    fg="#2ecc71")
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame,
                                      mode='indeterminate',
                                      length=200)
        self.progress.pack(side=tk.RIGHT, padx=10, pady=5)
    
    def switch_module(self, module):
        """切换功能模块"""
        self.current_module = module
        
        # 更新导航按钮状态
        for mod, btn in self.nav_buttons.items():
            if mod == module:
                btn.configure(bg="#3498db" if mod == "选股" else 
                            "#e74c3c" if mod == "分析" else
                            "#27ae60" if mod == "结果" else
                            "#f39c12" if mod == "交易" else "#9b59b6")
            else:
                btn.configure(bg="#34495e")
        
        # 更新工作区标题
        icons = {"选股": "📊", "分析": "🔍", "结果": "📋", "交易": "💰", "配置": "⚙️"}
        self.work_title.configure(text=f"{icons[module]} {module}模块")
        
        # 更新快捷面板和工作区内容
        self.update_quick_panel()
        self.update_work_area()
        
        self.log_message(f"切换到{module}模块", "INFO")
    
    def update_quick_panel(self):
        """更新左侧快捷面板"""
        # 清空现有内容
        for widget in self.quick_frame.winfo_children()[1:]:
            widget.destroy()
        
        # 根据当前模块显示不同按钮
        if self.current_module == "选股":
            self.create_stock_selection_panel()
        elif self.current_module == "分析":
            self.create_analysis_panel()
        elif self.current_module == "结果":
            self.create_results_panel()
        elif self.current_module == "交易":
            self.create_trading_panel()
        elif self.current_module == "配置":
            self.create_config_panel()
    
    def update_work_area(self):
        """更新中央工作区"""
        # 清空现有内容
        for widget in self.work_content.winfo_children():
            widget.destroy()
        
        # 根据当前模块显示不同内容
        if self.current_module == "选股":
            self.create_stock_selection_content()
        elif self.current_module == "分析":
            self.create_analysis_content()
        elif self.current_module == "结果":
            self.create_results_content()
        elif self.current_module == "交易":
            self.create_trading_content()
        elif self.current_module == "配置":
            self.create_config_content()
    
    def load_initial_data(self):
        """加载初始数据"""
        self.log_message("系统启动完成", "SUCCESS")
        self.start_time_update()
    
    def start_time_update(self):
        """开始时间更新"""
        def update_time():
            current_time = datetime.now().strftime('%H:%M:%S')
            self.time_label.configure(text=f"🕐 {current_time}")
            self.root.after(1000, update_time)
        
        update_time()
    
    def log_message(self, message, level="INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        color_map = {
            "INFO": "#00ff00",
            "SUCCESS": "#00ff00", 
            "WARNING": "#ffff00",
            "ERROR": "#ff0000"
        }
        
        self.status_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.status_text.see(tk.END)
        
        # 更新状态栏
        status_icons = {
            "INFO": "●",
            "SUCCESS": "✓",
            "WARNING": "⚠",
            "ERROR": "✗"
        }
        self.status_label.configure(text=f"{status_icons[level]} {message}")

    def create_stock_selection_panel(self):
        """创建选股模块快捷面板"""
        buttons = [
            ("🔥 热门股选股", self.run_hot_stocks, "#e74c3c"),
            ("📰 消息面选股", self.run_news_stocks, "#9b59b6"),
            ("📈 技术面选股", self.run_technical_stocks, "#3498db"),
            ("🎯 综合选股", self.run_comprehensive_selection, "#27ae60")
        ]

        for text, command, color in buttons:
            btn = tk.Button(self.quick_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=10,
                           pady=8,
                           cursor="hand2",
                           width=22)
            btn.pack(pady=8, padx=15, fill=tk.X)

    def create_analysis_panel(self):
        """创建分析模块快捷面板"""
        buttons = [
            ("🤖 AI智能分析", self.run_ai_analysis, "#8e44ad"),
            ("📊 技术指标分析", self.run_technical_analysis, "#3498db"),
            ("💭 情感分析", self.run_sentiment_analysis, "#e67e22"),
            ("⚖️ 风险评估", self.run_risk_assessment, "#e74c3c")
        ]

        for text, command, color in buttons:
            btn = tk.Button(self.quick_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=10,
                           pady=8,
                           cursor="hand2",
                           width=22)
            btn.pack(pady=8, padx=15, fill=tk.X)

    def create_results_panel(self):
        """创建结果模块快捷面板"""
        buttons = [
            ("📈 查看推荐列表", self.show_recommendations, "#27ae60"),
            ("📊 生成分析报告", self.generate_report, "#3498db"),
            ("💾 导出结果", self.export_results, "#f39c12"),
            ("🌐 打开Web报告", self.open_web_report, "#9b59b6")
        ]

        for text, command, color in buttons:
            btn = tk.Button(self.quick_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=10,
                           pady=8,
                           cursor="hand2",
                           width=22)
            btn.pack(pady=8, padx=15, fill=tk.X)

    def create_trading_panel(self):
        """创建交易模块快捷面板"""
        buttons = [
            ("💼 模拟交易", self.open_simulation_trading, "#27ae60"),
            ("🔌 连接券商", self.connect_broker, "#3498db"),
            ("📊 查看持仓", self.view_positions, "#f39c12"),
            ("💹 交易记录", self.view_trading_records, "#9b59b6")
        ]

        for text, command, color in buttons:
            btn = tk.Button(self.quick_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=10,
                           pady=8,
                           cursor="hand2",
                           width=22)
            btn.pack(pady=8, padx=15, fill=tk.X)

    def create_config_panel(self):
        """创建配置模块快捷面板"""
        buttons = [
            ("🔧 系统设置", self.open_system_settings, "#34495e"),
            ("📁 数据管理", self.open_data_management, "#3498db"),
            ("🌐 网络配置", self.open_network_config, "#e67e22"),
            ("📋 日志管理", self.open_log_management, "#27ae60")
        ]

        for text, command, color in buttons:
            btn = tk.Button(self.quick_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=10,
                           pady=8,
                           cursor="hand2",
                           width=22)
            btn.pack(pady=8, padx=15, fill=tk.X)

    def create_stock_selection_content(self):
        """创建选股模块内容"""
        # 选股策略选择
        strategy_frame = tk.LabelFrame(self.work_content,
                                     text="📊 选股策略",
                                     font=("Microsoft YaHei", 12, "bold"),
                                     bg="white",
                                     fg="#2c3e50")
        strategy_frame.pack(fill=tk.X, pady=(0, 15))

        # 策略选项
        self.strategy_var = tk.StringVar(value="热门股")
        strategies = [
            ("🔥 热门股选股", "热门股"),
            ("📰 消息面选股", "消息面"),
            ("📈 技术面选股", "技术面"),
            ("🎯 综合选股", "综合")
        ]

        strategy_buttons_frame = tk.Frame(strategy_frame, bg="white")
        strategy_buttons_frame.pack(padx=20, pady=15)

        for text, value in strategies:
            rb = tk.Radiobutton(strategy_buttons_frame,
                              text=text,
                              variable=self.strategy_var,
                              value=value,
                              font=("Microsoft YaHei", 10),
                              bg="white",
                              fg="#2c3e50",
                              selectcolor="#3498db")
            rb.pack(side=tk.LEFT, padx=20)

        # 执行按钮
        execute_btn = tk.Button(strategy_frame,
                              text="🚀 开始选股",
                              command=self.execute_stock_selection,
                              font=("Microsoft YaHei", 12, "bold"),
                              bg="#e74c3c",
                              fg="white",
                              relief="raised",
                              bd=3,
                              padx=30,
                              pady=10,
                              cursor="hand2")
        execute_btn.pack(pady=(0, 15))

        # 选股结果显示区域
        results_frame = tk.LabelFrame(self.work_content,
                                    text="📈 选股结果",
                                    font=("Microsoft YaHei", 12, "bold"),
                                    bg="white",
                                    fg="#2c3e50")
        results_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表格
        columns = ("排名", "股票代码", "股票名称", "当前价格", "涨跌幅", "推荐理由")
        self.stock_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        for col in columns:
            self.stock_tree.heading(col, text=col)
            self.stock_tree.column(col, width=120, anchor=tk.CENTER)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.stock_tree.yview)
        self.stock_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.stock_tree.pack(side="left", fill="both", expand=True, padx=20, pady=15)
        scrollbar.pack(side="right", fill="y", pady=15)

    def create_analysis_content(self):
        """创建分析模块内容"""
        # 分析类型选择
        analysis_frame = tk.LabelFrame(self.work_content,
                                     text="🔍 分析类型",
                                     font=("Microsoft YaHei", 12, "bold"),
                                     bg="white",
                                     fg="#2c3e50")
        analysis_frame.pack(fill=tk.X, pady=(0, 15))

        # 分析选项
        analysis_options = [
            ("🤖 AI智能分析", "#8e44ad"),
            ("📊 技术指标分析", "#3498db"),
            ("💭 情感分析", "#e67e22"),
            ("⚖️ 风险评估", "#e74c3c")
        ]

        analysis_buttons_frame = tk.Frame(analysis_frame, bg="white")
        analysis_buttons_frame.pack(padx=20, pady=15)

        for text, color in analysis_options:
            btn = tk.Button(analysis_buttons_frame,
                           text=text,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=15,
                           pady=8,
                           cursor="hand2",
                           width=15)
            btn.pack(side=tk.LEFT, padx=10)

        # 分析结果显示
        result_frame = tk.LabelFrame(self.work_content,
                                   text="📊 分析结果",
                                   font=("Microsoft YaHei", 12, "bold"),
                                   bg="white",
                                   fg="#2c3e50")
        result_frame.pack(fill=tk.BOTH, expand=True)

        self.analysis_text = scrolledtext.ScrolledText(result_frame,
                                                     height=20,
                                                     font=("Microsoft YaHei", 10),
                                                     bg="white",
                                                     fg="#2c3e50")
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)

    def create_results_content(self):
        """创建结果模块内容"""
        # 结果类型选择
        result_type_frame = tk.Frame(self.work_content, bg="white")
        result_type_frame.pack(fill=tk.X, pady=(0, 15))

        result_buttons = [
            ("📈 推荐列表", "#27ae60"),
            ("📊 分析报告", "#3498db"),
            ("🎯 投资建议", "#f39c12"),
            ("📱 实时监控", "#9b59b6")
        ]

        for text, color in result_buttons:
            btn = tk.Button(result_type_frame,
                           text=text,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=15,
                           pady=8,
                           cursor="hand2",
                           width=12)
            btn.pack(side=tk.LEFT, padx=10)

        # 结果显示区域
        self.results_notebook = ttk.Notebook(self.work_content)
        self.results_notebook.pack(fill=tk.BOTH, expand=True)

        # 推荐列表标签页
        recommendations_frame = tk.Frame(self.results_notebook, bg="white")
        self.results_notebook.add(recommendations_frame, text="📈 推荐列表")

        # 分析报告标签页
        report_frame = tk.Frame(self.results_notebook, bg="white")
        self.results_notebook.add(report_frame, text="📊 分析报告")

        # 投资建议标签页
        advice_frame = tk.Frame(self.results_notebook, bg="white")
        self.results_notebook.add(advice_frame, text="🎯 投资建议")

    def create_trading_content(self):
        """创建交易模块内容"""
        # 交易类型选择
        trading_type_frame = tk.LabelFrame(self.work_content,
                                         text="💰 交易类型",
                                         font=("Microsoft YaHei", 12, "bold"),
                                         bg="white",
                                         fg="#2c3e50")
        trading_type_frame.pack(fill=tk.X, pady=(0, 15))

        trading_buttons = [
            ("💼 模拟交易", "#27ae60"),
            ("🔌 实盘交易", "#e74c3c"),
            ("📊 持仓管理", "#3498db"),
            ("💹 交易记录", "#f39c12")
        ]

        trading_buttons_frame = tk.Frame(trading_type_frame, bg="white")
        trading_buttons_frame.pack(padx=20, pady=15)

        for text, color in trading_buttons:
            btn = tk.Button(trading_buttons_frame,
                           text=text,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=15,
                           pady=8,
                           cursor="hand2",
                           width=12)
            btn.pack(side=tk.LEFT, padx=10)

        # 交易界面
        trading_interface_frame = tk.LabelFrame(self.work_content,
                                              text="💹 交易界面",
                                              font=("Microsoft YaHei", 12, "bold"),
                                              bg="white",
                                              fg="#2c3e50")
        trading_interface_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧：交易操作
        left_frame = tk.Frame(trading_interface_frame, bg="white")
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20, pady=15)

        # 股票代码输入
        tk.Label(left_frame, text="股票代码:", font=("Microsoft YaHei", 10), bg="white").pack(anchor=tk.W)
        self.stock_code_entry = tk.Entry(left_frame, font=("Microsoft YaHei", 10), width=15)
        self.stock_code_entry.pack(pady=(5, 15))

        # 交易数量
        tk.Label(left_frame, text="交易数量:", font=("Microsoft YaHei", 10), bg="white").pack(anchor=tk.W)
        self.quantity_entry = tk.Entry(left_frame, font=("Microsoft YaHei", 10), width=15)
        self.quantity_entry.pack(pady=(5, 15))

        # 交易价格
        tk.Label(left_frame, text="交易价格:", font=("Microsoft YaHei", 10), bg="white").pack(anchor=tk.W)
        self.price_entry = tk.Entry(left_frame, font=("Microsoft YaHei", 10), width=15)
        self.price_entry.pack(pady=(5, 15))

        # 交易按钮
        buy_btn = tk.Button(left_frame, text="🛒 买入", bg="#27ae60", fg="white",
                           font=("Microsoft YaHei", 10, "bold"), padx=20, pady=8)
        buy_btn.pack(pady=5, fill=tk.X)

        sell_btn = tk.Button(left_frame, text="💸 卖出", bg="#e74c3c", fg="white",
                            font=("Microsoft YaHei", 10, "bold"), padx=20, pady=8)
        sell_btn.pack(pady=5, fill=tk.X)

        # 右侧：持仓信息
        right_frame = tk.Frame(trading_interface_frame, bg="white")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=20, pady=15)

        tk.Label(right_frame, text="📊 持仓信息", font=("Microsoft YaHei", 12, "bold"), bg="white").pack()

        # 持仓表格
        position_columns = ("股票代码", "股票名称", "持仓数量", "成本价", "当前价", "盈亏")
        self.position_tree = ttk.Treeview(right_frame, columns=position_columns, show="headings", height=10)

        for col in position_columns:
            self.position_tree.heading(col, text=col)
            self.position_tree.column(col, width=100, anchor=tk.CENTER)

        self.position_tree.pack(fill=tk.BOTH, expand=True, pady=15)

    def create_config_content(self):
        """创建配置模块内容"""
        # 配置选项卡
        config_notebook = ttk.Notebook(self.work_content)
        config_notebook.pack(fill=tk.BOTH, expand=True)

        # 系统设置
        system_frame = tk.Frame(config_notebook, bg="white")
        config_notebook.add(system_frame, text="🔧 系统设置")

        # 数据源配置
        data_frame = tk.Frame(config_notebook, bg="white")
        config_notebook.add(data_frame, text="📊 数据源")

        # 交易配置
        trading_config_frame = tk.Frame(config_notebook, bg="white")
        config_notebook.add(trading_config_frame, text="💰 交易配置")

        # 在系统设置标签页中添加内容
        settings_items = [
            ("自动刷新间隔", "30秒"),
            ("数据保存路径", "./data/"),
            ("日志级别", "INFO"),
            ("界面主题", "默认")
        ]

        for i, (label, default_value) in enumerate(settings_items):
            frame = tk.Frame(system_frame, bg="white")
            frame.pack(fill=tk.X, padx=20, pady=10)

            tk.Label(frame, text=f"{label}:", font=("Microsoft YaHei", 10), bg="white").pack(side=tk.LEFT)
            entry = tk.Entry(frame, font=("Microsoft YaHei", 10), width=20)
            entry.insert(0, default_value)
            entry.pack(side=tk.RIGHT)

    # ==================== 功能实现方法 ====================

    def execute_stock_selection(self):
        """执行选股"""
        strategy = self.strategy_var.get()
        self.log_message(f"开始执行{strategy}选股", "INFO")

        if strategy == "热门股":
            self.run_hot_stocks()
        elif strategy == "消息面":
            self.run_news_stocks()
        elif strategy == "技术面":
            self.run_technical_stocks()
        elif strategy == "综合":
            self.run_comprehensive_selection()

    def run_hot_stocks(self):
        """运行热门股选股"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在爬取热门股数据...", "INFO")

                # 运行后台采集
                result = subprocess.run([sys.executable, "后台采集.py"],
                                      cwd=os.getcwd(), capture_output=True, text=True)

                if result.returncode == 0:
                    self.log_message("热门股数据采集完成", "SUCCESS")
                    self.load_hot_stocks_data()
                else:
                    self.log_message("热门股数据采集失败", "ERROR")

            except Exception as e:
                self.log_message(f"热门股选股失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def run_news_stocks(self):
        """运行消息面选股"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在进行消息面选股...", "INFO")

                # 获取脚本文件的绝对路径
                script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "消息面智能选股.py")
                
                # 运行消息面选股
                result = subprocess.run([sys.executable, script_path],
                                      cwd=os.path.dirname(script_path), capture_output=True, text=True)

                if result.returncode == 0:
                    self.log_message("消息面选股完成", "SUCCESS")
                    self.load_news_stocks_data()
                else:
                    self.log_message("消息面选股失败", "ERROR")

            except Exception as e:
                self.log_message(f"消息面选股失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def run_technical_stocks(self):
        """运行技术面选股"""
        self.log_message("技术面选股功能开发中...", "WARNING")
        messagebox.showinfo("提示", "技术面选股功能正在开发中，敬请期待！")

    def run_comprehensive_selection(self):
        """运行综合选股"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在进行AI综合选股...", "INFO")

                # 运行AI智能选股
                result = subprocess.run([sys.executable, "AI智能选股分析.py"],
                                      cwd=os.getcwd(), capture_output=True, text=True)

                if result.returncode == 0:
                    self.log_message("AI综合选股完成", "SUCCESS")
                    self.load_ai_stocks_data()
                else:
                    self.log_message("AI综合选股失败", "ERROR")

            except Exception as e:
                self.log_message(f"AI综合选股失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def load_hot_stocks_data(self):
        """加载热门股数据"""
        try:
            # 清空现有数据
            for item in self.stock_tree.get_children():
                self.stock_tree.delete(item)

            # 加载人气榜数据
            if os.path.exists('popularity.csv'):
                with open('popularity.csv', 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for i, row in enumerate(reader, 1):
                        if i <= 20:  # 只显示前20只
                            self.stock_tree.insert("", "end", values=(
                                i,
                                row.get('code', ''),
                                row.get('name', ''),
                                row.get('price', ''),
                                row.get('change', ''),
                                "热门股推荐"
                            ))

                self.stats_labels["热门股票"].configure(text="20")
                self.log_message("热门股数据加载完成", "SUCCESS")

        except Exception as e:
            self.log_message(f"加载热门股数据失败: {str(e)}", "ERROR")

    def load_news_stocks_data(self):
        """加载消息面股票数据"""
        try:
            # 清空现有数据
            for item in self.stock_tree.get_children():
                self.stock_tree.delete(item)

            # 加载消息面选股结果
            if os.path.exists('消息面选股结果.json'):
                with open('消息面选股结果.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    recommendations = data.get('recommendations', [])

                    for i, stock in enumerate(recommendations[:20], 1):  # 只显示前20只
                        self.stock_tree.insert("", "end", values=(
                            i,
                            stock.get('code', ''),
                            stock.get('name', ''),
                            "实时获取",
                            "实时获取",
                            stock.get('reason', '')
                        ))

                self.stats_labels["消息股票"].configure(text=str(len(recommendations)))
                self.log_message("消息面股票数据加载完成", "SUCCESS")

        except Exception as e:
            self.log_message(f"加载消息面数据失败: {str(e)}", "ERROR")

    def load_ai_stocks_data(self):
        """加载AI选股数据"""
        try:
            # 清空现有数据
            for item in self.stock_tree.get_children():
                self.stock_tree.delete(item)

            # 加载AI选股结果
            if os.path.exists('AI智能选股分析结果.json'):
                with open('AI智能选股分析结果.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    final_rec = data.get('final_recommendations', {})

                    # 显示强烈推荐和建议买入的股票
                    all_recommendations = []
                    all_recommendations.extend(final_rec.get('strong_buy', []))
                    all_recommendations.extend(final_rec.get('buy', []))

                    for i, stock in enumerate(all_recommendations[:20], 1):
                        self.stock_tree.insert("", "end", values=(
                            i,
                            stock.get('code', ''),
                            stock.get('name', ''),
                            "实时获取",
                            "实时获取",
                            stock.get('ai_reason', '')
                        ))

                self.stats_labels["AI推荐"].configure(text=str(len(all_recommendations)))
                self.log_message("AI选股数据加载完成", "SUCCESS")

        except Exception as e:
            self.log_message(f"加载AI选股数据失败: {str(e)}", "ERROR")

    # ==================== 分析模块方法 ====================

    def run_ai_analysis(self):
        """运行AI分析"""
        self.log_message("正在进行AI智能分析...", "INFO")
        # 这里可以添加具体的AI分析逻辑

    def run_technical_analysis(self):
        """运行技术分析"""
        self.log_message("正在进行技术指标分析...", "INFO")
        # 这里可以添加技术分析逻辑

    def run_sentiment_analysis(self):
        """运行情感分析"""
        self.log_message("正在进行情感分析...", "INFO")
        # 这里可以添加情感分析逻辑

    def run_risk_assessment(self):
        """运行风险评估"""
        self.log_message("正在进行风险评估...", "INFO")
        # 这里可以添加风险评估逻辑

    # ==================== 结果模块方法 ====================

    def show_recommendations(self):
        """显示推荐列表"""
        self.log_message("显示推荐列表", "INFO")

    def generate_report(self):
        """生成分析报告"""
        self.log_message("正在生成分析报告...", "INFO")

    def export_results(self):
        """导出结果"""
        self.log_message("导出结果功能", "INFO")

    def open_web_report(self):
        """打开Web报告"""
        self.log_message("打开Web报告", "INFO")

    # ==================== 交易模块方法 ====================

    def open_simulation_trading(self):
        """打开模拟交易"""
        self.log_message("模拟交易功能", "INFO")

    def connect_broker(self):
        """连接券商"""
        self.log_message("连接券商功能", "INFO")

    def view_positions(self):
        """查看持仓"""
        self.log_message("查看持仓功能", "INFO")

    def view_trading_records(self):
        """查看交易记录"""
        self.log_message("查看交易记录", "INFO")

    # ==================== 配置模块方法 ====================

    def open_system_settings(self):
        """打开系统设置"""
        self.log_message("系统设置功能", "INFO")

    def open_data_management(self):
        """打开数据管理"""
        self.log_message("数据管理功能", "INFO")

    def open_network_config(self):
        """打开网络配置"""
        self.log_message("网络配置功能", "INFO")

    def open_log_management(self):
        """打开日志管理"""
        self.log_message("日志管理功能", "INFO")

def main():
    """主函数"""
    root = tk.Tk()
    app = StockInvestmentSystem(root)
    root.mainloop()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智股投系统 - 最终版界面设计
左侧主菜单 + 右上子菜单 + 主显示区域
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import sys
import os
import threading
import json
import csv
from datetime import datetime
import time

class ZhiGuTouSystem:
    def __init__(self, root):
        self.root = root
        self.current_module = "选股"  # 当前主菜单
        self.setup_window()
        self.create_main_interface()
        self.load_initial_data()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("🚀 智股投")
        self.root.geometry("1600x1000")
        self.root.resizable(True, True)
        self.root.configure(bg="#f8f9fa")
        
        # 窗口居中
        self.center_window()
        
        # 设置样式
        self.setup_styles()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 1600
        height = 1000
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def setup_styles(self):
        """设置界面样式"""
        self.style = ttk.Style()
        self.style.theme_use('clam')
    
    def create_main_interface(self):
        """创建主界面"""
        # 主容器
        main_container = tk.Frame(self.root, bg="#f8f9fa")
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧主菜单区域
        self.create_left_menu_area(main_container)
        
        # 右侧主工作区域
        self.create_right_work_area(main_container)
        
        # 底部状态栏
        self.create_status_bar(main_container)
    
    def create_left_menu_area(self, parent):
        """创建左侧主菜单区域"""
        left_frame = tk.Frame(parent, bg="#ecf0f1", width=200)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.pack_propagate(False)
        
        # 智股投标题
        title_frame = tk.Frame(left_frame, bg="#2c3e50", height=60)
        title_frame.pack(fill=tk.X, pady=(15, 10))
        title_frame.pack_propagate(False)
        
        tk.Label(title_frame,
                text="🚀 智股投",
                font=("Microsoft YaHei", 16, "bold"),
                bg="#2c3e50",
                fg="white").pack(expand=True)
        
        # 主菜单按钮组
        self.create_main_menu_buttons(left_frame)
        
        # 系统信息区域
        self.create_system_info_area(left_frame)
    
    def create_main_menu_buttons(self, parent):
        """创建主菜单按钮"""
        menu_frame = tk.Frame(parent, bg="#ecf0f1")
        menu_frame.pack(fill=tk.X, padx=15, pady=(0, 20))
        
        # 主菜单按钮配置
        main_menus = [
            ("📊 选股", "选股", "#3498db"),
            ("🔍 分析", "分析", "#e74c3c"),
            ("📋 结果", "结果", "#27ae60"),
            ("💰 交易", "交易", "#f39c12"),
            ("⚙️ 配置", "配置", "#9b59b6")
        ]
        
        self.main_menu_buttons = {}
        for text, module, color in main_menus:
            btn = tk.Button(menu_frame,
                           text=text,
                           command=lambda m=module: self.switch_main_module(m),
                           font=("Microsoft YaHei", 12, "bold"),
                           bg=color if module == self.current_module else "#34495e",
                           fg="white",
                           relief="flat",
                           bd=0,
                           padx=10,
                           pady=12,
                           cursor="hand2",
                           width=15)
            btn.pack(fill=tk.X, pady=3)
            self.main_menu_buttons[module] = btn
    
    def create_system_info_area(self, parent):
        """创建系统信息区域"""
        # 分隔线
        separator = tk.Frame(parent, height=2, bg="#bdc3c7")
        separator.pack(fill=tk.X, padx=15, pady=10)
        
        # 实时时间
        self.time_label = tk.Label(parent,
                                  text=f"🕐 {datetime.now().strftime('%H:%M:%S')}",
                                  font=("Microsoft YaHei", 11),
                                  bg="#ecf0f1",
                                  fg="#34495e")
        self.time_label.pack(pady=5)
        
        # 系统日志
        log_frame = tk.LabelFrame(parent,
                                text="📋 系统日志",
                                font=("Microsoft YaHei", 9, "bold"),
                                bg="#ecf0f1",
                                fg="#2c3e50")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame,
                                                height=8,
                                                width=20,
                                                font=("Consolas", 8),
                                                bg="#2c3e50",
                                                fg="#00ff00",
                                                insertbackground="white")
        self.log_text.pack(padx=5, pady=5, fill=tk.BOTH, expand=True)
        
        # 数据统计
        stats_frame = tk.LabelFrame(parent,
                                  text="📈 数据统计",
                                  font=("Microsoft YaHei", 9, "bold"),
                                  bg="#ecf0f1",
                                  fg="#2c3e50")
        stats_frame.pack(fill=tk.X, padx=15, pady=(5, 15))
        
        self.stats_labels = {}
        stats_items = [
            ("热门股票", "0"),
            ("消息股票", "0"),
            ("AI推荐", "0"),
            ("持仓股票", "0")
        ]
        
        for item, value in stats_items:
            frame = tk.Frame(stats_frame, bg="#ecf0f1")
            frame.pack(fill=tk.X, padx=5, pady=1)
            
            tk.Label(frame, text=f"{item}:", 
                    font=("Microsoft YaHei", 8),
                    bg="#ecf0f1", fg="#34495e").pack(side=tk.LEFT)
            
            label = tk.Label(frame, text=value,
                           font=("Microsoft YaHei", 8, "bold"),
                           bg="#ecf0f1", fg="#e74c3c")
            label.pack(side=tk.RIGHT)
            self.stats_labels[item] = label
    
    def create_right_work_area(self, parent):
        """创建右侧主工作区域"""
        right_frame = tk.Frame(parent, bg="white", relief="solid", bd=1)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 子菜单栏
        self.create_sub_menu_bar(right_frame)
        
        # 主显示区域
        self.create_main_display_area(right_frame)
    
    def create_sub_menu_bar(self, parent):
        """创建子菜单栏"""
        self.sub_menu_frame = tk.Frame(parent, bg="#34495e", height=60)
        self.sub_menu_frame.pack(fill=tk.X)
        self.sub_menu_frame.pack_propagate(False)
        
        # 子菜单容器
        self.sub_menu_container = tk.Frame(self.sub_menu_frame, bg="#34495e")
        self.sub_menu_container.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 初始化显示选股子菜单
        self.update_sub_menu()
    
    def create_main_display_area(self, parent):
        """创建主显示区域"""
        self.display_frame = tk.Frame(parent, bg="white")
        self.display_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 初始化显示内容
        self.update_display_content()
    
    def create_status_bar(self, parent):
        """创建底部状态栏"""
        status_frame = tk.Frame(parent, bg="#34495e", height=30)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        status_frame.pack_propagate(False)
        
        # 状态信息
        self.status_label = tk.Label(status_frame,
                                    text="● 系统就绪",
                                    font=("Microsoft YaHei", 9),
                                    bg="#34495e",
                                    fg="#2ecc71")
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame,
                                      mode='indeterminate',
                                      length=200)
        self.progress.pack(side=tk.RIGHT, padx=10, pady=5)
    
    def switch_main_module(self, module):
        """切换主菜单模块"""
        self.current_module = module
        
        # 更新主菜单按钮状态
        colors = {"选股": "#3498db", "分析": "#e74c3c", "结果": "#27ae60", "交易": "#f39c12", "配置": "#9b59b6"}
        for mod, btn in self.main_menu_buttons.items():
            if mod == module:
                btn.configure(bg=colors[mod])
            else:
                btn.configure(bg="#34495e")
        
        # 更新子菜单和显示内容
        self.update_sub_menu()
        self.update_display_content()
        
        self.log_message(f"切换到{module}模块", "INFO")
    
    def update_sub_menu(self):
        """更新子菜单"""
        # 清空现有子菜单
        for widget in self.sub_menu_container.winfo_children():
            widget.destroy()
        
        # 根据当前主菜单显示相应子菜单
        sub_menus = self.get_sub_menus(self.current_module)
        
        for text, command, color in sub_menus:
            btn = tk.Button(self.sub_menu_container,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=1,
                           padx=12,
                           pady=6,
                           cursor="hand2")
            btn.pack(side=tk.LEFT, padx=5)
    
    def get_sub_menus(self, module):
        """获取子菜单配置"""
        sub_menu_configs = {
            "选股": [
                ("🔥 热门股", lambda: self.switch_selection_mode("hot"), "#e74c3c"),
                ("📰 消息面", lambda: self.switch_selection_mode("news"), "#9b59b6"),
                ("🔄 刷新数据", self.safe_refresh_data, "#27ae60")
            ],
            "分析": [
                ("� 一键分析", lambda: self.run_comprehensive_analysis(), "#e67e22"),
                ("💭 情感分析", self.run_sentiment_analysis, "#3498db"),
                ("⚖️ 风险评估", self.run_risk_assessment, "#e74c3c"),
                ("🤖 AI详细分析", self.run_ai_detailed_analysis, "#8e44ad")
            ],
            "结果": [
                ("📈 推荐列表", self.show_recommendations, "#27ae60"),
                ("📊 分析报告", self.show_analysis_report, "#3498db"),
                ("🎯 投资建议", self.show_investment_advice, "#f39c12"),
                ("📱 实时监控", self.show_real_time_monitor, "#9b59b6")
            ],
            "交易": [
                ("💼 模拟交易", self.open_simulation_trading, "#27ae60"),
                ("🔌 实盘交易", self.open_real_trading, "#e74c3c"),
                ("📊 持仓管理", self.show_position_management, "#3498db"),
                ("💹 交易记录", self.show_trading_records, "#f39c12")
            ],
            "配置": [
                ("🔧 系统设置", self.open_system_settings, "#34495e"),
                ("📊 数据源配置", self.open_data_source_config, "#3498db"),
                ("💰 交易配置", self.open_trading_config, "#f39c12"),
                ("📋 日志管理", self.open_log_management, "#27ae60")
            ]
        }
        
        return sub_menu_configs.get(module, [])

    def update_display_content(self):
        """更新主显示区域内容"""
        # 清空现有内容
        for widget in self.display_frame.winfo_children():
            widget.destroy()

        # 根据当前模块显示不同内容
        if self.current_module == "选股":
            self.create_stock_selection_display()
        elif self.current_module == "分析":
            self.create_analysis_display()
        elif self.current_module == "结果":
            self.create_results_display()
        elif self.current_module == "交易":
            self.create_trading_display()
        elif self.current_module == "配置":
            self.create_config_display()

    def create_stock_selection_display(self):
        """创建选股模块显示"""
        # 清空现有内容
        for widget in self.display_frame.winfo_children():
            widget.destroy()

        # 根据当前选择的子功能显示不同内容
        if hasattr(self, 'current_selection_type'):
            if self.current_selection_type == "news":
                self.create_news_selection_display()
            else:
                self.create_hot_stocks_display()
        else:
            # 默认显示热门股
            self.current_selection_type = "hot"
            self.create_hot_stocks_display()

    def create_hot_stocks_display(self):
        """创建热门股显示（双列布局）"""
        # 创建双列布局容器
        main_container = tk.Frame(self.display_frame, bg="white")
        main_container.pack(fill=tk.BOTH, expand=True)

        # 左侧：人气榜
        left_frame = tk.LabelFrame(main_container,
                                 text="🔥 人气榜",
                                 font=("Microsoft YaHei", 12, "bold"),
                                 bg="white",
                                 fg="#e74c3c")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 右侧：飙升榜
        right_frame = tk.LabelFrame(main_container,
                                  text="📈 飙升榜",
                                  font=("Microsoft YaHei", 12, "bold"),
                                  bg="white",
                                  fg="#27ae60")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 人气榜表格 - 增加较昨日排名列
        popularity_columns = ("排名", "股票代码", "股票名称", "当前价格", "涨跌幅", "较昨日排名")
        self.popularity_tree = ttk.Treeview(left_frame, columns=popularity_columns, show="headings")

        # 设置人气榜列标题和宽度
        pop_widths = {"排名": 50, "股票代码": 80, "股票名称": 100, "当前价格": 80, "涨跌幅": 80, "较昨日排名": 80}

        for col in popularity_columns:
            self.popularity_tree.heading(col, text=col)
            self.popularity_tree.column(col, width=pop_widths.get(col, 80), anchor=tk.CENTER)

        # 人气榜滚动条
        pop_scrollbar = ttk.Scrollbar(left_frame, orient="vertical", command=self.popularity_tree.yview)
        self.popularity_tree.configure(yscrollcommand=pop_scrollbar.set)

        # 人气榜布局
        self.popularity_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        pop_scrollbar.pack(side="right", fill="y", pady=5)

        # 飙升榜表格 - 增加较昨日排名列
        soaring_columns = ("排名", "股票代码", "股票名称", "当前价格", "涨跌幅", "较昨日排名")
        self.soaring_tree = ttk.Treeview(right_frame, columns=soaring_columns, show="headings")

        # 设置飙升榜列标题和宽度
        soar_widths = {"排名": 50, "股票代码": 80, "股票名称": 100, "当前价格": 80, "涨跌幅": 80, "较昨日排名": 80}

        for col in soaring_columns:
            self.soaring_tree.heading(col, text=col)
            self.soaring_tree.column(col, width=soar_widths.get(col, 80), anchor=tk.CENTER)

        # 飙升榜滚动条
        soar_scrollbar = ttk.Scrollbar(right_frame, orient="vertical", command=self.soaring_tree.yview)
        self.soaring_tree.configure(yscrollcommand=soar_scrollbar.set)

        # 飙升榜布局
        self.soaring_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        soar_scrollbar.pack(side="right", fill="y", pady=5)

        # 加载现有数据
        self.load_existing_stock_data()

    def create_news_selection_display(self):
        """创建消息面选股显示（话题和股票两部分）"""
        # 创建上下分割布局容器
        main_container = tk.Frame(self.display_frame, bg="white")
        main_container.pack(fill=tk.BOTH, expand=True)

        # 上半部分：热门话题
        top_frame = tk.LabelFrame(main_container,
                                text="📰 热门话题",
                                font=("Microsoft YaHei", 12, "bold"),
                                bg="white",
                                fg="#9b59b6",
                                height=300)
        top_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        # 下半部分：推荐股票
        bottom_frame = tk.LabelFrame(main_container,
                                   text="📈 推荐股票",
                                   font=("Microsoft YaHei", 12, "bold"),
                                   bg="white",
                                   fg="#27ae60",
                                   height=300)
        bottom_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))

        # 热门话题表格
        topics_columns = ("排名", "话题标题", "热度", "情感倾向", "相关股票数", "发布时间")
        self.topics_tree = ttk.Treeview(top_frame, columns=topics_columns, show="headings", height=12)

        # 设置话题列标题和宽度
        topic_widths = {"排名": 50, "话题标题": 300, "热度": 80, "情感倾向": 80, "相关股票数": 100, "发布时间": 120}

        for col in topics_columns:
            self.topics_tree.heading(col, text=col)
            self.topics_tree.column(col, width=topic_widths.get(col, 100), anchor=tk.CENTER)

        # 话题滚动条
        topics_scrollbar = ttk.Scrollbar(top_frame, orient="vertical", command=self.topics_tree.yview)
        self.topics_tree.configure(yscrollcommand=topics_scrollbar.set)

        # 话题布局
        self.topics_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        topics_scrollbar.pack(side="right", fill="y", pady=5)

        # 推荐股票表格
        news_stocks_columns = ("排名", "股票代码", "股票名称", "情感得分", "提及次数", "推荐等级", "推荐理由")
        self.news_stocks_tree = ttk.Treeview(bottom_frame, columns=news_stocks_columns, show="headings", height=12)

        # 设置股票列标题和宽度
        news_widths = {"排名": 50, "股票代码": 80, "股票名称": 100, "情感得分": 80, "提及次数": 80, "推荐等级": 80, "推荐理由": 200}

        for col in news_stocks_columns:
            self.news_stocks_tree.heading(col, text=col)
            self.news_stocks_tree.column(col, width=news_widths.get(col, 100), anchor=tk.CENTER)

        # 股票滚动条
        news_stocks_scrollbar = ttk.Scrollbar(bottom_frame, orient="vertical", command=self.news_stocks_tree.yview)
        self.news_stocks_tree.configure(yscrollcommand=news_stocks_scrollbar.set)

        # 股票布局
        self.news_stocks_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        news_stocks_scrollbar.pack(side="right", fill="y", pady=5)

        # 加载消息面数据
        self.load_news_selection_data()

    def create_technical_selection_display(self):
        """创建技术面选股显示（单列表格）"""
        # 创建技术面选股容器
        main_container = tk.Frame(self.display_frame, bg="white")
        main_container.pack(fill=tk.BOTH, expand=True)

        # 技术面选股表格
        tech_frame = tk.LabelFrame(main_container,
                                 text="📈 技术面选股",
                                 font=("Microsoft YaHei", 12, "bold"),
                                 bg="white",
                                 fg="#3498db")
        tech_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 技术面股票表格
        tech_columns = ("排名", "股票代码", "股票名称", "当前价格", "涨跌幅", "技术指标", "推荐理由")
        self.technical_tree = ttk.Treeview(tech_frame, columns=tech_columns, show="headings", height=30)

        # 设置列标题和宽度
        tech_widths = {"排名": 50, "股票代码": 80, "股票名称": 100, "当前价格": 80, "涨跌幅": 80, "技术指标": 120, "推荐理由": 200}

        for col in tech_columns:
            self.technical_tree.heading(col, text=col)
            self.technical_tree.column(col, width=tech_widths.get(col, 100), anchor=tk.CENTER)

        # 技术面滚动条
        tech_scrollbar = ttk.Scrollbar(tech_frame, orient="vertical", command=self.technical_tree.yview)
        self.technical_tree.configure(yscrollcommand=tech_scrollbar.set)

        # 技术面布局
        self.technical_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        tech_scrollbar.pack(side="right", fill="y", pady=5)

        # 加载技术面数据
        self.load_technical_selection_data()

    def switch_selection_mode(self, mode):
        """切换选股显示模式"""
        self.log_message(f"切换到{mode}模式", "INFO")

        # 设置当前选股类型
        self.current_selection_type = mode

        # 如果当前在选股模块，立即更新显示
        if self.current_module == "选股":
            self.update_display_content()

            # 根据模式加载相应数据
            if mode == "hot":
                self.load_existing_stock_data()
            elif mode == "news":
                self.load_news_selection_data()

    def refresh_current_selection(self):
        """刷新当前选股模式的数据"""
        if not hasattr(self, 'current_selection_type'):
            self.current_selection_type = "hot"

        self.log_message(f"🔄 刷新{self.get_selection_mode_name()}数据", "INFO")

        # 根据当前模式运行相应的选股功能
        if self.current_selection_type == "hot":
            self.run_hot_stocks()
        elif self.current_selection_type == "news":
            self.run_news_stocks()
        else:
            self.log_message("未知的选股模式", "WARNING")

    def get_selection_mode_name(self):
        """获取选股模式名称"""
        mode_names = {
            "hot": "热门股",
            "news": "消息面"
        }
        return mode_names.get(self.current_selection_type, "未知")

    def safe_refresh_data(self):
        """安全的刷新数据方法"""
        try:
            # 显示刷新提示
            self.log_message("🔄 开始刷新数据...", "INFO")

            # 检查当前选股模式
            if not hasattr(self, 'current_selection_type'):
                self.current_selection_type = "hot"

            mode_name = self.get_selection_mode_name()
            self.log_message(f"📊 当前模式: {mode_name}", "INFO")

            # 根据模式选择刷新策略
            if self.current_selection_type == "hot":
                self.safe_refresh_hot_stocks()
            elif self.current_selection_type == "news":
                self.safe_refresh_news_stocks()
            else:
                self.log_message("未知的选股模式", "WARNING")

        except Exception as e:
            self.log_message(f"刷新数据失败: {str(e)}", "ERROR")
            messagebox.showerror("刷新失败", f"刷新数据时发生错误:\n{str(e)}")

    def safe_refresh_hot_stocks(self):
        """安全的热门股刷新 - 使用API接口"""
        try:
            self.log_message("🔥 开始刷新热门股真实数据...", "INFO")

            # 尝试导入后台采集模块
            try:
                from 后台采集 import collect_hot_stocks_data, get_collection_status
                use_api = True
                self.log_message("📊 使用API接口获取真实数据", "INFO")
            except ImportError:
                use_api = False
                self.log_message("⚠️ API接口不可用，使用传统方式", "WARNING")

            if use_api:
                # 使用API接口
                self._refresh_hot_stocks_api()
            else:
                # 使用传统subprocess方式
                self._refresh_hot_stocks_subprocess()

        except Exception as e:
            self.log_message(f"热门股刷新失败: {str(e)}", "ERROR")
            messagebox.showerror("刷新失败", f"热门股刷新失败:\n{str(e)}")

    def _refresh_hot_stocks_api(self):
        """使用API接口刷新热门股数据"""
        def progress_callback(step, message, progress):
            """进度回调函数"""
            self.log_message(f"📊 {message}", "INFO")
            if hasattr(self, 'progress'):
                if progress >= 0:
                    self.progress['value'] = progress
                else:
                    self.progress.stop()

        def run_api_collection():
            """在后台线程中运行API采集"""
            try:
                self.progress.start()
                self.log_message("🌐 开始采集东方财富真实数据...", "INFO")
                self.log_message("💡 请耐心等待，采集过程需要2-3分钟", "INFO")

                # 尝试导入API模块
                try:
                    from 后台采集 import collect_hot_stocks_data
                    # 调用API接口
                    result = collect_hot_stocks_data(
                        callback=progress_callback,
                        timeout=300
                    )

                    if result['success']:
                        self.log_message("✅ 热门股真实数据采集成功", "SUCCESS")
                        
                        # 更新界面数据
                        self.load_existing_stock_data()

                        # 显示成功信息
                        stats = result['statistics']
                        success_msg = "🎉 热门股真实数据刷新成功！\n\n"
                        success_msg += f"📊 人气榜: {stats.get('popularity_count', 0)} 只\n"
                        success_msg += f"📊 飙升榜: {stats.get('soaring_count', 0)} 只\n"
                        success_msg += f"📈 总计: {stats.get('total_count', 0)} 只\n"
                        success_msg += "📈 数据来源: 东方财富网"

                        messagebox.showinfo("刷新成功", success_msg)
                    else:
                        error_msg = result.get('error', '未知错误')
                        self.log_message(f"❌ 真实数据采集失败: {error_msg}", "ERROR")
                        messagebox.showerror("采集失败", 
                                           f"真实数据采集失败！\n\n"
                                           f"错误详情: {error_msg}\n\n"
                                           f"建议:\n"
                                           f"1. 检查网络连接\n"
                                           f"2. 稍后重试\n"
                                           f"3. 确保Chrome浏览器已安装")

                except ImportError:
                    # 如果API模块不可用，使用传统方式
                    self.log_message("⚠️ API模块不可用，使用传统采集方式", "WARNING")
                    self._refresh_hot_stocks_subprocess()

            except Exception as e:
                self.log_message(f"❌ API采集失败: {str(e)}", "ERROR")
                messagebox.showerror("采集失败", f"API采集失败:\n{str(e)}")
            finally:
                self.progress.stop()

        # 在后台线程中运行
        threading.Thread(target=run_api_collection, daemon=True).start()

    def _refresh_hot_stocks_subprocess(self):
        """使用subprocess方式刷新热门股数据（备用方案）"""
        # 只使用真实数据采集脚本
        script_to_use = None
        if os.path.exists("后台采集.py"):
            script_to_use = "后台采集.py"
            self.log_message("📊 使用后台采集.py获取真实数据", "INFO")
        elif os.path.exists("简化后台采集.py"):
            script_to_use = "简化后台采集.py"
            self.log_message("📊 使用简化后台采集.py获取真实数据", "INFO")
        else:
            self.log_message("❌ 未找到真实数据采集脚本", "ERROR")
            messagebox.showerror("文件缺失",
                               "未找到真实数据采集脚本！\n\n"
                               "需要以下文件之一:\n"
                               "• 后台采集.py\n"
                               "• 简化后台采集.py")
            return

        # 运行真实数据采集脚本
        try:
            self.log_message("🌐 开始采集东方财富真实数据...", "INFO")
            self.log_message("💡 请耐心等待，采集过程需要2-3分钟", "INFO")

            result = subprocess.run([sys.executable, script_to_use],
                                  cwd=os.getcwd(),
                                  capture_output=True,
                                  text=True,
                                  timeout=300)  # 5分钟超时

            if result.returncode == 0:
                self.log_message("✅ 热门股真实数据采集成功", "SUCCESS")
                self.load_existing_stock_data()

                # 检查生成的文件
                files_info = []
                if os.path.exists('popularity.csv'):
                    with open('popularity.csv', 'r', encoding='utf-8') as f:
                        lines = len(f.readlines()) - 1
                    files_info.append(f"人气榜 {lines}只")

                if os.path.exists('soaring.csv'):
                    with open('soaring.csv', 'r', encoding='utf-8') as f:
                        lines = len(f.readlines()) - 1
                    files_info.append(f"飙升榜 {lines}只")

                success_msg = "🎉 热门股真实数据刷新成功！\n\n"
                if files_info:
                    success_msg += f"📊 已获取: {', '.join(files_info)}\n"
                success_msg += "📈 数据来源: 东方财富网"

                messagebox.showinfo("刷新成功", success_msg)
            else:
                error_msg = result.stderr if result.stderr else result.stdout if result.stdout else "未知错误"
                self.log_message(f"❌ 真实数据采集失败: {error_msg[:100]}", "ERROR")

                # 显示详细错误信息
                messagebox.showerror("采集失败",
                                   f"真实数据采集失败！\n\n"
                                   f"可能原因:\n"
                                   f"• 网络连接问题\n"
                                   f"• 网站访问限制\n"
                                   f"• Chrome浏览器问题\n\n"
                                   f"错误详情: {error_msg[:200]}...\n\n"
                                   f"建议:\n"
                                   f"1. 检查网络连接\n"
                                   f"2. 稍后重试\n"
                                   f"3. 确保Chrome浏览器已安装")

        except subprocess.TimeoutExpired:
            self.log_message("❌ 采集脚本执行超时", "ERROR")
            messagebox.showerror("刷新超时", "数据采集超时，请检查网络连接")

    def safe_refresh_news_stocks(self):
        """安全的消息面刷新 - 异步执行避免卡死"""
        # 检查是否已有刷新任务在运行
        if hasattr(self, '_news_refresh_running') and self._news_refresh_running:
            messagebox.showwarning("刷新中", "消息面数据正在刷新中，请稍候...")
            return

        def run_news_analysis():
            """在后台线程中运行消息面分析"""
            self._news_refresh_running = True
            try:
                self.log_message("📰 开始刷新消息面真实数据...", "INFO")

                # 检查消息面脚本
                if not os.path.exists("消息面智能选股.py"):
                    self.log_message("❌ 未找到消息面选股脚本", "ERROR")
                    self.root.after(0, lambda: messagebox.showerror("文件缺失",
                                       "未找到消息面智能选股.py文件！\n\n"
                                       "请确保该文件存在于当前目录"))
                    return

                # 运行消息面选股脚本获取真实数据
                self.log_message("🌐 开始分析消息面真实数据...", "INFO")
                self.log_message("💡 正在分析股吧热门讨论，请耐心等待", "INFO")
                self.log_message("⏰ 预计需要30-60秒，请勿关闭程序", "INFO")

                import time
                start_time = time.time()

                # 使用合理的超时时间
                result = subprocess.run([sys.executable, "消息面智能选股.py"],
                                      cwd=os.getcwd(),
                                      capture_output=True,
                                      text=True,
                                      timeout=120)  # 2分钟超时

                end_time = time.time()
                duration = end_time - start_time

                if result.returncode == 0:
                    self.log_message(f"✅ 消息面分析成功 (耗时{duration:.1f}秒)", "SUCCESS")
                    # 在主线程中更新UI
                    self.root.after(0, self.load_news_selection_data)
                    self.root.after(0, lambda: messagebox.showinfo("选股完成",
                        f"🎉 消息面选股完成！\n\n⏱️ 耗时: {duration:.1f}秒\n📊 数据已加载到界面"))
                else:
                    error_output = result.stderr if result.stderr else result.stdout if result.stdout else "未知错误"
                    self.log_message(f"❌ 消息面选股失败: {error_output[:100]}", "ERROR")
                    self.root.after(0, lambda: messagebox.showerror("选股失败",
                        f"消息面选股失败:\n{error_output[:200]}"))

            except subprocess.TimeoutExpired:
                self.log_message("❌ 消息面选股超时", "ERROR")
                self.root.after(0, lambda: messagebox.showerror("选股超时",
                    "消息面选股超时（2分钟）\n请检查网络连接后重试"))

            except Exception as e:
                self.log_message(f"❌ 消息面选股失败: {str(e)}", "ERROR")
                self.root.after(0, lambda: messagebox.showerror("选股失败",
                    f"消息面选股失败:\n{str(e)}"))
            finally:
                self._news_refresh_running = False

        # 在后台线程中运行，避免阻塞UI
        import threading
        threading.Thread(target=run_news_analysis, daemon=True).start()

        # 立即显示开始信息
        messagebox.showinfo("开始刷新", "🚀 消息面数据刷新已开始！\n\n💡 程序将在后台运行，不会卡死\n⏰ 预计30-60秒完成\n📊 完成后会自动弹出提示")

    def run_technical_stocks(self):
        """运行技术面选股"""
        self.log_message("技术面选股功能开发中...", "WARNING")
        messagebox.showinfo("提示", "技术面选股功能正在开发中，敬请期待！\n\n将基于QMT历史数据进行技术指标分析")

    def run_ai_analysis(self):
        """运行AI智能分析"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在进行AI智能分析...", "INFO")

                # 检查前置条件
                required_files = [
                    ('popularity.csv', '人气榜数据'),
                    ('消息面选股结果.json', '消息面选股结果')
                ]

                missing_files = []
                for filename, description in required_files:
                    if not os.path.exists(filename):
                        missing_files.append(f"• {description} ({filename})")

                if missing_files:
                    error_msg = "缺少必要的数据文件:\n\n" + "\n".join(missing_files) + "\n\n请先运行相应的选股功能"
                    self.log_message("AI分析失败: 缺少数据文件", "ERROR")
                    messagebox.showerror("数据文件缺失", error_msg)
                    return

                # 运行AI分析
                result = subprocess.run([sys.executable, "AI智能选股分析.py"],
                                      cwd=os.getcwd(), capture_output=True, text=True)

                if result.returncode == 0:
                    self.log_message("AI智能分析完成", "SUCCESS")
                    self.load_ai_stocks_data()

                    # 切换到结果模块显示分析结果
                    self.switch_main_module("结果")

                else:
                    self.log_message("AI智能分析失败", "ERROR")

            except Exception as e:
                self.log_message(f"AI分析失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def run_comprehensive_analysis(self):
        """运行一键分析 - 分析已选出的股票"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("🚀 启动一键综合分析...", "INFO")

                # 检查是否有已选出的股票数据
                selected_stocks = self.get_selected_stocks()
                if not selected_stocks:
                    self.log_message("⚠️ 未发现已选股票，请先运行选股功能", "WARNING")
                    messagebox.showwarning("提示",
                                         "请先在选股模块中运行热门股或消息面选股，\n"
                                         "获取股票数据后再进行一键分析。")
                    return

                self.log_message(f"📊 发现 {len(selected_stocks)} 只待分析股票", "INFO")

                # 1. 技术面分析
                self.log_message("第1步: 技术面分析", "INFO")
                tech_result = self.run_technical_analysis_on_stocks(selected_stocks)
                if tech_result:
                    self.log_message("✅ 技术面分析完成", "SUCCESS")
                else:
                    self.log_message("⚠️ 技术面分析部分失败", "WARNING")

                # 2. 基本面分析
                self.log_message("第2步: 基本面分析", "INFO")
                fundamental_result = self.run_fundamental_analysis_on_stocks(selected_stocks)
                if fundamental_result:
                    self.log_message("✅ 基本面分析完成", "SUCCESS")
                else:
                    self.log_message("⚠️ 基本面分析部分失败", "WARNING")

                # 3. AI智能分析
                self.log_message("第3步: AI智能分析", "INFO")
                ai_result = self.run_ai_analysis_on_stocks(selected_stocks)
                if ai_result:
                    self.log_message("✅ AI智能分析完成", "SUCCESS")
                else:
                    self.log_message("⚠️ AI智能分析部分失败", "WARNING")

                # 4. 风险评估
                self.log_message("第4步: 风险评估", "INFO")
                risk_result = self.run_risk_assessment_on_stocks(selected_stocks)
                if risk_result:
                    self.log_message("✅ 风险评估完成", "SUCCESS")
                else:
                    self.log_message("⚠️ 风险评估部分失败", "WARNING")

                # 5. 生成综合报告
                self.log_message("第5步: 生成综合分析报告", "INFO")
                report_result = self.generate_comprehensive_report(selected_stocks)
                if report_result:
                    self.log_message("✅ 综合报告生成完成", "SUCCESS")
                else:
                    self.log_message("⚠️ 报告生成部分失败", "WARNING")

                # 6. 加载结果到界面
                self.log_message("第6步: 更新界面显示", "INFO")
                self.update_analysis_results()

                self.log_message("🎉 一键综合分析完成!", "SUCCESS")

                # 切换到结果模块显示
                self.switch_main_module("结果")

                # 显示分析完成信息
                analysis_summary = self.get_analysis_summary()
                if messagebox.askyesno("分析完成",
                                     f"🎉 一键综合分析已完成！\n\n"
                                     f"📊 分析股票: {len(selected_stocks)} 只\n"
                                     f"✅ 技术面分析: {'完成' if tech_result else '部分完成'}\n"
                                     f"✅ 基本面分析: {'完成' if fundamental_result else '部分完成'}\n"
                                     f"✅ AI智能分析: {'完成' if ai_result else '部分完成'}\n"
                                     f"✅ 风险评估: {'完成' if risk_result else '部分完成'}\n"
                                     f"✅ 综合报告: {'完成' if report_result else '部分完成'}\n\n"
                                     f"是否查看详细分析报告？"):
                    self.show_analysis_report()

            except Exception as e:
                self.log_message(f"一键分析失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def get_selected_stocks(self):
        """获取已选出的股票列表"""
        selected_stocks = []

        try:
            # 1. 从热门股数据中获取
            hot_files = ['人气榜.csv', '飙升榜.csv']
            for filename in hot_files:
                if os.path.exists(filename):
                    with open(filename, 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        for row in reader:
                            stock_info = {
                                'code': row.get('股票代码', ''),
                                'name': row.get('股票名称', ''),
                                'source': f'热门股-{filename[:-4]}',
                                'price': row.get('当前价格', ''),
                                'change': row.get('涨跌幅', ''),
                                'volume': row.get('成交量', '')
                            }
                            if stock_info['code']:
                                selected_stocks.append(stock_info)

            # 2. 从消息面选股数据中获取
            if os.path.exists('消息面推荐股票.csv'):
                with open('消息面推荐股票.csv', 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        stock_info = {
                            'code': row.get('股票代码', ''),
                            'name': row.get('股票名称', ''),
                            'source': '消息面选股',
                            'sentiment_score': row.get('情感得分', ''),
                            'mention_count': row.get('提及次数', ''),
                            'reason': row.get('推荐理由', '')
                        }
                        if stock_info['code']:
                            selected_stocks.append(stock_info)

            # 3. 去重处理
            unique_stocks = {}
            for stock in selected_stocks:
                code = stock['code']
                if code not in unique_stocks:
                    unique_stocks[code] = stock
                else:
                    # 合并来源信息
                    existing = unique_stocks[code]
                    existing['source'] += f", {stock['source']}"

            return list(unique_stocks.values())

        except Exception as e:
            self.log_message(f"获取已选股票失败: {str(e)}", "ERROR")
            return []

    def run_technical_analysis_on_stocks(self, stocks):
        """对选出的股票进行技术面分析"""
        try:
            self.log_message(f"📈 开始技术面分析 {len(stocks)} 只股票", "INFO")

            # 检查是否有技术分析脚本
            analysis_scripts = [
                "技术指标分析.py",
                "技术面分析.py",
                "股票技术分析.py"
            ]

            script_to_run = None
            for script in analysis_scripts:
                if os.path.exists(script):
                    script_to_run = script
                    break

            if script_to_run:
                # 创建股票列表文件供分析脚本使用
                stocks_file = "待分析股票列表.csv"
                with open(stocks_file, 'w', encoding='utf-8', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(['股票代码', '股票名称', '来源'])
                    for stock in stocks:
                        writer.writerow([stock['code'], stock['name'], stock['source']])

                # 运行技术分析脚本
                result = subprocess.run([sys.executable, script_to_run],
                                      cwd=os.getcwd(),
                                      capture_output=True,
                                      text=True,
                                      timeout=120)

                if result.returncode == 0:
                    self.log_message("✅ 技术面分析脚本执行成功", "SUCCESS")
                    return True
                else:
                    self.log_message(f"⚠️ 技术面分析脚本执行失败: {result.stderr[:100]}", "WARNING")
            else:
                self.log_message("⚠️ 未找到技术分析脚本，使用内置分析", "WARNING")
                # 使用内置简单技术分析
                self.simple_technical_analysis(stocks)
                return True

        except Exception as e:
            self.log_message(f"技术面分析失败: {str(e)}", "ERROR")

        return False

    def run_fundamental_analysis_on_stocks(self, stocks):
        """对选出的股票进行基本面分析"""
        try:
            self.log_message(f"📊 开始基本面分析 {len(stocks)} 只股票", "INFO")

            # 检查是否有基本面分析脚本
            analysis_scripts = [
                "基本面分析.py",
                "财务分析.py",
                "股票基本面分析.py"
            ]

            script_to_run = None
            for script in analysis_scripts:
                if os.path.exists(script):
                    script_to_run = script
                    break

            if script_to_run:
                result = subprocess.run([sys.executable, script_to_run],
                                      cwd=os.getcwd(),
                                      capture_output=True,
                                      text=True,
                                      timeout=180)

                if result.returncode == 0:
                    self.log_message("✅ 基本面分析脚本执行成功", "SUCCESS")
                    return True
                else:
                    self.log_message(f"⚠️ 基本面分析脚本执行失败", "WARNING")
            else:
                self.log_message("⚠️ 未找到基本面分析脚本，使用内置分析", "WARNING")
                # 使用内置简单基本面分析
                self.simple_fundamental_analysis(stocks)
                return True

        except Exception as e:
            self.log_message(f"基本面分析失败: {str(e)}", "ERROR")

        return False

    def run_ai_analysis_on_stocks(self, stocks):
        """对选出的股票进行AI智能分析"""
        try:
            self.log_message(f"🤖 开始AI智能分析 {len(stocks)} 只股票", "INFO")

            # 检查是否有AI分析脚本
            analysis_scripts = [
                "AI智能选股分析.py",
                "AI股票分析.py",
                "智能分析.py"
            ]

            script_to_run = None
            for script in analysis_scripts:
                if os.path.exists(script):
                    script_to_run = script
                    break

            if script_to_run:
                result = subprocess.run([sys.executable, script_to_run],
                                      cwd=os.getcwd(),
                                      capture_output=True,
                                      text=True,
                                      timeout=240)

                if result.returncode == 0:
                    self.log_message("✅ AI智能分析脚本执行成功", "SUCCESS")
                    return True
                else:
                    self.log_message(f"⚠️ AI智能分析脚本执行失败", "WARNING")
            else:
                self.log_message("⚠️ 未找到AI分析脚本，使用内置分析", "WARNING")
                # 使用内置简单AI分析
                self.simple_ai_analysis(stocks)
                return True

        except Exception as e:
            self.log_message(f"AI智能分析失败: {str(e)}", "ERROR")

        return False

    def run_risk_assessment_on_stocks(self, stocks):
        """对选出的股票进行风险评估"""
        try:
            self.log_message(f"⚖️ 开始风险评估 {len(stocks)} 只股票", "INFO")

            # 检查是否有风险评估脚本
            analysis_scripts = [
                "风险评估.py",
                "股票风险分析.py",
                "投资风险评估.py"
            ]

            script_to_run = None
            for script in analysis_scripts:
                if os.path.exists(script):
                    script_to_run = script
                    break

            if script_to_run:
                result = subprocess.run([sys.executable, script_to_run],
                                      cwd=os.getcwd(),
                                      capture_output=True,
                                      text=True,
                                      timeout=120)

                if result.returncode == 0:
                    self.log_message("✅ 风险评估脚本执行成功", "SUCCESS")
                    return True
                else:
                    self.log_message(f"⚠️ 风险评估脚本执行失败", "WARNING")
            else:
                self.log_message("⚠️ 未找到风险评估脚本，使用内置评估", "WARNING")
                # 使用内置简单风险评估
                self.simple_risk_assessment(stocks)
                return True

        except Exception as e:
            self.log_message(f"风险评估失败: {str(e)}", "ERROR")

        return False

    def generate_comprehensive_report(self, stocks):
        """生成综合分析报告"""
        try:
            self.log_message("📝 生成综合分析报告", "INFO")

            # 检查是否有报告生成脚本
            report_scripts = [
                "综合分析报告生成器.py",
                "AI选股报告生成器.py",
                "分析报告生成器.py"
            ]

            script_to_run = None
            for script in report_scripts:
                if os.path.exists(script):
                    script_to_run = script
                    break

            if script_to_run:
                result = subprocess.run([sys.executable, script_to_run],
                                      cwd=os.getcwd(),
                                      capture_output=True,
                                      text=True,
                                      timeout=60)

                if result.returncode == 0:
                    self.log_message("✅ 综合报告生成成功", "SUCCESS")
                    return True
                else:
                    self.log_message(f"⚠️ 报告生成脚本执行失败", "WARNING")

            # 生成简单的综合报告
            self.generate_simple_report(stocks)
            return True

        except Exception as e:
            self.log_message(f"生成综合报告失败: {str(e)}", "ERROR")

        return False

    def update_analysis_results(self):
        """更新分析结果到界面"""
        try:
            # 重新加载选股数据
            if hasattr(self, 'current_selection_type'):
                if self.current_selection_type == "hot":
                    self.load_existing_stock_data()
                elif self.current_selection_type == "news":
                    self.load_news_selection_data()

            # 更新统计信息
            self.update_statistics()

        except Exception as e:
            self.log_message(f"更新界面失败: {str(e)}", "ERROR")

    def get_analysis_summary(self):
        """获取分析摘要"""
        return {
            'total_stocks': 0,
            'analysis_complete': True,
            'report_generated': True
        }

    def simple_technical_analysis(self, stocks):
        """简单的技术面分析"""
        try:
            analysis_results = []
            for stock in stocks:
                result = {
                    'code': stock['code'],
                    'name': stock['name'],
                    'technical_score': 'B+',  # 模拟技术评分
                    'trend': '上升趋势',
                    'support': '技术支撑良好',
                    'resistance': '阻力位适中'
                }
                analysis_results.append(result)

            # 保存分析结果
            with open('技术面分析结果.json', 'w', encoding='utf-8') as f:
                json.dump(analysis_results, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.log_message(f"简单技术分析失败: {str(e)}", "ERROR")

    def simple_fundamental_analysis(self, stocks):
        """简单的基本面分析"""
        try:
            analysis_results = []
            for stock in stocks:
                result = {
                    'code': stock['code'],
                    'name': stock['name'],
                    'fundamental_score': 'A-',  # 模拟基本面评分
                    'pe_ratio': '适中',
                    'growth': '稳定增长',
                    'financial_health': '财务健康'
                }
                analysis_results.append(result)

            # 保存分析结果
            with open('基本面分析结果.json', 'w', encoding='utf-8') as f:
                json.dump(analysis_results, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.log_message(f"简单基本面分析失败: {str(e)}", "ERROR")

    def simple_ai_analysis(self, stocks):
        """简单的AI分析"""
        try:
            analysis_results = []
            for stock in stocks:
                result = {
                    'code': stock['code'],
                    'name': stock['name'],
                    'ai_score': 'B+',  # 模拟AI评分
                    'recommendation': '建议关注',
                    'confidence': '75%',
                    'reason': 'AI综合分析显示该股票具有投资价值'
                }
                analysis_results.append(result)

            # 保存分析结果
            with open('AI分析结果.json', 'w', encoding='utf-8') as f:
                json.dump(analysis_results, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.log_message(f"简单AI分析失败: {str(e)}", "ERROR")

    def simple_risk_assessment(self, stocks):
        """简单的风险评估"""
        try:
            assessment_results = []
            for stock in stocks:
                result = {
                    'code': stock['code'],
                    'name': stock['name'],
                    'risk_level': '中等风险',
                    'volatility': '适中',
                    'liquidity': '良好',
                    'market_risk': '可控'
                }
                assessment_results.append(result)

            # 保存评估结果
            with open('风险评估结果.json', 'w', encoding='utf-8') as f:
                json.dump(assessment_results, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.log_message(f"简单风险评估失败: {str(e)}", "ERROR")

    def generate_simple_report(self, stocks):
        """生成简单的综合报告"""
        try:
            report_content = f"""
# 智股投一键分析报告

## 分析概况
- 分析股票数量: {len(stocks)} 只
- 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 分析模块: 技术面、基本面、AI智能、风险评估

## 股票列表
"""
            for i, stock in enumerate(stocks, 1):
                report_content += f"{i}. {stock['code']} {stock['name']} (来源: {stock['source']})\n"

            report_content += """
## 分析结论
基于多维度分析，建议投资者关注以上股票的后续表现，
结合个人风险承受能力做出投资决策。

## 风险提示
股市有风险，投资需谨慎。本报告仅供参考，不构成投资建议。
"""

            # 保存报告
            with open('一键分析报告.md', 'w', encoding='utf-8') as f:
                f.write(report_content)

        except Exception as e:
            self.log_message(f"生成简单报告失败: {str(e)}", "ERROR")

    # ==================== 数据加载功能实现 ====================

    def load_existing_stock_data(self):
        """加载现有的股票数据到界面"""
        try:
            self.log_message("📊 加载现有股票数据...", "INFO")

            # 加载热门股数据
            self.load_hot_stocks_data()

            # 更新统计信息
            self.update_statistics()

        except Exception as e:
            self.log_message(f"加载股票数据失败: {str(e)}", "ERROR")

    def load_hot_stocks_data(self):
        """加载热门股数据"""
        try:
            # 清空现有数据
            if hasattr(self, 'popularity_tree'):
                for item in self.popularity_tree.get_children():
                    self.popularity_tree.delete(item)

            if hasattr(self, 'soaring_tree'):
                for item in self.soaring_tree.get_children():
                    self.soaring_tree.delete(item)

            # 加载人气榜数据
            if os.path.exists('popularity.csv'):
                try:
                    with open('popularity.csv', 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        count = 0
                        for i, row in enumerate(reader, 1):
                            if hasattr(self, 'popularity_tree'):
                                # 适配不同的列名格式
                                stock_code = row.get('股票代码', row.get('code', ''))
                                stock_name = row.get('股票名称', row.get('name', ''))
                                price = row.get('当前价格', row.get('price', ''))
                                change = row.get('涨跌幅', row.get('change', ''))
                                rank = row.get('排名', str(i))
                                rank_change = row.get('较昨日排名', '')

                                self.popularity_tree.insert('', 'end', values=(
                                    rank,  # 排名
                                    stock_code,  # 股票代码
                                    stock_name,  # 股票名称
                                    price,  # 当前价格
                                    change,  # 涨跌幅
                                    rank_change  # 较昨日排名
                                ))
                                count += 1
                    self.log_message(f"✅ 人气榜数据加载完成 ({count} 条)", "SUCCESS")
                except Exception as e:
                    self.log_message(f"加载人气榜数据失败: {str(e)}", "WARNING")

            # 加载飙升榜数据
            if os.path.exists('soaring.csv'):
                try:
                    with open('soaring.csv', 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        count = 0
                        for i, row in enumerate(reader, 1):
                            if hasattr(self, 'soaring_tree'):
                                # 适配不同的列名格式
                                stock_code = row.get('股票代码', row.get('code', ''))
                                stock_name = row.get('股票名称', row.get('name', ''))
                                price = row.get('当前价格', row.get('price', ''))
                                change = row.get('涨跌幅', row.get('change', ''))
                                rank = row.get('排名', str(i))
                                rank_change = row.get('较昨日排名', '')

                                self.soaring_tree.insert('', 'end', values=(
                                    rank,  # 排名
                                    stock_code,  # 股票代码
                                    stock_name,  # 股票名称
                                    price,  # 当前价格
                                    change,  # 涨跌幅
                                    rank_change  # 较昨日排名
                                ))
                                count += 1
                    self.log_message(f"✅ 飙升榜数据加载完成 ({count} 条)", "SUCCESS")
                except Exception as e:
                    self.log_message(f"加载飙升榜数据失败: {str(e)}", "WARNING")

        except Exception as e:
            self.log_message(f"加载热门股数据失败: {str(e)}", "ERROR")

    def load_news_selection_data(self):
        """加载消息面选股数据"""
        try:
            # 清空现有数据
            if hasattr(self, 'topics_tree'):
                for item in self.topics_tree.get_children():
                    self.topics_tree.delete(item)

            if hasattr(self, 'news_stocks_tree'):
                for item in self.news_stocks_tree.get_children():
                    self.news_stocks_tree.delete(item)

            # 加载热门话题数据
            if os.path.exists('消息面选股结果.json'):
                try:
                    with open('消息面选股结果.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    hot_topics = data.get('hot_topics', [])
                    for i, topic in enumerate(hot_topics, 1):
                        if hasattr(self, 'topics_tree'):
                            self.topics_tree.insert('', 'end', values=(
                                str(i),  # 排名
                                topic.get('title', '')[:50] + '...' if len(topic.get('title', '')) > 50 else topic.get('title', ''),  # 话题标题
                                topic.get('热度', 'N/A'),  # 热度
                                topic.get('情感倾向', '中性'),  # 情感倾向
                                topic.get('相关股票数', '0'),  # 相关股票数
                                topic.get('timestamp', '')  # 发布时间
                            ))
                    self.log_message(f"✅ 热门话题数据加载完成 ({len(hot_topics)} 个)", "SUCCESS")
                except Exception as e:
                    self.log_message(f"加载热门话题数据失败: {str(e)}", "WARNING")

            # 加载消息面推荐股票数据
            if os.path.exists('消息面推荐股票.csv'):
                try:
                    with open('消息面推荐股票.csv', 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        for row in reader:
                            if hasattr(self, 'news_stocks_tree'):
                                self.news_stocks_tree.insert('', 'end', values=(
                                    row.get('排名', ''),
                                    row.get('股票代码', ''),
                                    row.get('股票名称', ''),
                                    row.get('情感得分', ''),
                                    row.get('提及次数', ''),
                                    row.get('推荐等级', ''),
                                    row.get('推荐理由', '')
                                ))
                    self.log_message("✅ 消息面推荐股票数据加载完成", "SUCCESS")
                except Exception as e:
                    self.log_message(f"加载消息面推荐股票数据失败: {str(e)}", "WARNING")

        except Exception as e:
            self.log_message(f"加载消息面数据失败: {str(e)}", "ERROR")

    def update_statistics(self):
        """更新统计信息"""
        try:
            stats = {
                "热门股票": 0,
                "消息面股票": 0,
                "分析报告": 0,
                "推荐建议": 0
            }

            # 统计热门股数量
            if os.path.exists('popularity.csv'):
                with open('popularity.csv', 'r', encoding='utf-8') as f:
                    stats["热门股票"] = len(f.readlines()) - 1  # 减去标题行

            if os.path.exists('soaring.csv'):
                with open('soaring.csv', 'r', encoding='utf-8') as f:
                    soaring_count = len(f.readlines()) - 1
                stats["热门股票"] += soaring_count

            # 统计消息面股票数量
            if os.path.exists('消息面推荐股票.csv'):
                with open('消息面推荐股票.csv', 'r', encoding='utf-8') as f:
                    stats["消息面股票"] = len(f.readlines()) - 1

            # 统计分析报告数量
            report_files = ['AI智能选股分析报告.html', '消息面选股分析报告.html', '股票分析报告.html']
            stats["分析报告"] = sum(1 for f in report_files if os.path.exists(f))

            # 统计推荐建议数量
            advice_files = ['AI最终购买建议.csv', '推荐股票汇总.csv']
            stats["推荐建议"] = sum(1 for f in advice_files if os.path.exists(f))

            # 更新界面统计标签
            if hasattr(self, 'stats_labels'):
                for key, value in stats.items():
                    if key in self.stats_labels:
                        self.stats_labels[key].config(text=f"{key}: {value}")

        except Exception as e:
            self.log_message(f"更新统计信息失败: {str(e)}", "ERROR")

    def log_message(self, message, level="INFO"):
        """记录日志消息"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")

            # 根据级别设置颜色
            color_map = {
                "INFO": "#2c3e50",
                "SUCCESS": "#27ae60",
                "WARNING": "#f39c12",
                "ERROR": "#e74c3c"
            }

            color = color_map.get(level, "#2c3e50")

            # 格式化消息
            formatted_message = f"[{timestamp}] {message}"

            # 输出到控制台
            print(formatted_message)

            # 如果有日志文本框，也输出到界面
            if hasattr(self, 'log_text'):
                self.log_text.config(state='normal')
                self.log_text.insert(tk.END, formatted_message + "\n")
                self.log_text.see(tk.END)
                self.log_text.config(state='disabled')

        except Exception as e:
            print(f"日志记录失败: {str(e)}")

    def load_initial_data(self):
        """加载初始数据"""
        try:
            self.log_message("🚀 智股投系统启动", "INFO")
            self.log_message("📊 正在加载初始数据...", "INFO")

            # 初始化当前选股类型
            if not hasattr(self, 'current_selection_type'):
                self.current_selection_type = "hot"

            # 初始化当前模块
            if not hasattr(self, 'current_module'):
                self.current_module = "选股"

            # 加载现有数据（如果存在）
            self.load_existing_stock_data()

            self.log_message("✅ 初始数据加载完成", "SUCCESS")

        except Exception as e:
            self.log_message(f"初始数据加载失败: {str(e)}", "ERROR")

    # ==================== 分析模块功能实现 ====================

    def run_technical_analysis(self):
        """运行技术指标分析"""
        if self.current_module != "分析":
            self.switch_main_module("分析")

        analysis_text = """
📊 技术指标分析

正在分析技术指标...

📈 分析维度：
• 趋势指标：MA、MACD、布林带
• 震荡指标：RSI、KDJ、威廉指标
• 成交量指标：OBV、成交量比率
• 动量指标：ROC、MTM

💡 技术分析基于QMT历史数据，包含：
• K线形态识别
• 支撑阻力位分析
• 买卖信号判断
• 风险控制建议

⚠️ 注意：技术分析仅供参考，需结合基本面分析
        """

        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(tk.END, analysis_text)
        self.log_message("技术指标分析功能展示", "INFO")

    def run_sentiment_analysis(self):
        """运行情感分析"""
        if self.current_module != "分析":
            self.switch_main_module("分析")

        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在进行情感分析...", "INFO")

                self.analysis_text.delete(1.0, tk.END)
                self.analysis_text.insert(tk.END, "💭 正在分析市场情绪...\n\n")
                self.analysis_text.update()

                # 检查消息面数据
                if os.path.exists('消息面选股结果.json'):
                    with open('消息面选股结果.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    hot_topics = data.get('hot_topics', [])
                    recommendations = data.get('recommendations', [])

                    # 分析情感分布
                    sentiment_analysis = self.analyze_sentiment_distribution(hot_topics, recommendations)

                    self.analysis_text.delete(1.0, tk.END)
                    self.analysis_text.insert(tk.END, sentiment_analysis)

                    self.log_message("情感分析完成", "SUCCESS")
                else:
                    self.analysis_text.insert(tk.END, "❌ 未找到消息面数据\n请先运行消息面选股功能")
                    self.log_message("情感分析失败: 缺少数据", "ERROR")

            except Exception as e:
                self.analysis_text.insert(tk.END, f"❌ 情感分析失败: {str(e)}")
                self.log_message(f"情感分析失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def run_risk_assessment(self):
        """运行风险评估"""
        if self.current_module != "分析":
            self.switch_main_module("分析")

        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在进行风险评估...", "INFO")

                self.analysis_text.delete(1.0, tk.END)
                self.analysis_text.insert(tk.END, "⚖️ 正在评估投资风险...\n\n")
                self.analysis_text.update()

                # 检查AI分析结果
                if os.path.exists('AI智能选股分析结果.json'):
                    with open('AI智能选股分析结果.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    risk_analysis = self.analyze_investment_risks(data)

                    self.analysis_text.delete(1.0, tk.END)
                    self.analysis_text.insert(tk.END, risk_analysis)

                    self.log_message("风险评估完成", "SUCCESS")
                else:
                    self.analysis_text.insert(tk.END, "❌ 未找到AI分析数据\n请先运行AI智能分析功能")
                    self.log_message("风险评估失败: 缺少数据", "ERROR")

            except Exception as e:
                self.analysis_text.insert(tk.END, f"❌ 风险评估失败: {str(e)}")
                self.log_message(f"风险评估失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def run_ai_detailed_analysis(self):
        """运行AI详细分析"""
        if self.current_module != "分析":
            self.switch_main_module("分析")

        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在进行AI详细分析...", "INFO")

                self.analysis_text.delete(1.0, tk.END)
                self.analysis_text.insert(tk.END, "🤖 正在进行AI详细分析...\n\n")
                self.analysis_text.update()

                # 检查AI分析结果
                if os.path.exists('AI智能选股分析结果.json'):
                    with open('AI智能选股分析结果.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    detailed_analysis = self.generate_detailed_ai_analysis(data)

                    self.analysis_text.delete(1.0, tk.END)
                    self.analysis_text.insert(tk.END, detailed_analysis)

                    self.log_message("AI详细分析完成", "SUCCESS")
                else:
                    self.analysis_text.insert(tk.END, "❌ 未找到AI分析数据\n请先运行AI智能分析功能")
                    self.log_message("AI详细分析失败: 缺少数据", "ERROR")

            except Exception as e:
                self.analysis_text.insert(tk.END, f"❌ AI详细分析失败: {str(e)}")
                self.log_message(f"AI详细分析失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    # ==================== 结果模块功能实现 ====================

    def show_recommendations(self):
        """显示推荐列表"""
        if self.current_module != "结果":
            self.switch_main_module("结果")
        self.log_message("显示推荐列表", "INFO")

    def show_analysis_report(self):
        """显示分析报告"""
        if self.current_module != "结果":
            self.switch_main_module("结果")
        self.log_message("显示分析报告", "INFO")

    def show_investment_advice(self):
        """显示投资建议"""
        if self.current_module != "结果":
            self.switch_main_module("结果")
        self.log_message("显示投资建议", "INFO")

    def show_real_time_monitor(self):
        """显示实时监控"""
        if self.current_module != "结果":
            self.switch_main_module("结果")
        self.log_message("实时监控功能", "INFO")
        messagebox.showinfo("实时监控", "实时监控功能正在开发中\n\n将提供:\n• 实时价格监控\n• 涨跌幅提醒\n• 成交量异动提醒")

    # ==================== 交易模块功能实现 ====================

    def open_simulation_trading(self):
        """打开模拟交易"""
        if self.current_module != "交易":
            self.switch_main_module("交易")
        self.log_message("模拟交易功能", "INFO")
        messagebox.showinfo("模拟交易", "模拟交易功能\n\n• 使用虚拟资金进行交易练习\n• 验证投资策略效果\n• 无真实资金风险")

    def open_real_trading(self):
        """打开实盘交易"""
        if self.current_module != "交易":
            self.switch_main_module("交易")
        self.log_message("实盘交易功能", "INFO")
        messagebox.showinfo("实盘交易", "实盘交易功能\n\n• 基于miniqmt接口\n• 连接券商进行真实交易\n• 需要配置券商账户")

    def show_position_management(self):
        """显示持仓管理"""
        if self.current_module != "交易":
            self.switch_main_module("交易")
        self.log_message("持仓管理功能", "INFO")

    def show_trading_records(self):
        """显示交易记录"""
        if self.current_module != "交易":
            self.switch_main_module("交易")
        self.log_message("交易记录功能", "INFO")

    # ==================== 配置模块功能实现 ====================

    def open_system_settings(self):
        """打开系统设置"""
        if self.current_module != "配置":
            self.switch_main_module("配置")
        self.log_message("系统设置功能", "INFO")

    def open_data_source_config(self):
        """打开数据源配置"""
        if self.current_module != "配置":
            self.switch_main_module("配置")
        self.log_message("数据源配置功能", "INFO")

    def open_trading_config(self):
        """打开交易配置"""
        if self.current_module != "配置":
            self.switch_main_module("配置")
        self.log_message("交易配置功能", "INFO")

    def open_log_management(self):
        """打开日志管理"""
        if self.current_module != "配置":
            self.switch_main_module("配置")
        self.log_message("日志管理功能", "INFO")

    # ==================== 分析工具方法 ====================

    def analyze_sentiment_distribution(self, hot_topics, recommendations):
        """分析情感分布"""
        try:
            positive_count = 0
            negative_count = 0
            neutral_count = 0

            # 分析推荐股票的情感分布
            for rec in recommendations:
                sentiment_score = rec.get('sentiment_score', 0)
                if sentiment_score > 0.5:
                    positive_count += 1
                elif sentiment_score < -0.5:
                    negative_count += 1
                else:
                    neutral_count += 1

            total = len(recommendations)
            if total == 0:
                return "❌ 没有足够的数据进行情感分析"

            analysis_text = f"""
💭 市场情感分析结果

📊 情感分布统计：
• 😊 积极情感: {positive_count} 只 ({positive_count/total*100:.1f}%)
• 😐 中性情感: {neutral_count} 只 ({neutral_count/total*100:.1f}%)
• 😟 消极情感: {negative_count} 只 ({negative_count/total*100:.1f}%)

📈 话题热度分析：
• 总话题数: {len(hot_topics)} 个
• 平均热度: {sum([t.get('热度', 0) for t in hot_topics])/len(hot_topics):.1f}
• 高热度话题: {len([t for t in hot_topics if t.get('热度', 0) > 100])} 个

💡 市场情绪判断：
"""

            if positive_count > negative_count * 1.5:
                analysis_text += "🟢 市场情绪偏向乐观，投资者信心较强"
            elif negative_count > positive_count * 1.5:
                analysis_text += "🔴 市场情绪偏向悲观，建议谨慎投资"
            else:
                analysis_text += "🟡 市场情绪相对平衡，可适度参与"

            return analysis_text

        except Exception as e:
            return f"❌ 情感分析处理失败: {str(e)}"

    def analyze_investment_risks(self, data):
        """分析投资风险"""
        try:
            final_rec = data.get('final_recommendations', {})
            complete_analysis = data.get('complete_analysis', [])

            if not complete_analysis:
                return "❌ 没有足够的数据进行风险分析"

            # 风险等级统计
            risk_stats = {}
            for stock in complete_analysis:
                risk_level = stock.get('risk_level', '未知风险')
                risk_stats[risk_level] = risk_stats.get(risk_level, 0) + 1

            # 高分股票风险分析
            high_score_stocks = [s for s in complete_analysis if s.get('ai_score', 0) >= 70]
            low_risk_high_score = [s for s in high_score_stocks if '低风险' in s.get('risk_level', '')]

            analysis_text = f"""
⚖️ 投资风险评估报告

📊 整体风险分布：
"""

            for risk_level, count in risk_stats.items():
                percentage = count / len(complete_analysis) * 100
                analysis_text += f"• {risk_level}: {count} 只 ({percentage:.1f}%)\n"

            analysis_text += f"""

🎯 高分股票风险分析：
• 高分股票总数: {len(high_score_stocks)} 只
• 低风险高分股票: {len(low_risk_high_score)} 只
• 风险收益比: {len(low_risk_high_score)/max(len(high_score_stocks), 1)*100:.1f}%

💡 风险控制建议：
• 🟢 优先选择低风险高分股票
• 🟡 中等风险股票适量配置
• 🔴 高风险股票谨慎参与
• 📊 建议分散投资，控制单只股票仓位

⚠️ 风险提示：
• 股市有风险，投资需谨慎
• 本分析仅供参考，不构成投资建议
• 请根据个人风险承受能力做决策
• 建议结合更多信息进行综合判断
            """

            return analysis_text

        except Exception as e:
            return f"❌ 风险分析处理失败: {str(e)}"

    def generate_detailed_ai_analysis(self, data):
        """生成详细AI分析"""
        try:
            final_rec = data.get('final_recommendations', {})
            data_sources = data.get('data_sources', {})
            complete_analysis = data.get('complete_analysis', [])

            analysis_text = f"""
🤖 AI智能分析详细报告

📊 数据概览：
• 热门股数据: {data_sources.get('hot_stocks_count', 0)} 只
• 消息面数据: {data_sources.get('news_stocks_count', 0)} 只
• 合并分析: {data_sources.get('combined_count', 0)} 只
• 高分股票: {final_rec.get('high_score_count', 0)} 只

🎯 AI推荐分布：
• 🔥 强烈建议买入: {len(final_rec.get('strong_buy', []))} 只
• ⭐ 建议买入: {len(final_rec.get('buy', []))} 只
• 👀 可以考虑: {len(final_rec.get('consider', []))} 只

💡 AI分析算法：
• 综合评分: 热门股得分(40%) + 消息面得分(35%) + 数据源加分(25%)
• 风险评估: 基于AI得分和数据完整性的5级风险评估
• 投资建议: 85+强烈推荐, 75+建议买入, 65+可以考虑
• 双重验证: 同时出现在热门股和消息面的股票获得额外加分

📈 推荐股票详情：
"""

            # 添加强烈推荐股票详情
            strong_buy = final_rec.get('strong_buy', [])
            if strong_buy:
                analysis_text += "\n🔥 强烈建议买入股票:\n"
                for i, stock in enumerate(strong_buy[:5], 1):
                    analysis_text += f"{i}. {stock.get('code', '')} {stock.get('name', '')} - AI得分: {stock.get('ai_score', 0):.2f}\n"
                    analysis_text += f"   理由: {stock.get('ai_reason', '')}\n"

            # 添加一般推荐股票详情
            buy = final_rec.get('buy', [])
            if buy:
                analysis_text += "\n⭐ 建议买入股票:\n"
                for i, stock in enumerate(buy[:3], 1):
                    analysis_text += f"{i}. {stock.get('code', '')} {stock.get('name', '')} - AI得分: {stock.get('ai_score', 0):.2f}\n"
                    analysis_text += f"   理由: {stock.get('ai_reason', '')}\n"

            analysis_text += "\n📋 详细结果请查看结果模块的推荐列表"

            return analysis_text

        except Exception as e:
            return f"❌ AI详细分析失败: {str(e)}"

    def open_web_reports(self):
        """打开Web报告"""
        try:
            import webbrowser

            # 打开AI选股报告
            if os.path.exists("AI智能选股分析报告.html"):
                webbrowser.open(f"file:///{os.path.abspath('AI智能选股分析报告.html')}")
                self.log_message("已打开AI选股分析报告", "INFO")

            # 打开消息面选股报告
            if os.path.exists("消息面选股分析报告.html"):
                webbrowser.open(f"file:///{os.path.abspath('消息面选股分析报告.html')}")
                self.log_message("已打开消息面选股报告", "INFO")

        except Exception as e:
            self.log_message(f"打开Web报告失败: {str(e)}", "ERROR")

    def check_data_status(self):
        """检查数据状态"""
        try:
            from 后台采集 import get_collection_status
            status = get_collection_status()
            
            # 更新状态显示
            if hasattr(self, 'stats_labels'):
                total_count = 0
                for filename, info in status['files_exist'].items():
                    if info.get('exists'):
                        total_count += info.get('line_count', 0)
                self.stats_labels["热门股票"].configure(text=str(total_count))
            
            return status
        except ImportError:
            # 如果API不可用，使用简单检查
            total_count = 0
            for filename in ['popularity.csv', 'soaring.csv']:
                if os.path.exists(filename):
                    try:
                        with open(filename, 'r', encoding='utf-8') as f:
                            lines = len(f.readlines()) - 1
                        total_count += lines
                    except:
                        pass
            
            if hasattr(self, 'stats_labels'):
                self.stats_labels["热门股票"].configure(text=str(total_count))
            
            return {'total_count': total_count}

def main():
    """主函数"""
    root = tk.Tk()
    app = ZhiGuTouSystem(root)
    root.mainloop()

if __name__ == "__main__":
    main()

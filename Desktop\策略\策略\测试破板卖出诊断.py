#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试破板卖出功能诊断
诊断为什么破板卖出功能没有运行
"""

import os
import sys
import json
from datetime import datetime

def test_break_limit_sell_logic():
    """测试破板卖出逻辑"""
    print("🧪 测试破板卖出逻辑")
    print("=" * 60)
    
    # 模拟破板卖出逻辑
    def simulate_break_limit_sell(stock_code="002905.SZ"):
        """模拟破板卖出逻辑"""
        try:
            # 模拟条件检查
            conditions = {
                "in_trade_time": True,  # 是否在交易时间
                "has_position": True,   # 是否有持仓
                "is_limit_up": False,   # 是否涨停（破板时为False）
                "buy_signal": False,    # 是否触发买入信号
                "can_sell": True        # 是否可以卖出
            }
            
            print(f"📊 股票 {stock_code} 破板卖出条件检查:")
            print(f"  交易时间: {'✅ 是' if conditions['in_trade_time'] else '❌ 否'}")
            print(f"  有持仓: {'✅ 是' if conditions['has_position'] else '❌ 否'}")
            print(f"  是否涨停: {'❌ 是（未破板）' if conditions['is_limit_up'] else '✅ 否（已破板）'}")
            print(f"  买入信号: {'❌ 触发（不卖出）' if conditions['buy_signal'] else '✅ 未触发（可卖出）'}")
            print(f"  可卖出: {'✅ 是' if conditions['can_sell'] else '❌ 否'}")
            
            # 模拟破板卖出逻辑
            if not conditions['in_trade_time']:
                print(f"❌ 不在交易时间，跳过破板卖出")
                return False, "不在交易时间"
            
            if not conditions['has_position']:
                print(f"❌ 无持仓，跳过破板卖出")
                return False, "无持仓"
            
            if conditions['is_limit_up']:
                print(f"❌ 仍在涨停，跳过破板卖出")
                return False, "仍在涨停"
            
            # 破板立即卖出
            if not conditions['is_limit_up']:
                if conditions['buy_signal']:
                    print(f"❌ 破板但触发买入信号，保留不卖出")
                    return False, "触发买入信号"
                else:
                    print(f"✅ 破板！立即卖出")
                    if conditions['can_sell']:
                        print(f"✅ 破板卖出成功")
                        return True, "破板卖出成功"
                    else:
                        print(f"❌ 破板卖出失败")
                        return False, "卖出失败"
            
            return False, "未知原因"
            
        except Exception as e:
            print(f"❌ 破板卖出异常: {e}")
            return False, f"异常: {e}"
    
    # 测试正常破板卖出
    print("📈 测试正常破板卖出...")
    result1, msg1 = simulate_break_limit_sell()
    print(f"结果: {'✅ 成功' if result1 else '❌ 失败'} - {msg1}")
    
    # 测试触发买入信号的情况
    def simulate_break_limit_sell_with_buy_signal():
        """模拟破板但触发买入信号的情况"""
        try:
            conditions = {
                "in_trade_time": True,
                "has_position": True,
                "is_limit_up": False,
                "buy_signal": True,  # 触发买入信号
                "can_sell": True
            }
            
            print(f"\n📊 股票 002905.SZ 破板但触发买入信号:")
            print(f"  交易时间: {'✅ 是' if conditions['in_trade_time'] else '❌ 否'}")
            print(f"  有持仓: {'✅ 是' if conditions['has_position'] else '❌ 否'}")
            print(f"  是否涨停: {'❌ 是（未破板）' if conditions['is_limit_up'] else '✅ 否（已破板）'}")
            print(f"  买入信号: {'❌ 触发（不卖出）' if conditions['buy_signal'] else '✅ 未触发（可卖出）'}")
            
            if not conditions['is_limit_up'] and conditions['buy_signal']:
                print(f"❌ 破板但触发买入信号，保留不卖出")
                return False, "触发买入信号"
            
            return True, "正常卖出"
            
        except Exception as e:
            return False, f"异常: {e}"
    
    print(f"\n📈 测试破板但触发买入信号...")
    result2, msg2 = simulate_break_limit_sell_with_buy_signal()
    print(f"结果: {'✅ 成功' if result2 else '❌ 失败'} - {msg2}")
    
    return result1 and not result2

def test_buy_signal_check():
    """测试买入信号检查"""
    print(f"\n🔍 测试买入信号检查...")
    
    # 模拟买入信号检查逻辑
    def simulate_buy_signal_check():
        """模拟买入信号检查"""
        try:
            # 模拟OBV因子检查
            obv_passed = True
            obv_score = 75
            obv_threshold = 70
            
            # 模拟防追高检查
            avoid_chasing_high = True
            
            print(f"📊 买入信号检查:")
            print(f"  OBV因子: {'✅ 通过' if obv_passed else '❌ 未通过'} (评分:{obv_score}, 阈值:{obv_threshold})")
            print(f"  防追高: {'✅ 通过' if avoid_chasing_high else '❌ 未通过'}")
            
            # 判断是否触发买入信号
            if obv_passed and obv_score >= obv_threshold and avoid_chasing_high:
                print(f"✅ 触发买入信号")
                return True, obv_score, "OBV因子通过且防追高通过"
            else:
                print(f"❌ 未触发买入信号")
                return False, 0, "条件不满足"
                
        except Exception as e:
            return False, 0, f"检查异常: {e}"
    
    result, score, reason = simulate_buy_signal_check()
    print(f"买入信号检查结果: {'✅ 触发' if result else '❌ 未触发'} - {reason}")
    
    return not result  # 买入信号未触发时，破板卖出应该成功

def test_position_check():
    """测试持仓检查"""
    print(f"\n🔍 测试持仓检查...")
    
    # 模拟持仓检查
    def simulate_position_check(stock_code="002905"):
        """模拟持仓检查"""
        try:
            # 模拟持仓数据
            positions = [
                {"code": "000001", "volume": 1000},
                {"code": "000002", "volume": 1000},
                {"code": "002905", "volume": 1000},  # 有持仓
            ]
            
            # 查找持仓
            target_position = None
            for pos in positions:
                if pos["code"] == stock_code and pos["volume"] > 0:
                    target_position = pos
                    break
            
            if target_position:
                print(f"✅ 找到 {stock_code} 的持仓: {target_position['volume']}股")
                return True, target_position
            else:
                print(f"❌ 未找到 {stock_code} 的持仓")
                return False, None
                
        except Exception as e:
            print(f"❌ 持仓检查异常: {e}")
            return False, None
    
    has_position, position = simulate_position_check()
    print(f"持仓检查结果: {'✅ 有持仓' if has_position else '❌ 无持仓'}")
    
    return has_position

def test_trade_time_check():
    """测试交易时间检查"""
    print(f"\n🔍 测试交易时间检查...")
    
    # 模拟交易时间检查
    def simulate_trade_time_check():
        """模拟交易时间检查"""
        try:
            # 模拟当前时间
            current_time = datetime.now().time()
            morning_start = datetime.time(9, 30)
            morning_end = datetime.time(11, 30)
            afternoon_start = datetime.time(13, 0)
            afternoon_end = datetime.time(15, 0)
            
            in_trade_time = (
                (morning_start <= current_time <= morning_end) or
                (afternoon_start <= current_time <= afternoon_end)
            )
            
            print(f"📊 交易时间检查:")
            print(f"  当前时间: {current_time}")
            print(f"  交易时间: {'✅ 是' if in_trade_time else '❌ 否'}")
            
            return in_trade_time
            
        except Exception as e:
            print(f"❌ 交易时间检查异常: {e}")
            return False
    
    in_trade_time = simulate_trade_time_check()
    print(f"交易时间检查结果: {'✅ 在交易时间' if in_trade_time else '❌ 不在交易时间'}")
    
    return in_trade_time

def analyze_original_log():
    """分析原始日志"""
    print(f"\n🔍 分析原始日志...")
    
    original_log = "2025-07-31 09:39:18][7303加了开市重启][SH000300][日线] 昨日涨停，已在待卖名单中：002905.SZ    今天破板没有卖出   破板卖出功能没有运行"
    
    print(f"原始日志: {original_log}")
    
    # 分析日志信息
    analysis = {
        "timestamp": "2025-07-31 09:39:18",
        "stock_code": "002905.SZ",
        "status": "昨日涨停，已在待卖名单中",
        "issue": "今天破板没有卖出，破板卖出功能没有运行"
    }
    
    print(f"📊 日志分析:")
    print(f"  时间: {analysis['timestamp']}")
    print(f"  股票: {analysis['stock_code']}")
    print(f"  状态: {analysis['status']}")
    print(f"  问题: {analysis['issue']}")
    
    # 可能的原因分析
    possible_reasons = [
        "1. 买入信号检查阻止了卖出",
        "2. 不在交易时间内",
        "3. 股票不在持仓中",
        "4. 涨停状态检查有误",
        "5. 卖出函数执行失败"
    ]
    
    print(f"\n💡 可能的原因:")
    for reason in possible_reasons:
        print(f"  {reason}")
    
    return True

def main():
    """主测试函数"""
    print("🎯 破板卖出功能诊断")
    print("=" * 60)
    
    # 分析原始日志
    test1_result = analyze_original_log()
    
    # 测试破板卖出逻辑
    test2_result = test_break_limit_sell_logic()
    
    # 测试买入信号检查
    test3_result = test_buy_signal_check()
    
    # 测试持仓检查
    test4_result = test_position_check()
    
    # 测试交易时间检查
    test5_result = test_trade_time_check()
    
    print(f"\n" + "=" * 60)
    print("📋 诊断结果汇总:")
    print(f"日志分析: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"破板卖出逻辑: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"买入信号检查: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"持仓检查: {'✅ 通过' if test4_result else '❌ 失败'}")
    print(f"交易时间检查: {'✅ 通过' if test5_result else '❌ 失败'}")
    
    print(f"\n🔍 诊断建议:")
    if not test3_result:
        print("  💡 可能原因：买入信号检查阻止了破板卖出")
        print("  🔧 建议：检查OBV因子和防追高设置")
    elif not test4_result:
        print("  💡 可能原因：股票不在持仓中")
        print("  🔧 建议：检查持仓数据和股票代码格式")
    elif not test5_result:
        print("  💡 可能原因：不在交易时间内")
        print("  🔧 建议：检查交易时间设置")
    else:
        print("  💡 可能原因：其他技术问题")
        print("  🔧 建议：检查日志和错误信息")
    
    print(f"\n🎯 下一步行动:")
    print("  1. 检查买入信号检查逻辑")
    print("  2. 验证持仓数据是否正确")
    print("  3. 确认交易时间设置")
    print("  4. 查看详细错误日志")

if __name__ == "__main__":
    main() 
# 股票数据采集与智能选股系统

## 🎯 系统简介
基于热门股票数据的多维度智能选股分析系统，提供数据采集、选股分析、报告生成、Web可视化等完整功能。

## 🚀 核心功能
- 📊 **数据采集**: 自动采集人气榜和飙升榜股票数据
- 🎯 **智能选股**: 6种选股策略多维度分析
- 📋 **报告生成**: 美观的HTML分析报告
- 🏭 **行业分析**: 27个行业智能分类
- 🖥️ **GUI界面**: 友好的可视化操作界面
- 🌐 **Web可视化**: 实时Web界面展示

## 📁 核心文件

### 🔧 主要功能模块
- `可视化主程序.py` - GUI主界面
- `后台采集.py` - 股票数据采集
- `智能选股系统.py` - 多策略选股分析
- `选股报告生成器.py` - HTML报告生成
- `行业板块分类.py` - 行业智能分类
- `增强数据显示.py` - 数据整合显示
- `一键选股.py` - 完整选股流程

### 🌐 Web功能模块
- `数据服务器.py` - Web可视化数据服务
- `智能数据分析报告.py` - 智能分析报告生成
- `可视化界面_实时版.html` - Web实时可视化界面

## 🎯 使用方法

### 方法一：GUI界面（推荐）
1. 运行 `python 可视化主程序.py` 启动GUI界面
2. 点击 "🔄 开始数据采集" 获取最新数据
3. 点击 "🎯 智能选股分析" 进行选股
4. 点击 "🏆 查看选股结果" 查看结果
5. 点击 "🌐 启动Web可视化" 启动Web界面

### 方法二：一键选股
1. 运行 `python 一键选股.py` 执行完整流程
2. 自动完成数据采集、选股分析、报告生成

### 方法三：Web可视化
1. 运行 `python 数据服务器.py` 启动数据服务器
2. 打开浏览器访问 `http://localhost:8080/可视化界面_实时版.html`
3. 实时查看股票数据和选股结果

## 📊 选股策略
- 🚀 **动量选股**: 选择强势上涨股票
- 💎 **价值选股**: 选择相对低估股票
- 🌱 **成长选股**: 选择高成长潜力股票
- ⚖️ **均衡选股**: 综合考虑多个因素
- 🔥 **热门板块**: 选择表现最佳行业股票
- 🛡️ **风险控制**: 选择相对安全股票

## 🏭 行业分类
系统支持27个行业智能分类：
- 金融类：银行、保险、证券
- 科技类：电子、计算机、通信、人工智能
- 消费类：食品饮料、农林牧渔、纺织服装
- 工业类：机械设备、电气设备、汽车
- 医药类：医药生物、医疗器械、医疗服务
- 能源类：石油石化、煤炭、电力、新能源
- 材料类：钢铁、有色金属、化工、建材
- 其他：房地产、交通运输、传媒等

## 📋 生成报告
- `智能选股分析报告.html` - 选股分析报告
- `股票行业整合报告.html` - 行业分析报告
- `股票分析报告.html` - 基础数据分析报告
- `选股结果.json` - 详细选股数据
- `推荐股票汇总.csv` - 推荐股票表格

## 🌐 Web API接口
数据服务器提供以下REST API：
- `GET /api/stocks` - 获取股票数据
- `GET /api/selection` - 获取选股结果
- `GET /api/status` - 获取系统状态

## 🔧 技术特点
- **自动化**: 全自动数据采集和处理
- **智能化**: 多策略智能选股算法
- **实时性**: 实时数据更新和Web展示
- **专业性**: 专业级的数据分析和报告
- **易用性**: 简单直观的操作界面
- **跨平台**: 支持Windows、macOS、Linux

## 📦 依赖环境
- Python 3.7+
- selenium (浏览器自动化)
- requests (HTTP请求)
- tkinter (GUI界面)
- csv, json (数据处理)

## ⚠️ 风险提示
股市有风险，投资需谨慎。本系统仅供参考，不构成投资建议。请根据自身情况做出投资决策，注意风险控制。

## 📞 技术支持
如有问题或建议，请通过以下方式联系：
- 查看系统日志获取详细错误信息
- 运行系统状态检查功能
- 确保网络连接正常

---
**版本**: v3.0 Final with Web Support  
**更新时间**: 2025-07-30  
**状态**: ✅ 完整功能版本

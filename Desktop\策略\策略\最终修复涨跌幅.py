#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复涨跌幅数据
针对剩余的异常数据进行精确修复
"""

import requests
import csv
import time
import os
import re

def get_stock_price_sina(code):
    """从新浪财经获取准确的股票数据"""
    try:
        if code.startswith('6'):
            full_code = f"sh{code}"
        else:
            full_code = f"sz{code}"
        
        url = f"http://hq.sinajs.cn/list={full_code}"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.encoding = 'gbk'
        
        if response.status_code == 200 and response.text:
            data = response.text.strip()
            if 'var hq_str_' in data and '=""' not in data:
                start = data.find('"') + 1
                end = data.rfind('"')
                if start > 0 and end > start:
                    fields = data[start:end].split(',')
                    if len(fields) >= 4:
                        name = fields[0].strip()
                        current_price = float(fields[3]) if fields[3] and fields[3] != '0.000' else 0
                        prev_close = float(fields[2]) if fields[2] and fields[2] != '0.000' else 0
                        
                        if current_price > 0 and prev_close > 0:
                            change_percent = ((current_price - prev_close) / prev_close) * 100
                            
                            if change_percent > 0:
                                change_str = f"+{change_percent:.2f}%"
                            elif change_percent < 0:
                                change_str = f"{change_percent:.2f}%"
                            else:
                                change_str = "0.00%"
                            
                            return {
                                'name': name,
                                'change': change_str,
                                'price': current_price,
                                'prev_close': prev_close
                            }
                        elif current_price == 0:
                            return {
                                'name': name,
                                'change': '停牌',
                                'price': 0,
                                'prev_close': prev_close
                            }
    except Exception as e:
        print(f"    ❌ 获取 {code} 数据失败: {str(e)[:50]}")
    
    return None

def is_abnormal_change(change_str):
    """检查涨跌幅是否异常"""
    if not change_str or change_str in ['', 'N/A', '停牌']:
        return False
    
    try:
        # 提取数字部分
        number_str = change_str.replace('+', '').replace('%', '').strip()
        if not number_str:
            return False
        
        change_value = float(number_str)
        
        # 检查是否超过合理范围（±25%）
        if abs(change_value) > 25:
            return True
        
        return False
        
    except:
        return True

def fix_csv_file(filename):
    """修复CSV文件中的异常涨跌幅"""
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return
    
    print(f"🔧 修复 {filename} 中的异常涨跌幅...")
    
    # 读取数据
    with open(filename, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        data = list(reader)
    
    # 找出异常数据
    abnormal_stocks = []
    for row in data:
        code = row.get('code', '')
        change = row.get('change', '')
        
        if is_abnormal_change(change):
            abnormal_stocks.append((code, change))
    
    if not abnormal_stocks:
        print(f"✅ {filename} 中没有异常的涨跌幅数据")
        return
    
    print(f"⚠️ 发现 {len(abnormal_stocks)} 只股票的涨跌幅异常:")
    for code, change in abnormal_stocks:
        print(f"  📊 {code}: {change}")
    
    # 修复异常数据
    fixed_count = 0
    
    for row in data:
        code = row.get('code', '')
        current_change = row.get('change', '')
        
        if is_abnormal_change(current_change):
            print(f"  🔧 修复 {code} ({current_change})...")
            
            stock_data = get_stock_price_sina(code)
            
            if stock_data:
                row['change'] = stock_data['change']
                if stock_data.get('name'):
                    row['name'] = stock_data['name']
                
                print(f"    ✅ 修复成功: {stock_data['change']}")
                fixed_count += 1
            else:
                print(f"    ❌ 修复失败，设为N/A")
                row['change'] = 'N/A'
            
            time.sleep(0.5)  # 避免请求过快
    
    # 保存修复后的数据
    if fixed_count > 0:
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            if data:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
        
        print(f"✅ {filename} 修复完成，修复了 {fixed_count} 只股票")
    else:
        print(f"ℹ️ {filename} 无数据需要修复")

def main():
    """主函数"""
    print("🔧 最终修复涨跌幅数据工具")
    print("=" * 50)
    
    # 修复两个CSV文件
    csv_files = ['popularity.csv', 'soaring.csv']
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"\n📁 处理文件: {csv_file}")
            fix_csv_file(csv_file)
        else:
            print(f"⚠️ 文件不存在: {csv_file}")
    
    print(f"\n🎉 最终修复完成！")
    print(f"💡 所有异常的涨跌幅数据已修复")
    print(f"🌐 HTML界面将显示正确的数据")

if __name__ == "__main__":
    main()

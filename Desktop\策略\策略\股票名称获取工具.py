#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票名称获取工具
从多个数据源在线获取股票名称，并缓存到本地
"""

import requests
import json
import csv
import time
import os
from datetime import datetime, timedelta

class StockNameFetcher:
    def __init__(self):
        self.cache_file = "stock_names_cache.json"
        self.cache_data = self.load_cache()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def load_cache(self):
        """加载缓存数据"""
        if os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache = json.load(f)
                    # 检查缓存是否过期（7天）
                    cache_date = datetime.fromisoformat(cache.get('update_time', '2000-01-01'))
                    if datetime.now() - cache_date < timedelta(days=7):
                        print(f"✅ 加载股票名称缓存，共 {len(cache.get('names', {}))} 只股票")
                        return cache
            except Exception as e:
                print(f"⚠️ 缓存文件损坏: {e}")
        
        return {'names': {}, 'update_time': '2000-01-01'}
    
    def save_cache(self):
        """保存缓存数据"""
        self.cache_data['update_time'] = datetime.now().isoformat()
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_data, f, ensure_ascii=False, indent=2)
            print(f"💾 已保存股票名称缓存，共 {len(self.cache_data['names'])} 只股票")
        except Exception as e:
            print(f"❌ 保存缓存失败: {e}")
    
    def get_name_from_sina(self, code):
        """从新浪财经获取股票名称"""
        try:
            # 根据股票代码确定市场
            if code.startswith('6'):
                full_code = f"sh{code}"
            else:
                full_code = f"sz{code}"
            
            url = f"http://hq.sinajs.cn/list={full_code}"
            response = self.session.get(url, timeout=5)
            response.encoding = 'gbk'
            
            if response.status_code == 200 and response.text:
                # 解析返回数据
                data = response.text.strip()
                if 'var hq_str_' in data and '=""' not in data:
                    # 提取股票名称
                    start = data.find('"') + 1
                    end = data.find(',', start)
                    if start > 0 and end > start:
                        name = data[start:end].strip()
                        if name and len(name) > 1:
                            return name
            
        except Exception as e:
            pass
        
        return None
    
    def get_name_from_eastmoney(self, code):
        """从东方财富获取股票名称"""
        try:
            # 根据股票代码确定市场
            if code.startswith('6'):
                market_code = f"1.{code}"
            else:
                market_code = f"0.{code}"
            
            url = f"http://push2.eastmoney.com/api/qt/stock/get"
            params = {
                'secid': market_code,
                'fields': 'f58'  # f58是股票名称字段
            }
            
            response = self.session.get(url, params=params, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('rc') == 0 and data.get('data'):
                    name = data['data'].get('f58')
                    if name and len(name) > 1:
                        return name
            
        except Exception as e:
            pass
        
        return None
    
    def get_name_from_tencent(self, code):
        """从腾讯财经获取股票名称"""
        try:
            # 根据股票代码确定市场
            if code.startswith('6'):
                full_code = f"sh{code}"
            else:
                full_code = f"sz{code}"
            
            url = f"http://qt.gtimg.cn/q={full_code}"
            response = self.session.get(url, timeout=5)
            response.encoding = 'gbk'
            
            if response.status_code == 200 and response.text:
                # 解析返回数据
                data = response.text.strip()
                if '~' in data:
                    parts = data.split('~')
                    if len(parts) > 1:
                        name = parts[1].strip()
                        if name and len(name) > 1:
                            return name
            
        except Exception as e:
            pass
        
        return None
    
    def get_stock_name(self, code):
        """获取股票名称（优先使用缓存）"""
        # 先检查缓存
        if code in self.cache_data['names']:
            return self.cache_data['names'][code]
        
        # 尝试多个数据源
        sources = [
            ('新浪财经', self.get_name_from_sina),
            ('东方财富', self.get_name_from_eastmoney),
            ('腾讯财经', self.get_name_from_tencent)
        ]
        
        for source_name, get_func in sources:
            try:
                name = get_func(code)
                if name:
                    print(f"  📈 {code} {name} (来源: {source_name})")
                    # 保存到缓存
                    self.cache_data['names'][code] = name
                    return name
            except Exception as e:
                continue
            
            # 避免请求过于频繁
            time.sleep(0.1)
        
        # 如果都失败了，生成默认名称
        if code.startswith('6'):
            default_name = f"沪市{code}"
        elif code.startswith('00'):
            default_name = f"深市{code}"
        elif code.startswith('30'):
            default_name = f"创业板{code}"
        else:
            default_name = f"股票{code}"
        
        self.cache_data['names'][code] = default_name
        return default_name
    
    def batch_update_names(self, codes, max_requests=100):
        """批量更新股票名称"""
        print(f"🔄 开始批量获取 {len(codes)} 只股票的名称...")
        print(f"⚠️ 为避免被限制，最多处理 {max_requests} 只股票")
        
        updated_count = 0
        cached_count = 0
        
        for i, code in enumerate(codes[:max_requests]):
            if code in self.cache_data['names']:
                cached_count += 1
                if i % 20 == 0:  # 每20个显示一次进度
                    print(f"  📋 进度: {i+1}/{min(len(codes), max_requests)} (缓存命中: {cached_count})")
                continue
            
            print(f"  🔍 获取 {code} 的名称...")
            name = self.get_stock_name(code)
            if name:
                updated_count += 1
            
            # 每10个请求保存一次缓存
            if (i + 1) % 10 == 0:
                self.save_cache()
                print(f"  📋 进度: {i+1}/{min(len(codes), max_requests)} (新获取: {updated_count}, 缓存: {cached_count})")
                time.sleep(1)  # 休息1秒
        
        # 最终保存
        self.save_cache()
        
        print(f"✅ 批量更新完成!")
        print(f"  📊 总处理: {min(len(codes), max_requests)} 只股票")
        print(f"  🆕 新获取: {updated_count} 只")
        print(f"  💾 缓存命中: {cached_count} 只")

def update_csv_with_names(csv_file, fetcher):
    """更新CSV文件中的股票名称"""
    if not os.path.exists(csv_file):
        print(f"❌ 文件不存在: {csv_file}")
        return
    
    print(f"📝 更新 {csv_file} 中的股票名称...")
    
    # 读取CSV文件
    data = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        data = list(reader)
    
    # 更新名称
    updated_count = 0
    for row in data:
        code = row.get('code', '')
        current_name = row.get('name', '')
        
        # 如果名称为空或者是默认格式，则更新
        if (not current_name or 
            current_name.startswith('沪市') or 
            current_name.startswith('深市') or 
            current_name.startswith('创业板') or
            current_name.startswith('股票')):
            
            new_name = fetcher.get_stock_name(code)
            if new_name != current_name:
                row['name'] = new_name
                updated_count += 1
    
    # 保存更新后的文件
    if updated_count > 0:
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            if data:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
        
        print(f"✅ {csv_file} 更新完成，更新了 {updated_count} 个股票名称")
    else:
        print(f"ℹ️ {csv_file} 无需更新")

def main():
    """主函数"""
    print("🚀 股票名称获取工具")
    print("=" * 50)
    
    fetcher = StockNameFetcher()
    
    # 读取需要更新的股票代码
    codes_to_update = []
    
    # 从CSV文件中收集股票代码
    csv_files = ['popularity.csv', 'soaring.csv']
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    code = row.get('code', '')
                    if code and code not in codes_to_update:
                        codes_to_update.append(code)
    
    if codes_to_update:
        print(f"📋 找到 {len(codes_to_update)} 只需要更新名称的股票")
        
        # 批量获取名称
        fetcher.batch_update_names(codes_to_update)
        
        # 更新CSV文件
        for csv_file in csv_files:
            update_csv_with_names(csv_file, fetcher)
        
        print(f"\n🎉 所有任务完成！")
        print(f"💡 股票名称已缓存到 {fetcher.cache_file}")
        print(f"📊 下次运行时会优先使用缓存数据")
        
    else:
        print("ℹ️ 没有找到需要更新的股票代码")

if __name__ == "__main__":
    main()

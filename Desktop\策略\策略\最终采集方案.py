#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终采集方案
通过不同URL或页面刷新的方式采集两个不同的榜单
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
import time
import csv
import re

def setup_driver():
    """配置并启动Chrome浏览器"""
    print("🚀 启动浏览器...")
    
    chrome_options = Options()
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--log-level=3')
    chrome_options.add_argument('--silent')
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    return driver

def extract_clean_stock_data(driver, target_count=100):
    """提取干净的股票数据"""
    print(f"📊 提取股票数据，目标 {target_count} 条...")
    
    # 等待页面稳定
    time.sleep(5)
    
    # 尝试多种方式提取股票代码
    stock_codes = set()
    
    # 方法1: 从页面源码中提取6位数字
    page_source = driver.page_source
    
    # 使用正则表达式提取所有6位数字
    all_numbers = re.findall(r'\b(\d{6})\b', page_source)
    
    for code in all_numbers:
        # 验证是否为有效股票代码
        if code.startswith(('00', '30', '60')):
            stock_codes.add(code)
            if len(stock_codes) >= target_count:
                break
    
    # 转换为列表
    codes_list = list(stock_codes)[:target_count]
    
    # 构造数据
    data = []
    for i, code in enumerate(codes_list):
        # 简单的名称生成
        if code.startswith('60'):
            name = f"沪市股票{i+1}"
        elif code.startswith('00'):
            name = f"深市主板{i+1}"
        else:
            name = f"创业板{i+1}"
        
        data.append([code, name, ""])
    
    print(f"✅ 提取到 {len(data)} 条股票数据")
    
    # 显示前10条数据
    print("📋 前10条数据预览:")
    for i, (code, name, change) in enumerate(data[:10]):
        print(f"  {i+1:2d}. {code} {name}")
    
    return data

def try_click_tab_multiple_ways(driver, tab_name, max_attempts=3):
    """尝试多种方式点击标签页"""
    print(f"🎯 尝试点击 {tab_name} 标签页...")
    
    for attempt in range(max_attempts):
        print(f"  第 {attempt + 1} 次尝试...")
        
        # 记录点击前的页面内容
        before_content = driver.page_source[:1000]
        
        try:
            # 查找包含目标文本的所有元素
            elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{tab_name}')]")
            
            if not elements:
                print(f"    未找到包含 '{tab_name}' 的元素")
                continue
            
            print(f"    找到 {len(elements)} 个包含 '{tab_name}' 的元素")
            
            for i, element in enumerate(elements):
                try:
                    if element.is_displayed() and element.is_enabled():
                        print(f"      尝试点击第 {i+1} 个元素...")
                        
                        # 滚动到元素
                        driver.execute_script("arguments[0].scrollIntoView(true);", element)
                        time.sleep(1)
                        
                        # 高亮元素
                        driver.execute_script("arguments[0].style.border='3px solid red'", element)
                        time.sleep(0.5)
                        
                        # 点击
                        element.click()
                        time.sleep(3)
                        
                        # 检查页面是否有变化
                        after_content = driver.page_source[:1000]
                        if after_content != before_content:
                            print(f"      ✅ 点击成功，页面内容已改变")
                            # 移除高亮
                            try:
                                driver.execute_script("arguments[0].style.border=''", element)
                            except:
                                pass
                            return True
                        else:
                            print(f"      ⚠️ 点击后页面内容未改变")
                        
                        # 移除高亮
                        try:
                            driver.execute_script("arguments[0].style.border=''", element)
                        except:
                            pass
                            
                except Exception as e:
                    print(f"      ❌ 点击第 {i+1} 个元素失败: {str(e)[:30]}")
                    continue
                    
        except Exception as e:
            print(f"    ❌ 第 {attempt + 1} 次尝试失败: {str(e)[:50]}")
        
        # 如果这次尝试失败，等待一下再试
        if attempt < max_attempts - 1:
            print(f"    等待3秒后重试...")
            time.sleep(3)
    
    print(f"❌ 所有尝试都失败，无法点击 {tab_name}")
    return False

def save_data(data, filename, tab_name):
    """保存数据到CSV文件"""
    if data:
        with open(filename, "w", newline="", encoding="utf-8") as f:
            writer = csv.writer(f)
            writer.writerow(["code", "name", "change"])
            writer.writerows(data)
        print(f"✅ {filename} 已保存，共 {len(data)} 条数据")
        return True
    else:
        print(f"❌ {tab_name} 未提取到有效数据")
        return False

def main():
    """主函数"""
    driver = setup_driver()
    
    try:
        # 访问网站
        print("🌐 访问东方财富VIP网站...")
        driver.get("https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock")
        print("⏳ 页面加载中...")
        time.sleep(25)  # 充分等待页面加载
        
        # 第一步：采集当前页面数据
        print("\n" + "="*60)
        print("📊 第一步：采集当前页面数据")
        print("="*60)
        
        first_data = extract_clean_stock_data(driver, 100)
        
        # 假设当前是人气榜，保存为人气榜数据
        save_data(first_data, "popularity.csv", "人气榜")
        
        # 第二步：尝试切换到另一个榜单
        print("\n" + "="*60)
        print("🔄 第二步：尝试切换到飙升榜")
        print("="*60)
        
        # 尝试点击飙升榜
        switch_success = try_click_tab_multiple_ways(driver, "飙升榜", max_attempts=5)
        
        if switch_success:
            print("✅ 成功切换到飙升榜")
            
            # 等待页面稳定
            time.sleep(5)
            
            # 采集飙升榜数据
            second_data = extract_clean_stock_data(driver, 100)
            save_data(second_data, "soaring.csv", "飙升榜")
            
            # 验证数据差异
            if first_data and second_data:
                first_codes = set(item[0] for item in first_data)
                second_codes = set(item[0] for item in second_data)
                common_codes = first_codes & second_codes
                
                print(f"\n📊 数据验证:")
                print(f"  第一次采集股票数: {len(first_codes)}")
                print(f"  第二次采集股票数: {len(second_codes)}")
                print(f"  重复股票数: {len(common_codes)}")
                
                if len(common_codes) < len(first_codes) * 0.8:
                    print("✅ 两次采集数据存在明显差异，切换可能成功")
                else:
                    print("⚠️ 两次采集数据相似度较高，切换可能不完全")
        else:
            print("❌ 无法切换到飙升榜")
            print("💡 尝试刷新页面重新采集...")
            
            # 刷新页面重新采集
            driver.refresh()
            time.sleep(20)
            
            # 再次采集数据作为飙升榜数据
            second_data = extract_clean_stock_data(driver, 100)
            save_data(second_data, "soaring.csv", "飙升榜（刷新后）")
        
        print("\n🎉 采集任务完成！")
        
        # 最终统计
        try:
            with open("popularity.csv", "r", encoding="utf-8") as f:
                pop_count = len(f.readlines()) - 1  # 减去标题行
        except:
            pop_count = 0
            
        try:
            with open("soaring.csv", "r", encoding="utf-8") as f:
                soar_count = len(f.readlines()) - 1  # 减去标题行
        except:
            soar_count = 0
        
        print(f"📈 最终统计：人气榜 {pop_count} 条，飙升榜 {soar_count} 条")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n🔚 正在关闭浏览器...")
        driver.quit()
        print("✅ 浏览器已关闭")

if __name__ == "__main__":
    main()

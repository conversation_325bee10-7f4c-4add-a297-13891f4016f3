#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化主程序 - 优化版
去掉确认对话框，直接执行功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import sys
import os
import threading
import webbrowser
import csv
import json
from datetime import datetime
import time

class StockVisualizationSystem:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.create_widgets()
        self.load_initial_data()
        self.start_auto_refresh()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("🚀 股票数据采集系统 - 可视化主程序")
        self.root.geometry("1400x900")
        self.root.resizable(True, True)
        self.root.configure(bg="#f0f0f0")
        
        # 窗口居中
        self.center_window()
        
        # 设置样式
        self.setup_styles()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 1400
        height = 900
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_styles(self):
        """设置样式"""
        self.style = ttk.Style()
        
        # 配置样式
        self.style.configure("Title.TLabel",
                           font=("Microsoft YaHei", 18, "bold"),
                           foreground="#2c3e50",
                           background="#f0f0f0")
        
        self.style.configure("Header.TLabel",
                           font=("Microsoft YaHei", 12, "bold"),
                           foreground="#34495e",
                           background="#ecf0f1")
        
        self.style.configure("Action.TButton",
                           font=("Microsoft YaHei", 10, "bold"),
                           padding=(15, 8))
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = tk.Frame(self.root, bg="#f0f0f0")
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 顶部标题区域
        self.create_header(main_container)
        
        # 主要内容区域
        content_frame = tk.Frame(main_container, bg="#f0f0f0")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))
        
        # 左侧控制面板
        self.create_control_panel(content_frame)
        
        # 右侧数据显示区域
        self.create_data_display(content_frame)
        
        # 底部状态栏
        self.create_status_bar(main_container)
    
    def create_header(self, parent):
        """创建顶部标题区域"""
        header_frame = tk.Frame(parent, bg="#2c3e50", height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # 主标题
        title_label = tk.Label(header_frame,
                              text="🚀 股票数据采集系统 - 可视化主程序",
                              font=("Microsoft YaHei", 20, "bold"),
                              bg="#2c3e50",
                              fg="white")
        title_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # 系统信息
        info_frame = tk.Frame(header_frame, bg="#2c3e50")
        info_frame.pack(side=tk.RIGHT, padx=20, pady=20)
        
        self.time_label = tk.Label(info_frame,
                                  text=f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                                  font=("Microsoft YaHei", 10),
                                  bg="#2c3e50",
                                  fg="#bdc3c7")
        self.time_label.pack()
        
        self.status_label = tk.Label(info_frame,
                                    text="● 系统运行中",
                                    font=("Microsoft YaHei", 10, "bold"),
                                    bg="#2c3e50",
                                    fg="#27ae60")
        self.status_label.pack()
    
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_frame = tk.Frame(parent, bg="#ecf0f1", width=350)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 15))
        control_frame.pack_propagate(False)
        
        # 控制面板标题
        tk.Label(control_frame,
                text="🎮 功能控制面板",
                font=("Microsoft YaHei", 14, "bold"),
                bg="#ecf0f1",
                fg="#2c3e50").pack(pady=(20, 25))
        
        # 数据采集区域
        self.create_collection_section(control_frame)
        
        # 数据处理区域
        self.create_processing_section(control_frame)
        
        # 系统管理区域
        self.create_management_section(control_frame)
        
        # 系统状态显示
        self.create_system_status(control_frame)
    
    def create_collection_section(self, parent):
        """创建数据采集区域"""
        section_frame = tk.LabelFrame(parent,
                                     text="📊 数据采集",
                                     font=("Microsoft YaHei", 11, "bold"),
                                     bg="#ecf0f1",
                                     fg="#2c3e50",
                                     padx=10,
                                     pady=10)
        section_frame.pack(fill=tk.X, padx=20, pady=10)
        
        buttons = [
            ("🚀 后台数据采集", self.run_background_collection, "#3498db"),
            ("📊 获取股票名称", self.get_stock_names, "#9b59b6"),
            ("📈 获取涨跌幅数据", self.get_price_changes, "#e67e22")
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(section_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=10,
                           pady=6,
                           cursor="hand2",
                           width=25)
            btn.pack(pady=5, fill=tk.X)
    
    def create_processing_section(self, parent):
        """创建数据处理区域"""
        section_frame = tk.LabelFrame(parent,
                                     text="📄 数据处理",
                                     font=("Microsoft YaHei", 11, "bold"),
                                     bg="#ecf0f1",
                                     fg="#2c3e50",
                                     padx=10,
                                     pady=10)
        section_frame.pack(fill=tk.X, padx=20, pady=10)
        
        buttons = [
            ("📄 生成分析报告", self.generate_report, "#27ae60"),
            ("🌐 启动Web可视化", self.start_web_visualization, "#3498db")
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(section_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=10,
                           pady=6,
                           cursor="hand2",
                           width=25)
            btn.pack(pady=5, fill=tk.X)
    
    def create_management_section(self, parent):
        """创建系统管理区域"""
        section_frame = tk.LabelFrame(parent,
                                     text="⚙️ 系统管理",
                                     font=("Microsoft YaHei", 11, "bold"),
                                     bg="#ecf0f1",
                                     fg="#2c3e50",
                                     padx=10,
                                     pady=10)
        section_frame.pack(fill=tk.X, padx=20, pady=10)
        
        buttons = [
            ("🔄 刷新数据显示", self.refresh_data, "#34495e"),
            ("⚙️ 系统状态检查", self.check_system_status, "#95a5a6")
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(section_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=10,
                           pady=6,
                           cursor="hand2",
                           width=25)
            btn.pack(pady=5, fill=tk.X)
    
    def create_system_status(self, parent):
        """创建系统状态显示"""
        status_frame = tk.LabelFrame(parent,
                                    text="📊 系统状态",
                                    font=("Microsoft YaHei", 11, "bold"),
                                    bg="#ecf0f1",
                                    fg="#2c3e50",
                                    padx=10,
                                    pady=10)
        status_frame.pack(fill=tk.X, padx=20, pady=(10, 20))
        
        # 状态信息
        status_items = [
            ("Python版本:", "检查中..."),
            ("依赖包状态:", "检查中..."),
            ("核心文件:", "检查中..."),
            ("数据文件:", "检查中...")
        ]
        
        self.status_labels = {}
        for label_text, initial_value in status_items:
            frame = tk.Frame(status_frame, bg="#ecf0f1")
            frame.pack(fill=tk.X, pady=2)
            
            tk.Label(frame,
                    text=label_text,
                    font=("Microsoft YaHei", 9),
                    bg="#ecf0f1",
                    fg="#2c3e50").pack(side=tk.LEFT)
            
            value_label = tk.Label(frame,
                                  text=initial_value,
                                  font=("Microsoft YaHei", 9),
                                  bg="#ecf0f1",
                                  fg="#27ae60")
            value_label.pack(side=tk.RIGHT)
            
            self.status_labels[label_text] = value_label
    
    def create_data_display(self, parent):
        """创建右侧数据显示区域"""
        display_frame = tk.Frame(parent, bg="#ffffff")
        display_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 数据显示标题
        title_frame = tk.Frame(display_frame, bg="#34495e", height=40)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        tk.Label(title_frame,
                text="📊 实时股票数据显示",
                font=("Microsoft YaHei", 14, "bold"),
                bg="#34495e",
                fg="white").pack(side=tk.LEFT, padx=20, pady=10)
        
        # 清空按钮
        tk.Button(title_frame,
                 text="🗑️ 清空",
                 command=self.clear_display,
                 font=("Microsoft YaHei", 9),
                 bg="#e74c3c",
                 fg="white",
                 relief="raised",
                 bd=1,
                 padx=10,
                 pady=2,
                 cursor="hand2").pack(side=tk.RIGHT, padx=20, pady=8)
        
        # 标签页控件
        self.notebook = ttk.Notebook(display_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 创建标签页
        self.create_display_tabs()
    
    def create_display_tabs(self):
        """创建数据显示标签页"""
        # 双列数据显示标签页
        self.dual_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.dual_frame, text="📊 双列数据显示")
        
        # 创建双列布局 - 使用Grid确保等宽
        dual_container = tk.Frame(self.dual_frame, bg="white")
        dual_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 配置Grid权重，确保两列等宽
        dual_container.grid_columnconfigure(0, weight=1)
        dual_container.grid_columnconfigure(1, weight=1)
        dual_container.grid_rowconfigure(0, weight=1)
        
        # 人气榜列 - 左侧
        popularity_frame = tk.LabelFrame(dual_container,
                                        text="🔥 人气榜完整名单",
                                        font=("Microsoft YaHei", 12, "bold"),
                                        fg="#e74c3c")
        popularity_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        
        self.popularity_text = scrolledtext.ScrolledText(popularity_frame,
                                                        font=("Microsoft YaHei", 10),
                                                        wrap=tk.WORD,
                                                        bg="#f8f9fa",
                                                        fg="#2c3e50")
        self.popularity_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 飙升榜列 - 右侧
        soaring_frame = tk.LabelFrame(dual_container,
                                     text="🚀 飙升榜完整名单",
                                     font=("Microsoft YaHei", 12, "bold"),
                                     fg="#3498db")
        soaring_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        
        self.soaring_text = scrolledtext.ScrolledText(soaring_frame,
                                                     font=("Microsoft YaHei", 10),
                                                     wrap=tk.WORD,
                                                     bg="#f8f9fa",
                                                     fg="#2c3e50")
        self.soaring_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 操作日志标签页
        self.log_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.log_frame, text="📝 操作日志")
        
        self.log_text = scrolledtext.ScrolledText(self.log_frame,
                                                 font=("Consolas", 10),
                                                 wrap=tk.WORD,
                                                 bg="#f8f9fa",
                                                 fg="#2c3e50")
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 数据统计标签页
        self.stats_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.stats_frame, text="📈 数据统计")
        
        self.stats_text = scrolledtext.ScrolledText(self.stats_frame,
                                                   font=("Microsoft YaHei", 10),
                                                   wrap=tk.WORD,
                                                   bg="#f8f9fa",
                                                   fg="#2c3e50")
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_status_bar(self, parent):
        """创建底部状态栏"""
        status_frame = tk.Frame(parent, bg="#bdc3c7", height=35)
        status_frame.pack(fill=tk.X, pady=(15, 0))
        status_frame.pack_propagate(False)
        
        # 状态指示器
        self.main_status_label = tk.Label(status_frame,
                                         text="● 系统就绪",
                                         font=("Microsoft YaHei", 10, "bold"),
                                         bg="#bdc3c7",
                                         fg="#27ae60")
        self.main_status_label.pack(side=tk.LEFT, padx=20, pady=8)
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate', length=200)
        self.progress.pack(side=tk.LEFT, padx=20, pady=8)
        
        # 自动刷新状态
        self.auto_refresh_label = tk.Label(status_frame,
                                          text="🔄 自动刷新: 开启",
                                          font=("Microsoft YaHei", 9),
                                          bg="#bdc3c7",
                                          fg="#2c3e50")
        self.auto_refresh_label.pack(side=tk.LEFT, padx=20, pady=8)
        
        # 退出按钮
        tk.Button(status_frame,
                 text="🚪 退出系统",
                 command=self.exit_application,
                 font=("Microsoft YaHei", 9),
                 bg="#e74c3c",
                 fg="white",
                 relief="raised",
                 bd=1,
                 padx=15,
                 pady=3,
                 cursor="hand2").pack(side=tk.RIGHT, padx=20, pady=5)
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 根据级别设置颜色
        if level == "ERROR":
            self.log_text.tag_add("error", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("error", foreground="#e74c3c")
        elif level == "SUCCESS":
            self.log_text.tag_add("success", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("success", foreground="#27ae60")
        elif level == "WARNING":
            self.log_text.tag_add("warning", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("warning", foreground="#f39c12")
    
    def show_data(self, title, data):
        """在统计标签页显示数据"""
        self.stats_text.insert(tk.END, f"\n{'='*60}\n")
        self.stats_text.insert(tk.END, f"📊 {title} - {datetime.now().strftime('%H:%M:%S')}\n")
        self.stats_text.insert(tk.END, f"{'='*60}\n")
        self.stats_text.insert(tk.END, f"{data}\n")
        self.stats_text.see(tk.END)
        
        # 切换到统计标签页
        self.notebook.select(self.stats_frame)
    
    def clear_display(self):
        """清空所有显示区域"""
        self.log_text.delete(1.0, tk.END)
        self.popularity_text.delete(1.0, tk.END)
        self.soaring_text.delete(1.0, tk.END)
        self.stats_text.delete(1.0, tk.END)
        self.log_message("所有显示区域已清空", "INFO")
    
    def set_status(self, message, color="#27ae60"):
        """设置主状态"""
        self.main_status_label.config(text=f"● {message}", fg=color)
        self.status_label.config(text=f"● {message}", fg=color)
        self.root.update()
    
    def start_progress(self):
        """开始进度条"""
        self.progress.start(10)
    
    def stop_progress(self):
        """停止进度条"""
        self.progress.stop()
    
    def load_initial_data(self):
        """加载初始数据"""
        self.log_message("🚀 可视化主程序启动完成", "SUCCESS")
        self.log_message("系统初始化完成，开始加载数据", "INFO")
        
        # 检查系统状态
        self.check_system_status_silent()
        
        # 加载现有数据
        self.load_existing_data()
    
    def check_system_status_silent(self):
        """静默检查系统状态"""
        try:
            # Python版本
            python_version = f"{sys.version.split()[0]}"
            self.status_labels["Python版本:"].config(text=python_version, fg="#27ae60")
            
            # 检查依赖包
            required_modules = ["selenium", "requests", "matplotlib", "pandas", "numpy"]
            missing_modules = []
            
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError:
                    missing_modules.append(module)
            
            if missing_modules:
                self.status_labels["依赖包状态:"].config(text=f"缺少 {len(missing_modules)} 个", fg="#e74c3c")
            else:
                self.status_labels["依赖包状态:"].config(text="全部已安装", fg="#27ae60")
            
            # 检查核心文件
            core_files = [
                "后台采集.py", "股票名称获取工具.py", "股票涨跌幅获取工具.py",
                "智能数据分析报告.py", "数据服务器.py"
            ]
            
            missing_files = [f for f in core_files if not os.path.exists(f)]
            
            if missing_files:
                self.status_labels["核心文件:"].config(text=f"缺少 {len(missing_files)} 个", fg="#e74c3c")
            else:
                self.status_labels["核心文件:"].config(text="全部存在", fg="#27ae60")
            
            # 检查数据文件
            data_files = ["popularity.csv", "soaring.csv", "codes.txt"]
            existing_data_files = [f for f in data_files if os.path.exists(f)]
            
            if len(existing_data_files) == len(data_files):
                self.status_labels["数据文件:"].config(text="全部存在", fg="#27ae60")
            else:
                self.status_labels["数据文件:"].config(text=f"存在 {len(existing_data_files)}/{len(data_files)} 个", fg="#f39c12")
                
        except Exception as e:
            self.log_message(f"系统状态检查出错: {str(e)}", "ERROR")
    
    def load_existing_data(self):
        """加载现有数据文件"""
        try:
            # 加载人气榜数据
            self.load_csv_data("popularity.csv", "人气榜", self.popularity_text)
            
            # 加载飙升榜数据
            self.load_csv_data("soaring.csv", "飙升榜", self.soaring_text)
            
            # 生成数据统计
            self.generate_data_statistics()
            
        except Exception as e:
            self.log_message(f"加载数据时出错: {str(e)}", "ERROR")
    
    def load_csv_data(self, filename, title, text_widget):
        """加载CSV数据到指定的文本控件"""
        try:
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    data = list(reader)
                
                if data:
                    content = f"📊 {title}数据 (共 {len(data)} 只股票)\n"
                    content += f"📅 更新时间: {datetime.fromtimestamp(os.path.getmtime(filename)).strftime('%Y-%m-%d %H:%M:%S')}\n"
                    content += "=" * 60 + "\n\n"
                    
                    for i, stock in enumerate(data, 1):
                        code = stock.get('code', 'N/A')
                        name = stock.get('name', '未知')
                        change = stock.get('change', 'N/A')
                        
                        # 格式化涨跌幅显示
                        if change.startswith('+'):
                            change_display = f"📈 {change}"
                        elif change.startswith('-'):
                            change_display = f"📉 {change}"
                        else:
                            change_display = f"📊 {change}"
                        
                        content += f"{i:3d}. {code} {name:<12} {change_display}\n"
                    
                    text_widget.delete(1.0, tk.END)
                    text_widget.insert(1.0, content)
                    
                    self.log_message(f"✅ {title}数据加载完成: {len(data)} 只股票", "SUCCESS")
                else:
                    text_widget.delete(1.0, tk.END)
                    text_widget.insert(1.0, f"📭 {title}数据文件为空\n请先进行数据采集")
                    self.log_message(f"⚠️ {title}数据文件为空", "WARNING")
            else:
                text_widget.delete(1.0, tk.END)
                text_widget.insert(1.0, f"❌ {title}数据文件不存在\n文件: {filename}\n请先进行数据采集")
                self.log_message(f"❌ {title}数据文件不存在: {filename}", "ERROR")
                
        except Exception as e:
            text_widget.delete(1.0, tk.END)
            text_widget.insert(1.0, f"❌ 加载{title}数据失败\n错误: {str(e)}")
            self.log_message(f"❌ 加载{title}数据失败: {str(e)}", "ERROR")
    
    def generate_data_statistics(self):
        """生成数据统计"""
        try:
            stats_content = "📊 股票数据统计分析\n"
            stats_content += f"📅 统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            stats_content += "=" * 60 + "\n\n"
            
            # 统计各个数据文件
            data_files = [
                ("popularity.csv", "人气榜数据"),
                ("soaring.csv", "飙升榜数据"),
                ("codes.txt", "股票代码库"),
                ("stock_names_cache.json", "股票名称缓存"),
                ("股票分析报告.html", "分析报告")
            ]
            
            total_records = 0
            
            for filename, description in data_files:
                if os.path.exists(filename):
                    size = os.path.getsize(filename)
                    mod_time = datetime.fromtimestamp(os.path.getmtime(filename))
                    
                    if filename.endswith('.csv'):
                        try:
                            with open(filename, 'r', encoding='utf-8') as f:
                                reader = csv.reader(f)
                                rows = list(reader)
                                record_count = len(rows) - 1 if len(rows) > 1 else 0
                                total_records += record_count
                                stats_content += f"✅ {description}: {record_count} 条记录 ({size/1024:.1f} KB)\n"
                        except:
                            stats_content += f"⚠️ {description}: 读取失败 ({size/1024:.1f} KB)\n"
                    elif filename.endswith('.txt'):
                        try:
                            with open(filename, 'r', encoding='utf-8') as f:
                                lines = [line.strip() for line in f if line.strip()]
                                stats_content += f"✅ {description}: {len(lines)} 条记录 ({size/1024:.1f} KB)\n"
                        except:
                            stats_content += f"⚠️ {description}: 读取失败 ({size/1024:.1f} KB)\n"
                    else:
                        stats_content += f"✅ {description}: {size/1024:.1f} KB\n"
                    
                    stats_content += f"   📅 更新时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                else:
                    stats_content += f"❌ {description}: 文件不存在\n\n"
            
            stats_content += f"📊 数据总览:\n"
            stats_content += f"   总记录数: {total_records} 条\n"
            stats_content += f"   系统状态: 正常运行\n"
            stats_content += f"   Python版本: {sys.version.split()[0]}\n"
            
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_content)
            
        except Exception as e:
            self.log_message(f"生成统计数据失败: {str(e)}", "ERROR")
    
    def start_auto_refresh(self):
        """启动自动刷新"""
        def auto_refresh():
            while True:
                try:
                    time.sleep(60)  # 每60秒刷新一次
                    self.root.after(0, self.refresh_data_silent)
                except:
                    break
        
        refresh_thread = threading.Thread(target=auto_refresh, daemon=True)
        refresh_thread.start()
        
        # 更新时间显示
        def update_time():
            self.time_label.config(text=f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.root.after(1000, update_time)
        
        update_time()
    
    def refresh_data_silent(self):
        """静默刷新数据"""
        try:
            self.load_existing_data()
            self.log_message("🔄 数据自动刷新完成", "INFO")
        except Exception as e:
            self.log_message(f"自动刷新失败: {str(e)}", "ERROR")
    
    def run_script_async(self, script_name, description):
        """异步运行脚本"""
        def run():
            self.set_status("运行中...", "#f39c12")
            self.start_progress()
            self.log_message(f"🚀 开始执行: {description}", "INFO")
            
            try:
                if not os.path.exists(script_name):
                    raise FileNotFoundError(f"脚本文件不存在: {script_name}")
                
                # 运行脚本
                process = subprocess.Popen([sys.executable, script_name], cwd=os.getcwd())
                
                self.log_message(f"✅ {description} 已启动", "SUCCESS")
                
                # 等待一段时间后重新加载数据
                self.root.after(5000, self.load_existing_data)
                
            except Exception as e:
                error_msg = f"{description} 执行出错: {str(e)}"
                self.log_message(error_msg, "ERROR")
                messagebox.showerror("执行错误", error_msg)
            
            finally:
                self.stop_progress()
                self.set_status("就绪", "#27ae60")
        
        thread = threading.Thread(target=run, daemon=True)
        thread.start()
    
    # 功能按钮回调函数 - 保留确认对话框的功能
    def run_background_collection(self):
        """后台数据采集"""
        if messagebox.askyesno("确认操作", "开始后台数据采集？\n这将启动浏览器采集股票数据。"):
            self.run_script_async("后台采集.py", "后台数据采集")
    
    def get_stock_names(self):
        """获取股票名称"""
        if messagebox.askyesno("确认操作", "开始获取股票名称？\n这将从网络获取真实股票名称。"):
            self.run_script_async("股票名称获取工具.py", "股票名称获取")
    
    def get_price_changes(self):
        """获取涨跌幅数据"""
        if messagebox.askyesno("确认操作", "开始获取涨跌幅数据？\n这将从网络获取实时涨跌幅。"):
            self.run_script_async("股票涨跌幅获取工具.py", "涨跌幅数据获取")
    
    # 去掉确认对话框的功能
    def generate_report(self):
        """生成分析报告 - 直接执行"""
        self.log_message("📄 开始生成智能分析报告", "INFO")
        
        def on_complete():
            if messagebox.askyesno("报告生成完成", "智能分析报告生成完成！\n是否在浏览器中打开查看？"):
                self.open_html_report()
        
        self.run_script_async("智能数据分析报告.py", "智能分析报告生成")
        self.root.after(10000, on_complete)
    
    def start_web_visualization(self):
        """启动Web可视化 - 直接执行"""
        self.log_message("🌐 开始启动Web可视化界面", "INFO")
        
        def start_and_open():
            self.set_status("启动Web服务...", "#f39c12")
            self.start_progress()
            
            try:
                # 检查服务器是否已经运行
                import requests
                try:
                    response = requests.get("http://localhost:8080/api/stock-data", timeout=3)
                    server_running = response.status_code == 200
                    self.log_message("✅ 检测到数据服务器已在运行", "SUCCESS")
                except:
                    server_running = False
                    self.log_message("ℹ️ 数据服务器未运行，正在启动...", "INFO")
                
                # 如果服务器未运行，则启动它
                if not server_running:
                    if not os.path.exists("数据服务器.py"):
                        raise FileNotFoundError("数据服务器.py 文件不存在")
                    
                    subprocess.Popen([sys.executable, "数据服务器.py"], cwd=os.getcwd())
                    self.log_message("🌐 数据服务器已启动", "SUCCESS")
                    
                    # 等待服务器启动
                    import time
                    time.sleep(3)
                
                # 打开浏览器
                url = "http://localhost:8080/可视化界面_实时版.html"
                webbrowser.open(url)
                
                self.log_message("🎉 Web可视化界面已启动", "SUCCESS")
                self.show_data("Web可视化启动成功", 
                             f"✅ Web可视化界面已成功启动\n\n"
                             f"🌐 访问地址: {url}\n"
                             f"📊 功能特点:\n"
                             f"  • 双列等宽显示人气榜和飙升榜\n"
                             f"  • 实时数据自动刷新\n"
                             f"  • 支持在线生成分析报告\n"
                             f"  • 响应式设计，支持各种屏幕\n\n"
                             f"💡 提示: 浏览器界面已自动打开，您可以：\n"
                             f"  1. 查看双列股票数据\n"
                             f"  2. 点击'生成报告'按钮创建分析报告\n"
                             f"  3. 使用自动刷新功能获取最新数据")
                
            except Exception as e:
                error_msg = f"启动Web可视化失败: {str(e)}"
                self.log_message(error_msg, "ERROR")
                messagebox.showerror("启动失败", 
                                   f"Web可视化启动失败:\n\n{str(e)}\n\n"
                                   f"请检查:\n"
                                   f"• 数据服务器.py 文件是否存在\n"
                                   f"• 端口8080是否被占用\n"
                                   f"• 网络连接是否正常")
            
            finally:
                self.stop_progress()
                self.set_status("就绪", "#27ae60")
        
        # 在新线程中执行
        thread = threading.Thread(target=start_and_open, daemon=True)
        thread.start()
    
    def open_html_report(self):
        """打开HTML报告"""
        try:
            report_file = "股票分析报告.html"
            
            # 检查文件是否存在
            if not os.path.exists(report_file):
                self.log_message("报告文件不存在，请先生成报告", "ERROR")
                messagebox.showerror("文件不存在", 
                                   f"找不到报告文件: {report_file}\n\n"
                                   f"请先点击'生成分析报告'按钮生成报告。")
                return
            
            # 获取绝对路径并转换为正确的URL格式
            report_path = os.path.abspath(report_file)
            
            # Windows路径处理
            if os.name == 'nt':  # Windows系统
                report_url = f"file:///{report_path.replace(os.sep, '/')}"
            else:
                report_url = f"file://{report_path}"
            
            # 打开浏览器
            webbrowser.open(report_url)
            
            self.log_message(f"📄 分析报告已在浏览器中打开: {report_file}", "SUCCESS")
            
        except Exception as e:
            error_msg = f"打开报告失败: {str(e)}"
            self.log_message(error_msg, "ERROR")
            messagebox.showerror("打开失败", f"无法打开HTML报告:\n{str(e)}\n\n请检查浏览器设置或手动打开文件。")
    
    def refresh_data(self):
        """刷新数据显示"""
        self.log_message("🔄 手动刷新数据显示...", "INFO")
        self.load_existing_data()
        self.check_system_status_silent()
        self.log_message("✅ 数据显示已刷新", "SUCCESS")
    
    def check_system_status(self):
        """检查系统状态"""
        self.log_message("⚙️ 执行系统状态检查...", "INFO")
        
        # 更新状态显示
        self.check_system_status_silent()
        
        # 生成详细报告
        status_report = "⚙️ 系统状态详细报告\n\n"
        
        # Python信息
        status_report += f"🐍 Python版本: {sys.version}\n"
        status_report += f"📍 Python路径: {sys.executable}\n\n"
        
        # 依赖包检查
        required_modules = ["selenium", "requests", "matplotlib", "pandas", "numpy"]
        status_report += "📦 依赖包状态:\n"
        
        for module in required_modules:
            try:
                mod = __import__(module)
                version = getattr(mod, '__version__', '未知版本')
                status_report += f"  ✅ {module}: {version}\n"
            except ImportError:
                status_report += f"  ❌ {module}: 未安装\n"
        
        # 文件检查
        core_files = [
            "后台采集.py", "股票名称获取工具.py", "股票涨跌幅获取工具.py",
            "智能数据分析报告.py", "数据服务器.py"
        ]
        
        status_report += "\n📁 核心文件状态:\n"
        for file in core_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                status_report += f"  ✅ {file}: {size/1024:.1f} KB\n"
            else:
                status_report += f"  ❌ {file}: 不存在\n"
        
        self.show_data("系统状态检查", status_report)
        self.log_message("✅ 系统状态检查完成", "SUCCESS")
    
    def exit_application(self):
        """退出应用程序"""
        if messagebox.askyesno("确认退出", "确定要退出股票数据采集系统吗？"):
            self.log_message("🚪 正在退出系统...", "INFO")
            self.root.quit()
            self.root.destroy()

def main():
    """主函数"""
    root = tk.Tk()
    app = StockVisualizationSystem(root)
    root.mainloop()

if __name__ == "__main__":
    main()

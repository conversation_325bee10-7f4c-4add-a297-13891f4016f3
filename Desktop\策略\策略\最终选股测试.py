#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终选股功能测试
验证选股功能是否完全修复
"""

import subprocess
import sys
import os
import time

def test_final_selection():
    """最终选股功能测试"""
    print("🎯 最终选股功能测试")
    print("=" * 40)
    
    # 检查数据文件
    if not os.path.exists('popularity.csv') or not os.path.exists('soaring.csv'):
        print("❌ 数据文件不存在，请先运行数据采集")
        return False
    
    print("✅ 数据文件存在")
    
    # 测试1: 直接运行智能选股系统
    print("\n🎯 测试1: 直接运行智能选股系统")
    try:
        result = subprocess.run([sys.executable, '智能选股系统.py'], cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ 智能选股系统运行成功")
            
            # 检查输出文件
            if os.path.exists('选股结果.json'):
                size = os.path.getsize('选股结果.json')
                print(f"✅ 选股结果.json: {size/1024:.1f} KB")
            else:
                print("❌ 选股结果.json 未生成")
                return False
        else:
            print("❌ 智能选股系统运行失败")
            return False
            
    except Exception as e:
        print(f"❌ 运行出错: {str(e)}")
        return False
    
    # 测试2: 运行选股报告生成器
    print("\n📋 测试2: 运行选股报告生成器")
    try:
        result = subprocess.run([sys.executable, '选股报告生成器.py'], cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ 选股报告生成器运行成功")
            
            if os.path.exists('智能选股分析报告.html'):
                size = os.path.getsize('智能选股分析报告.html')
                print(f"✅ 智能选股分析报告.html: {size/1024:.1f} KB")
            else:
                print("❌ HTML报告未生成")
                return False
        else:
            print("❌ 选股报告生成器运行失败")
            return False
            
    except Exception as e:
        print(f"❌ 运行出错: {str(e)}")
        return False
    
    # 测试3: 检查选股结果内容
    print("\n📊 测试3: 检查选股结果内容")
    try:
        import json
        with open('选股结果.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 分析股票总数: {data.get('total_stocks', 0)}")
        print(f"✅ 选股策略数量: {len(data.get('strategies', {}))}")
        
        total_recommendations = sum(len(stocks) for stocks in data.get('strategies', {}).values())
        print(f"✅ 推荐股票总数: {total_recommendations}")
        
        if total_recommendations > 0:
            print("✅ 选股结果内容正常")
        else:
            print("❌ 选股结果为空")
            return False
            
    except Exception as e:
        print(f"❌ 检查选股结果出错: {str(e)}")
        return False
    
    # 测试4: 模拟GUI完整流程
    print("\n🖥️ 测试4: 模拟GUI完整流程")
    try:
        print("  步骤1: 检查数据文件...")
        if not os.path.exists('popularity.csv') or not os.path.exists('soaring.csv'):
            print("  ❌ 数据文件检查失败")
            return False
        print("  ✅ 数据文件检查通过")
        
        print("  步骤2: 运行选股分析...")
        result = subprocess.run([sys.executable, '智能选股系统.py'], cwd=os.getcwd())
        if result.returncode != 0:
            print("  ❌ 选股分析失败")
            return False
        print("  ✅ 选股分析完成")
        
        print("  步骤3: 生成选股报告...")
        result = subprocess.run([sys.executable, '选股报告生成器.py'], cwd=os.getcwd())
        if result.returncode != 0:
            print("  ❌ 报告生成失败")
            return False
        print("  ✅ 报告生成完成")
        
        print("  步骤4: 生成行业报告...")
        result = subprocess.run([sys.executable, '增强数据显示.py'], cwd=os.getcwd())
        if result.returncode == 0:
            print("  ✅ 行业报告生成完成")
        else:
            print("  ⚠️ 行业报告生成失败（不影响主要功能）")
        
        print("  🎉 GUI完整流程模拟成功！")
        
    except Exception as e:
        print(f"  ❌ GUI流程模拟出错: {str(e)}")
        return False
    
    return True

def show_final_results():
    """显示最终结果"""
    print("\n📊 最终测试结果")
    print("=" * 30)
    
    # 检查生成的文件
    files_to_check = [
        ('选股结果.json', 'JSON格式选股结果'),
        ('推荐股票汇总.csv', 'CSV格式股票汇总'),
        ('智能选股分析报告.html', 'HTML选股报告'),
        ('股票行业整合报告.html', '行业整合报告')
    ]
    
    print("📁 生成的文件:")
    for filename, description in files_to_check:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"  ✅ {filename}: {size/1024:.1f} KB - {description}")
        else:
            print(f"  ❌ {filename}: 不存在 - {description}")
    
    # 显示选股结果摘要
    try:
        import json
        with open('选股结果.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"\n🎯 选股结果摘要:")
        print(f"  📊 分析股票: {data.get('total_stocks', 0)} 只")
        print(f"  🎯 选股策略: {len(data.get('strategies', {}))} 个")
        
        total_recommendations = sum(len(stocks) for stocks in data.get('strategies', {}).values())
        print(f"  💎 推荐股票: {total_recommendations} 只")
        
        # 计算共识推荐
        stock_votes = {}
        for strategy, stocks in data.get('strategies', {}).items():
            for stock in stocks:
                code = stock['code']
                if code not in stock_votes:
                    stock_votes[code] = 0
                stock_votes[code] += 1
        
        consensus_count = len([code for code, votes in stock_votes.items() if votes >= 2])
        print(f"  🏆 共识推荐: {consensus_count} 只")
        
    except Exception as e:
        print(f"❌ 读取选股结果失败: {str(e)}")

def main():
    """主函数"""
    print("🎯 最终选股功能测试")
    print("验证选股功能是否完全修复并可在GUI中使用")
    print("=" * 60)
    
    success = test_final_selection()
    
    if success:
        show_final_results()
        
        print(f"\n🎉 选股功能测试完全通过！")
        print(f"✅ 所有功能正常工作")
        print(f"✅ GUI中的选股功能应该可以正常使用")
        
        print(f"\n💡 使用说明:")
        print(f"  1. 启动可视化主程序")
        print(f"  2. 点击 '🎯 智能选股分析' 按钮")
        print(f"  3. 等待分析完成（可能需要1-2分钟）")
        print(f"  4. 点击 '🏆 查看选股结果' 查看结果")
        print(f"  5. 或使用 '⚡ 一键选股流程' 执行完整流程")
        
        print(f"\n📋 生成的报告:")
        print(f"  • 智能选股分析报告.html - 美观的选股报告")
        print(f"  • 股票行业整合报告.html - 行业分析报告")
        print(f"  • 选股结果.json - 详细的选股数据")
        print(f"  • 推荐股票汇总.csv - 表格格式汇总")
        
    else:
        print(f"\n❌ 选股功能测试失败")
        print(f"请检查上述错误信息并修复问题")
    
    return success

if __name__ == "__main__":
    main()

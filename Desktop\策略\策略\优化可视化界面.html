<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 股票数据采集系统 - 实时可视化界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
            margin-bottom: 20px;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #34495e;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .control-panel h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn.btn-success {
            background: linear-gradient(45deg, #27ae60, #229954);
        }

        .btn.btn-success:hover {
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }

        .btn.btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }

        .btn.btn-warning:hover {
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
        }

        .btn.btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .btn.btn-danger:hover {
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }

        .data-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .tabs {
            display: flex;
            margin-bottom: 25px;
            background: #ecf0f1;
            border-radius: 10px;
            padding: 5px;
        }

        .tab {
            flex: 1;
            background: transparent;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            color: #7f8c8d;
        }

        .tab.active {
            background: #3498db;
            color: white;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 双列布局样式 */
        .dual-column-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 25px;
        }

        .column {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            min-height: 500px;
        }

        .column h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stock-list {
            max-height: 450px;
            overflow-y: auto;
        }

        .stock-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid #ecf0f1;
            transition: background-color 0.2s;
            border-radius: 5px;
            margin-bottom: 5px;
        }

        .stock-item:hover {
            background-color: #e8f4fd;
        }

        .stock-code {
            font-weight: bold;
            color: #2c3e50;
            min-width: 80px;
            font-family: 'Courier New', monospace;
        }

        .stock-name {
            flex: 1;
            margin-left: 15px;
            color: #34495e;
        }

        .stock-change {
            font-weight: bold;
            min-width: 80px;
            text-align: right;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .stock-change.positive {
            background: #e8f5e8;
            color: #27ae60;
        }

        .stock-change.negative {
            background: #fdf2f2;
            color: #e74c3c;
        }

        .stock-change.neutral {
            background: #f8f9fa;
            color: #7f8c8d;
        }

        .data-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .table-header {
            background: #3498db;
            color: white;
            padding: 15px;
            font-weight: bold;
        }

        .table-content {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .loading-spinner {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .stats-value {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .dual-column-layout {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .button-grid {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .stock-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .stock-change {
                text-align: left;
            }
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 500px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
        }

        .modal-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🚀 股票数据采集系统</h1>
            <div class="subtitle">实时股票数据监控与分析平台</div>
            
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>系统状态: <span id="systemStatus">运行中</span></span>
                </div>
                <div class="status-item">
                    <span>📊 数据更新: <span id="lastUpdate">--</span></span>
                </div>
                <div class="status-item">
                    <span>🔄 自动刷新: <span id="autoRefresh">开启</span></span>
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <h3>🎮 控制面板</h3>
            <div class="button-grid">
                <button class="btn btn-success" onclick="refreshData()">
                    🔄 刷新数据
                </button>
                <button class="btn" onclick="runCollection()">
                    🚀 开始采集
                </button>
                <button class="btn btn-warning" onclick="generateReport()">
                    📊 生成报告
                </button>
                <button class="btn btn-danger" onclick="toggleAutoRefresh()">
                    ⏸️ 暂停刷新
                </button>
            </div>
        </div>

        <!-- 数据展示区域 -->
        <div class="data-section">
            <!-- 标签页 -->
            <div class="tabs">
                <button class="tab active" onclick="showTab('dual-view')">📊 双列视图</button>
                <button class="tab" onclick="showTab('popularity')">🔥 人气榜</button>
                <button class="tab" onclick="showTab('soaring')">🚀 飙升榜</button>
                <button class="tab" onclick="showTab('stats')">📈 数据统计</button>
            </div>

            <!-- 双列视图 -->
            <div id="dual-view" class="tab-content active">
                <div class="dual-column-layout">
                    <div class="column">
                        <h4>🔥 人气榜完整名单</h4>
                        <div id="popularityList" class="stock-list">
                            <div class="loading">
                                <div class="loading-spinner"></div>
                                正在加载人气榜数据...
                            </div>
                        </div>
                    </div>
                    
                    <div class="column">
                        <h4>🚀 飙升榜完整名单</h4>
                        <div id="soaringList" class="stock-list">
                            <div class="loading">
                                <div class="loading-spinner"></div>
                                正在加载飙升榜数据...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 人气榜 -->
            <div id="popularity" class="tab-content">
                <div class="data-table">
                    <div class="table-header">🔥 人气榜股票 (前20名)</div>
                    <div class="table-content">
                        <div id="popularityTable" class="loading">
                            <div class="loading-spinner"></div>
                            正在加载人气榜数据...
                        </div>
                    </div>
                </div>
            </div>

            <!-- 飙升榜 -->
            <div id="soaring" class="tab-content">
                <div class="data-table">
                    <div class="table-header">🚀 飙升榜股票 (前20名)</div>
                    <div class="table-content">
                        <div id="soaringTable" class="loading">
                            <div class="loading-spinner"></div>
                            正在加载飙升榜数据...
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据统计 -->
            <div id="stats" class="tab-content">
                <div class="stats-grid">
                    <div class="stats-card">
                        <div class="stats-value" id="totalStocks">--</div>
                        <div class="stats-label">总股票数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value" id="popularityCount">--</div>
                        <div class="stats-label">人气榜</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value" id="soaringCount">--</div>
                        <div class="stats-label">飙升榜</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value" id="risingCount">--</div>
                        <div class="stats-label">上涨股票</div>
                    </div>
                </div>
                
                <div class="data-table">
                    <div class="table-header">📈 市场概况</div>
                    <div class="table-content">
                        <div id="marketOverview" class="loading">
                            <div class="loading-spinner"></div>
                            正在加载统计数据...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="infoModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">信息</div>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div id="modalContent">
                <!-- 模态框内容 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let stockData = {
            popularity: [],
            soaring: [],
            codes: [],
            update_time: null
        };
        let autoRefreshEnabled = true;
        let refreshInterval;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 股票数据采集系统启动');
            loadData();
            startAutoRefresh();
        });

        // 加载数据
        function loadData() {
            console.log('📊 开始加载股票数据...');
            
            fetch('/api/stock-data')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('✅ 数据加载成功:', data);
                    stockData = data;
                    updateUI();
                    updateStatus('数据加载成功', 'success');
                })
                .catch(error => {
                    console.error('❌ 数据加载失败:', error);
                    updateStatus('数据加载失败', 'error');
                    showError('数据加载失败', error.message);
                });
        }

        // 更新界面
        function updateUI() {
            // 更新状态栏
            document.getElementById('lastUpdate').textContent = stockData.update_time || '未知';
            
            // 更新双列视图
            updateStockList('popularityList', stockData.popularity, '人气榜');
            updateStockList('soaringList', stockData.soaring, '飙升榜');
            
            // 更新表格
            updateTable('popularityTable', stockData.popularity, '人气榜');
            updateTable('soaringTable', stockData.soaring, '飙升榜');
            
            // 更新统计
            updateStats();
        }

        // 更新股票列表（双列视图）
        function updateStockList(elementId, data, title) {
            const listElement = document.getElementById(elementId);
            
            if (data && data.length > 0) {
                let html = '';
                data.forEach((item, index) => {
                    const changeValue = parseFloat(item.change.replace(/[+%]/g, ''));
                    let changeClass = 'neutral';
                    if (changeValue > 0) {
                        changeClass = 'positive';
                    } else if (changeValue < 0) {
                        changeClass = 'negative';
                    }
                    
                    html += `
                        <div class="stock-item">
                            <div class="stock-code">${item.code}</div>
                            <div class="stock-name">${item.name || '获取中...'}</div>
                            <div class="stock-change ${changeClass}">${item.change || 'N/A'}</div>
                        </div>
                    `;
                });
                listElement.innerHTML = html;
            } else {
                listElement.innerHTML = `
                    <div class="loading">
                        📭 暂无${title}数据<br>
                        <small>请点击"🚀 开始采集"获取数据</small>
                    </div>
                `;
            }
        }

        // 更新表格（前20名）
        function updateTable(elementId, data, title) {
            const tableElement = document.getElementById(elementId);
            
            if (data && data.length > 0) {
                let html = '<div style="overflow-x: auto;"><table style="width: 100%; border-collapse: collapse;">';
                html += `
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">排名</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">代码</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">名称</th>
                            <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6;">涨跌幅</th>
                        </tr>
                    </thead>
                    <tbody>
                `;
                
                data.slice(0, 20).forEach((item, index) => {
                    const changeValue = parseFloat(item.change.replace(/[+%]/g, ''));
                    let changeClass = 'neutral';
                    let changeColor = '#7f8c8d';
                    if (changeValue > 0) {
                        changeClass = 'positive';
                        changeColor = '#27ae60';
                    } else if (changeValue < 0) {
                        changeClass = 'negative';
                        changeColor = '#e74c3c';
                    }
                    
                    html += `
                        <tr style="border-bottom: 1px solid #dee2e6;">
                            <td style="padding: 12px; font-weight: bold;">${index + 1}</td>
                            <td style="padding: 12px; font-family: 'Courier New', monospace; font-weight: bold;">${item.code}</td>
                            <td style="padding: 12px;">${item.name || '获取中...'}</td>
                            <td style="padding: 12px; text-align: right; font-weight: bold; color: ${changeColor};">${item.change || 'N/A'}</td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table></div>';
                tableElement.innerHTML = html;
            } else {
                tableElement.innerHTML = `
                    <div class="loading">
                        📭 暂无${title}数据<br>
                        <small>请点击"🚀 开始采集"获取数据</small>
                    </div>
                `;
            }
        }

        // 更新统计数据
        function updateStats() {
            const totalStocks = stockData.codes ? stockData.codes.length : 0;
            const popularityCount = stockData.popularity ? stockData.popularity.length : 0;
            const soaringCount = stockData.soaring ? stockData.soaring.length : 0;
            
            // 计算上涨股票数
            let risingCount = 0;
            if (stockData.popularity) {
                risingCount += stockData.popularity.filter(item => 
                    item.change && item.change.startsWith('+')
                ).length;
            }
            if (stockData.soaring) {
                risingCount += stockData.soaring.filter(item => 
                    item.change && item.change.startsWith('+')
                ).length;
            }
            
            document.getElementById('totalStocks').textContent = totalStocks;
            document.getElementById('popularityCount').textContent = popularityCount;
            document.getElementById('soaringCount').textContent = soaringCount;
            document.getElementById('risingCount').textContent = risingCount;
            
            // 更新市场概况
            const marketOverview = document.getElementById('marketOverview');
            let overviewHtml = `
                <div style="line-height: 1.8;">
                    <p><strong>📊 数据概览:</strong></p>
                    <p>• 股票代码库: ${totalStocks} 只股票</p>
                    <p>• 人气榜股票: ${popularityCount} 只</p>
                    <p>• 飙升榜股票: ${soaringCount} 只</p>
                    <p>• 上涨股票: ${risingCount} 只</p>
                    <p>• 最后更新: ${stockData.update_time || '未知'}</p>
                </div>
            `;
            marketOverview.innerHTML = overviewHtml;
        }

        // 显示标签页
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            
            // 激活对应的标签按钮
            event.target.classList.add('active');
        }

        // 刷新数据
        function refreshData() {
            updateStatus('正在刷新数据...', 'loading');
            loadData();
        }

        // 开始采集
        function runCollection() {
            showInfo('开始数据采集', 
                '数据采集功能需要在后台运行。<br><br>' +
                '请在命令行中运行以下命令：<br>' +
                '<code style="background: #f8f9fa; padding: 10px; border-radius: 5px; display: block; margin: 10px 0;">python 后台采集.py</code>' +
                '<br>或者使用GUI系统启动采集功能。'
            );
        }

        // 生成报告
        function generateReport() {
            showInfo('生成分析报告', '正在生成智能分析报告，请稍候...<div class="loading-spinner"></div>');
            
            fetch('/api/generate-report')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'started') {
                        showInfo('报告生成中', 
                            `✅ 报告生成已启动<br>
                            📄 脚本: ${data.script}<br>
                            🕒 启动时间: ${data.timestamp}<br><br>
                            ⏳ 报告生成需要约30-60秒，完成后将自动检查<br>
                            💡 您可以继续使用其他功能`);
                        
                        setTimeout(checkReportStatus, 30000);
                    } else {
                        showInfo('生成失败', `❌ 报告生成失败<br>错误: ${data.error || '未知错误'}`);
                    }
                })
                .catch(error => {
                    console.error('生成报告失败:', error);
                    showInfo('生成失败', 
                        `❌ 无法启动报告生成<br>
                        错误: ${error.message}<br><br>
                        💡 请确保数据服务器正常运行<br>
                        或手动运行: <code>python 智能数据分析报告.py</code>`);
                });
        }

        // 检查报告状态
        function checkReportStatus() {
            fetch('/股票分析报告.html')
                .then(response => {
                    if (response.ok) {
                        showInfo('报告生成完成', 
                            `🎉 智能分析报告生成完成！<br><br>
                            📄 报告文件: 股票分析报告.html<br>
                            📊 包含完整的数据分析和图表<br><br>
                            <button class="btn btn-success" onclick="openReport()" style="margin: 10px 5px;">📖 打开报告</button>
                            <button class="btn" onclick="refreshData()" style="margin: 10px 5px;">🔄 刷新数据</button>`);
                    } else {
                        setTimeout(checkReportStatus, 30000);
                    }
                })
                .catch(error => {
                    console.error('检查报告状态失败:', error);
                });
        }

        // 打开报告
        function openReport() {
            window.open('/股票分析报告.html', '_blank');
            closeModal();
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            autoRefreshEnabled = !autoRefreshEnabled;
            const btn = event.target;
            const statusSpan = document.getElementById('autoRefresh');
            
            if (autoRefreshEnabled) {
                btn.innerHTML = '⏸️ 暂停刷新';
                btn.className = 'btn btn-danger';
                statusSpan.textContent = '开启';
                startAutoRefresh();
            } else {
                btn.innerHTML = '▶️ 开启刷新';
                btn.className = 'btn btn-success';
                statusSpan.textContent = '暂停';
                stopAutoRefresh();
            }
        }

        // 开始自动刷新
        function startAutoRefresh() {
            if (autoRefreshEnabled) {
                refreshInterval = setInterval(() => {
                    if (autoRefreshEnabled) {
                        loadData();
                    }
                }, 30000); // 30秒刷新一次
            }
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        // 更新状态
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('systemStatus');
            const indicator = document.querySelector('.status-indicator');
            
            statusElement.textContent = message;
            
            switch (type) {
                case 'success':
                    indicator.style.background = '#27ae60';
                    break;
                case 'error':
                    indicator.style.background = '#e74c3c';
                    break;
                case 'loading':
                    indicator.style.background = '#f39c12';
                    break;
                default:
                    indicator.style.background = '#3498db';
            }
        }

        // 显示信息模态框
        function showInfo(title, message) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalContent').innerHTML = message;
            document.getElementById('infoModal').style.display = 'block';
        }

        // 显示错误
        function showError(title, message) {
            showInfo(title, `❌ ${message}`);
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('infoModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('infoModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            } else if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
                event.preventDefault();
                refreshData();
            }
        });

        console.log('🎉 股票数据采集系统界面加载完成');
    </script>
</body>
</html>

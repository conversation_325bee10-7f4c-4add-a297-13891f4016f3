#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能数据分析报告工具
生成详细的股票数据分析报告，包含多维度分析
"""

import os
import json
import csv
import sys
from datetime import datetime
from collections import Counter, defaultdict

# 解决Windows控制台编码问题
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

try:
    import matplotlib.pyplot as plt
    import pandas as pd
    import numpy as np
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("Warning: matplotlib not available, charts will not be generated")

class StockAnalyzer:
    def __init__(self):
        self.popularity_data = []
        self.soaring_data = []
        self.codes_data = []
        self.names_cache = {}
        
        self.load_data()
    
    def safe_print(self, message, fallback_message=None):
        """安全打印，处理编码问题"""
        try:
            print(message)
        except UnicodeEncodeError:
            if fallback_message:
                print(fallback_message)
            else:
                # 移除emoji和特殊字符
                clean_message = message.encode('ascii', 'ignore').decode('ascii')
                print(clean_message)
    
    def load_data(self):
        """加载所有数据"""
        self.safe_print("Loading stock data...", "Loading stock data...")
        
        # 加载人气榜数据
        if os.path.exists('popularity.csv'):
            with open('popularity.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                self.popularity_data = list(reader)
        
        # 加载飙升榜数据
        if os.path.exists('soaring.csv'):
            with open('soaring.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                self.soaring_data = list(reader)
        
        # 加载股票代码
        if os.path.exists('codes.txt'):
            with open('codes.txt', 'r', encoding='utf-8') as f:
                self.codes_data = [line.strip() for line in f if line.strip()]
        
        # 加载股票名称缓存
        if os.path.exists('stock_names_cache.json'):
            with open('stock_names_cache.json', 'r', encoding='utf-8') as f:
                cache = json.load(f)
                self.names_cache = cache.get('names', {})
        
        self.safe_print(f"Data loaded successfully:")
        self.safe_print(f"  Popularity list: {len(self.popularity_data)} stocks")
        self.safe_print(f"  Soaring list: {len(self.soaring_data)} stocks")
        self.safe_print(f"  Code database: {len(self.codes_data)} stocks")
        self.safe_print(f"  Name cache: {len(self.names_cache)} stocks")
    
    def analyze_market_distribution(self):
        """分析市场分布"""
        self.safe_print("\nMarket Distribution Analysis")
        self.safe_print("-" * 40)
        
        # 分析股票代码库
        sh_codes = [code for code in self.codes_data if code.startswith('6')]
        sz_main = [code for code in self.codes_data if code.startswith('00')]
        sz_gem = [code for code in self.codes_data if code.startswith('30')]
        bj_codes = [code for code in self.codes_data if code.startswith('8')]
        
        self.safe_print(f"Stock Code Database Distribution:")
        self.safe_print(f"  Shanghai Main Board (6xxxxx): {len(sh_codes)} stocks ({len(sh_codes)/len(self.codes_data)*100:.1f}%)")
        self.safe_print(f"  Shenzhen Main Board (00xxxx): {len(sz_main)} stocks ({len(sz_main)/len(self.codes_data)*100:.1f}%)")
        self.safe_print(f"  ChiNext Board (30xxxx): {len(sz_gem)} stocks ({len(sz_gem)/len(self.codes_data)*100:.1f}%)")
        self.safe_print(f"  Beijing Stock Exchange (8xxxxx): {len(bj_codes)} stocks ({len(bj_codes)/len(self.codes_data)*100:.1f}%)")
        
        # 分析人气榜分布
        if self.popularity_data:
            pop_sh = len([d for d in self.popularity_data if d['code'].startswith('6')])
            pop_sz_main = len([d for d in self.popularity_data if d['code'].startswith('00')])
            pop_sz_gem = len([d for d in self.popularity_data if d['code'].startswith('30')])
            
            self.safe_print(f"\nPopularity List Distribution:")
            self.safe_print(f"  Shanghai Main Board: {pop_sh} stocks ({pop_sh/len(self.popularity_data)*100:.1f}%)")
            self.safe_print(f"  Shenzhen Main Board: {pop_sz_main} stocks ({pop_sz_main/len(self.popularity_data)*100:.1f}%)")
            self.safe_print(f"  ChiNext Board: {pop_sz_gem} stocks ({pop_sz_gem/len(self.popularity_data)*100:.1f}%)")
        
        # 分析飙升榜分布
        if self.soaring_data:
            soar_sh = len([d for d in self.soaring_data if d['code'].startswith('6')])
            soar_sz_main = len([d for d in self.soaring_data if d['code'].startswith('00')])
            soar_sz_gem = len([d for d in self.soaring_data if d['code'].startswith('30')])
            
            self.safe_print(f"\nSoaring List Distribution:")
            self.safe_print(f"  Shanghai Main Board: {soar_sh} stocks ({soar_sh/len(self.soaring_data)*100:.1f}%)")
            self.safe_print(f"  Shenzhen Main Board: {soar_sz_main} stocks ({soar_sz_main/len(self.soaring_data)*100:.1f}%)")
            self.safe_print(f"  ChiNext Board: {soar_sz_gem} stocks ({soar_sz_gem/len(self.soaring_data)*100:.1f}%)")
        
        return {
            'total': {'sh': len(sh_codes), 'sz_main': len(sz_main), 'sz_gem': len(sz_gem)},
            'popularity': {'sh': pop_sh, 'sz_main': pop_sz_main, 'sz_gem': pop_sz_gem} if self.popularity_data else {},
            'soaring': {'sh': soar_sh, 'sz_main': soar_sz_main, 'sz_gem': soar_sz_gem} if self.soaring_data else {}
        }
    
    def analyze_price_changes(self):
        """分析涨跌幅分布"""
        self.safe_print("\nPrice Change Analysis")
        self.safe_print("-" * 40)
        
        def parse_change(change_str):
            """解析涨跌幅字符串"""
            if not change_str or change_str in ['N/A', '停牌', '']:
                return None
            try:
                return float(change_str.replace('+', '').replace('%', ''))
            except:
                return None
        
        # 分析人气榜涨跌幅
        if self.popularity_data:
            pop_changes = [parse_change(d['change']) for d in self.popularity_data]
            pop_changes = [c for c in pop_changes if c is not None]
            
            if pop_changes and MATPLOTLIB_AVAILABLE:
                self.safe_print(f"Popularity List Price Change Statistics:")
                self.safe_print(f"  Average change: {np.mean(pop_changes):.2f}%")
                self.safe_print(f"  Maximum gain: {max(pop_changes):.2f}%")
                self.safe_print(f"  Maximum loss: {min(pop_changes):.2f}%")
                self.safe_print(f"  Rising stocks: {len([c for c in pop_changes if c > 0])} stocks")
                self.safe_print(f"  Falling stocks: {len([c for c in pop_changes if c < 0])} stocks")
                self.safe_print(f"  Flat stocks: {len([c for c in pop_changes if c == 0])} stocks")
        
        # 分析飙升榜涨跌幅
        if self.soaring_data:
            soar_changes = [parse_change(d['change']) for d in self.soaring_data]
            soar_changes = [c for c in soar_changes if c is not None]
            
            if soar_changes and MATPLOTLIB_AVAILABLE:
                self.safe_print(f"\nSoaring List Price Change Statistics:")
                self.safe_print(f"  Average change: {np.mean(soar_changes):.2f}%")
                self.safe_print(f"  Maximum gain: {max(soar_changes):.2f}%")
                self.safe_print(f"  Maximum loss: {min(soar_changes):.2f}%")
                self.safe_print(f"  Rising stocks: {len([c for c in soar_changes if c > 0])} stocks")
                self.safe_print(f"  Falling stocks: {len([c for c in soar_changes if c < 0])} stocks")
                self.safe_print(f"  Flat stocks: {len([c for c in soar_changes if c == 0])} stocks")
        
        return {
            'popularity': pop_changes if self.popularity_data else [],
            'soaring': soar_changes if self.soaring_data else []
        }
    
    def find_top_performers(self):
        """找出表现最好的股票"""
        self.safe_print("\nTop Performing Stocks")
        self.safe_print("-" * 40)
        
        def parse_change(change_str):
            if not change_str or change_str in ['N/A', '停牌', '']:
                return 0
            try:
                return float(change_str.replace('+', '').replace('%', ''))
            except:
                return 0
        
        # 合并两个榜单的数据
        all_stocks = []
        
        for stock in self.popularity_data:
            change = parse_change(stock['change'])
            all_stocks.append({
                'code': stock['code'],
                'name': stock['name'],
                'change': change,
                'source': 'Popularity'
            })
        
        for stock in self.soaring_data:
            change = parse_change(stock['change'])
            all_stocks.append({
                'code': stock['code'],
                'name': stock['name'],
                'change': change,
                'source': 'Soaring'
            })
        
        # 去重并排序
        unique_stocks = {}
        for stock in all_stocks:
            code = stock['code']
            if code not in unique_stocks or abs(stock['change']) > abs(unique_stocks[code]['change']):
                unique_stocks[code] = stock
        
        sorted_stocks = sorted(unique_stocks.values(), key=lambda x: x['change'], reverse=True)
        
        self.safe_print("Top 10 Gaining Stocks:")
        for i, stock in enumerate(sorted_stocks[:10]):
            if stock['change'] > 0:
                self.safe_print(f"  {i+1:2d}. {stock['code']} {stock['name']} +{stock['change']:.2f}% ({stock['source']})")
        
        self.safe_print("\nTop 10 Losing Stocks:")
        bottom_stocks = sorted(unique_stocks.values(), key=lambda x: x['change'])
        for i, stock in enumerate(bottom_stocks[:10]):
            if stock['change'] < 0:
                self.safe_print(f"  {i+1:2d}. {stock['code']} {stock['name']} {stock['change']:.2f}% ({stock['source']})")
        
        return sorted_stocks
    
    def analyze_overlap(self):
        """分析两个榜单的重叠情况"""
        self.safe_print("\nList Overlap Analysis")
        self.safe_print("-" * 40)
        
        if not self.popularity_data or not self.soaring_data:
            self.safe_print("Insufficient data for overlap analysis")
            return
        
        pop_codes = set(d['code'] for d in self.popularity_data)
        soar_codes = set(d['code'] for d in self.soaring_data)
        
        overlap_codes = pop_codes & soar_codes
        pop_only = pop_codes - soar_codes
        soar_only = soar_codes - pop_codes
        
        self.safe_print(f"Overlap Statistics:")
        self.safe_print(f"  Popularity list only: {len(pop_only)} stocks ({len(pop_only)/len(pop_codes)*100:.1f}%)")
        self.safe_print(f"  Soaring list only: {len(soar_only)} stocks ({len(soar_only)/len(soar_codes)*100:.1f}%)")
        self.safe_print(f"  Both lists: {len(overlap_codes)} stocks")
        
        if overlap_codes:
            self.safe_print(f"\nStocks appearing in both lists:")
            overlap_stocks = []
            for code in list(overlap_codes)[:10]:  # 只显示前10个
                pop_stock = next((d for d in self.popularity_data if d['code'] == code), None)
                soar_stock = next((d for d in self.soaring_data if d['code'] == code), None)
                if pop_stock and soar_stock:
                    self.safe_print(f"  {code} {pop_stock['name']} (Pop:{pop_stock['change']} Soar:{soar_stock['change']})")
        
        return {
            'overlap': len(overlap_codes),
            'pop_only': len(pop_only),
            'soar_only': len(soar_only)
        }
    
    def generate_charts(self):
        """生成图表"""
        if not MATPLOTLIB_AVAILABLE:
            self.safe_print("\nSkipping chart generation (matplotlib not available)")
            return
        
        self.safe_print("\nGenerating analysis charts...")
        
        try:
            # 创建图表目录
            if not os.path.exists('charts'):
                os.makedirs('charts')
            
            # 图表1: 市场分布饼图
            market_data = self.analyze_market_distribution()
            if market_data['total']:
                fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 5))
                
                # 总体分布
                labels = ['Shanghai Main', 'Shenzhen Main', 'ChiNext']
                sizes = [market_data['total']['sh'], market_data['total']['sz_main'], market_data['total']['sz_gem']]
                colors = ['#ff9999', '#66b3ff', '#99ff99']
                
                ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
                ax1.set_title('Stock Code Database Distribution')
                
                # 人气榜分布
                if market_data['popularity']:
                    sizes_pop = [market_data['popularity']['sh'], market_data['popularity']['sz_main'], market_data['popularity']['sz_gem']]
                    ax2.pie(sizes_pop, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
                    ax2.set_title('Popularity List Distribution')
                
                # 飙升榜分布
                if market_data['soaring']:
                    sizes_soar = [market_data['soaring']['sh'], market_data['soaring']['sz_main'], market_data['soaring']['sz_gem']]
                    ax3.pie(sizes_soar, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
                    ax3.set_title('Soaring List Distribution')
                
                plt.tight_layout()
                plt.savefig('charts/market_distribution.png', dpi=300, bbox_inches='tight')
                plt.close()
                self.safe_print("  Market distribution chart saved: charts/market_distribution.png")
            
            # 图表2: 涨跌幅分布直方图
            change_data = self.analyze_price_changes()
            if change_data['popularity'] or change_data['soaring']:
                fig, axes = plt.subplots(1, 2, figsize=(12, 5))
                
                if change_data['popularity']:
                    axes[0].hist(change_data['popularity'], bins=20, alpha=0.7, color='red', edgecolor='black')
                    axes[0].set_title('Popularity List Price Change Distribution')
                    axes[0].set_xlabel('Price Change (%)')
                    axes[0].set_ylabel('Number of Stocks')
                    axes[0].grid(True, alpha=0.3)
                
                if change_data['soaring']:
                    axes[1].hist(change_data['soaring'], bins=20, alpha=0.7, color='blue', edgecolor='black')
                    axes[1].set_title('Soaring List Price Change Distribution')
                    axes[1].set_xlabel('Price Change (%)')
                    axes[1].set_ylabel('Number of Stocks')
                    axes[1].grid(True, alpha=0.3)
                
                plt.tight_layout()
                plt.savefig('charts/change_distribution.png', dpi=300, bbox_inches='tight')
                plt.close()
                self.safe_print("  Price change distribution chart saved: charts/change_distribution.png")
            
        except Exception as e:
            self.safe_print(f"  Chart generation failed: {str(e)}")
    
    def generate_html_report(self):
        """生成HTML报告"""
        self.safe_print("\nGenerating HTML report...")
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 获取分析数据
        market_data = self.analyze_market_distribution()
        change_data = self.analyze_price_changes()
        top_stocks = self.find_top_performers()
        overlap_data = self.analyze_overlap()
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票数据分析报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; border-left: 4px solid #3498db; padding-left: 15px; }}
        .summary {{ background: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; }}
        .metric {{ display: inline-block; margin: 10px 20px; text-align: center; }}
        .metric-value {{ font-size: 2em; font-weight: bold; color: #e74c3c; }}
        .metric-label {{ color: #7f8c8d; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #3498db; color: white; }}
        tr:hover {{ background-color: #f5f5f5; }}
        .positive {{ color: #27ae60; font-weight: bold; }}
        .negative {{ color: #e74c3c; font-weight: bold; }}
        .chart {{ text-align: center; margin: 20px 0; }}
        .footer {{ text-align: center; color: #7f8c8d; margin-top: 30px; border-top: 1px solid #ddd; padding-top: 20px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Stock Data Analysis Report</h1>
        
        <div class="summary">
            <h2>Data Overview</h2>
            <div class="metric">
                <div class="metric-value">{len(self.codes_data)}</div>
                <div class="metric-label">Total Stock Codes</div>
            </div>
            <div class="metric">
                <div class="metric-value">{len(self.popularity_data)}</div>
                <div class="metric-label">Popularity List</div>
            </div>
            <div class="metric">
                <div class="metric-value">{len(self.soaring_data)}</div>
                <div class="metric-label">Soaring List</div>
            </div>
            <div class="metric">
                <div class="metric-value">{len(self.names_cache)}</div>
                <div class="metric-label">Cached Names</div>
            </div>
            <p><strong>Report Generated:</strong> {current_time}</p>
        </div>
        
        <h2>Market Distribution Analysis</h2>
        <table>
            <tr><th>Market</th><th>Code Database</th><th>Popularity List</th><th>Soaring List</th></tr>
            <tr><td>Shanghai Main Board</td><td>{market_data['total']['sh']}</td><td>{market_data['popularity'].get('sh', 0)}</td><td>{market_data['soaring'].get('sh', 0)}</td></tr>
            <tr><td>Shenzhen Main Board</td><td>{market_data['total']['sz_main']}</td><td>{market_data['popularity'].get('sz_main', 0)}</td><td>{market_data['soaring'].get('sz_main', 0)}</td></tr>
            <tr><td>ChiNext Board</td><td>{market_data['total']['sz_gem']}</td><td>{market_data['popularity'].get('sz_gem', 0)}</td><td>{market_data['soaring'].get('sz_gem', 0)}</td></tr>
        </table>
        
        <h2>Top Performing Stocks (Top 10)</h2>
        <table>
            <tr><th>Rank</th><th>Stock Code</th><th>Stock Name</th><th>Price Change</th><th>Source</th></tr>
        """
        
        # 添加前10只表现最好的股票
        for i, stock in enumerate(top_stocks[:10]):
            if stock['change'] > 0:
                change_class = 'positive'
                change_text = f"+{stock['change']:.2f}%"
            elif stock['change'] < 0:
                change_class = 'negative'
                change_text = f"{stock['change']:.2f}%"
            else:
                change_class = ''
                change_text = "0.00%"
            
            html_content += f"""
            <tr>
                <td>{i+1}</td>
                <td>{stock['code']}</td>
                <td>{stock['name']}</td>
                <td class="{change_class}">{change_text}</td>
                <td>{stock['source']}</td>
            </tr>
            """
        
        html_content += f"""
        </table>
        
        <h2>List Overlap Analysis</h2>
        <p>Popularity list only: <strong>{overlap_data.get('pop_only', 0)}</strong> stocks</p>
        <p>Soaring list only: <strong>{overlap_data.get('soar_only', 0)}</strong> stocks</p>
        <p>Both lists: <strong>{overlap_data.get('overlap', 0)}</strong> stocks</p>
        
        <div class="chart">
            <h2>Analysis Charts</h2>
            <p>Chart files saved to charts/ directory</p>
            <p>• market_distribution.png - Market Distribution Chart</p>
            <p>• change_distribution.png - Price Change Distribution Chart</p>
        </div>
        
        <div class="footer">
            <p>Stock Data Collection System - Intelligent Analysis Report</p>
            <p>Generated: {current_time}</p>
        </div>
    </div>
</body>
</html>
        """
        
        # 保存HTML报告
        with open('股票分析报告.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.safe_print("  HTML report saved: 股票分析报告.html")
        
        return '股票分析报告.html'

def main():
    """主函数"""
    try:
        print("Intelligent Stock Data Analysis System")
        print("=" * 50)
        
        analyzer = StockAnalyzer()
        
        # 执行各种分析
        analyzer.analyze_market_distribution()
        analyzer.analyze_price_changes()
        analyzer.find_top_performers()
        analyzer.analyze_overlap()
        
        # 生成图表和报告
        analyzer.generate_charts()
        report_file = analyzer.generate_html_report()
        
        print(f"\nAnalysis completed!")
        print(f"HTML Report: {report_file}")
        print(f"Charts Directory: charts/")
        print(f"Open {report_file} in browser to view the complete report")
        
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

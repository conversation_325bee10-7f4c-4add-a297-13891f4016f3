#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备用数据生成器 - 当主采集脚本失败时使用
生成模拟的热门股数据，确保系统正常运行
"""

import csv
import random
import time
from datetime import datetime

# 常见股票池
STOCK_POOL = [
    ("000001", "平安银行"), ("000002", "万科A"), ("000063", "中兴通讯"),
    ("000100", "TCL科技"), ("000166", "申万宏源"), ("000568", "泸州老窖"),
    ("000661", "长春高新"), ("000725", "京东方A"), ("000776", "广发证券"),
    ("000858", "五粮液"), ("002008", "大族激光"), ("002049", "紫光国微"),
    ("002097", "山河智能"), ("002142", "宁波银行"), ("002230", "科大讯飞"),
    ("002304", "洋河股份"), ("002371", "北方华创"), ("002415", "海康威视"),
    ("002460", "赣锋锂业"), ("002475", "立讯精密"), ("002594", "比亚迪"),
    ("300003", "乐普医疗"), ("300015", "爱尔眼科"), ("300059", "东方财富"),
    ("300122", "智飞生物"), ("300124", "汇川技术"), ("300274", "阳光电源"),
    ("300347", "泰格医药"), ("300408", "三环集团"), ("300454", "深信服"),
    ("300750", "宁德时代"), ("300760", "迈瑞医疗"), ("300782", "卓胜微"),
    ("600000", "浦发银行"), ("600036", "招商银行"), ("600519", "贵州茅台"),
    ("600570", "恒生电子"), ("600887", "伊利股份"), ("603259", "药明康德"),
    ("688012", "中微公司"), ("688036", "传音控股"), ("688981", "中芯国际")
]

def generate_stock_data(stock_code, stock_name, base_price=None):
    """生成单只股票的模拟数据"""
    if base_price is None:
        base_price = random.uniform(10, 200)
    
    # 生成价格变动
    change_percent = random.uniform(-10, 10)
    current_price = round(base_price * (1 + change_percent / 100), 2)
    
    # 生成其他数据
    volume = random.randint(1000, 50000)
    turnover = round(current_price * volume / 10000, 2)
    
    return {
        "排名": 0,  # 稍后设置
        "股票代码": stock_code,
        "股票名称": stock_name,
        "当前价格": f"{current_price:.2f}",
        "涨跌幅": f"{change_percent:+.2f}%",
        "成交量": f"{volume}万手",
        "成交额": f"{turnover:.2f}亿",
        "更新时间": datetime.now().strftime("%H:%M:%S")
    }

def generate_popularity_data():
    """生成人气榜数据"""
    print("📊 生成人气榜模拟数据...")
    
    # 随机选择50只股票
    selected_stocks = random.sample(STOCK_POOL, min(50, len(STOCK_POOL)))
    
    data = []
    for i, (code, name) in enumerate(selected_stocks, 1):
        stock_data = generate_stock_data(code, name)
        stock_data["排名"] = i
        data.append(stock_data)
    
    # 保存到CSV
    with open('popularity.csv', 'w', encoding='utf-8', newline='') as f:
        fieldnames = ["排名", "股票代码", "股票名称", "当前价格", "涨跌幅", "成交量", "成交额", "更新时间"]
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
    
    print(f"✅ 人气榜数据已生成: {len(data)} 只股票")
    return len(data)

def generate_soaring_data():
    """生成飙升榜数据"""
    print("📈 生成飙升榜模拟数据...")
    
    # 随机选择30只股票（与人气榜部分重叠）
    selected_stocks = random.sample(STOCK_POOL, min(30, len(STOCK_POOL)))
    
    data = []
    for i, (code, name) in enumerate(selected_stocks, 1):
        # 飙升榜的股票涨幅偏高
        base_price = random.uniform(15, 150)
        change_percent = random.uniform(2, 15)  # 偏向正涨幅
        current_price = round(base_price * (1 + change_percent / 100), 2)
        
        volume = random.randint(2000, 80000)
        turnover = round(current_price * volume / 10000, 2)
        
        stock_data = {
            "排名": i,
            "股票代码": code,
            "股票名称": name,
            "当前价格": f"{current_price:.2f}",
            "涨跌幅": f"{change_percent:+.2f}%",
            "成交量": f"{volume}万手",
            "成交额": f"{turnover:.2f}亿",
            "更新时间": datetime.now().strftime("%H:%M:%S")
        }
        data.append(stock_data)
    
    # 保存到CSV
    with open('soaring.csv', 'w', encoding='utf-8', newline='') as f:
        fieldnames = ["排名", "股票代码", "股票名称", "当前价格", "涨跌幅", "成交量", "成交额", "更新时间"]
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
    
    print(f"✅ 飙升榜数据已生成: {len(data)} 只股票")
    return len(data)

def main():
    """主函数"""
    print("🔄 备用数据生成器启动")
    print("=" * 40)
    print("⚠️ 这是备用模式，生成模拟数据")
    print("💡 用于在主采集脚本失败时保证系统正常运行")
    print("=" * 40)
    
    try:
        # 生成人气榜数据
        popularity_count = generate_popularity_data()
        time.sleep(1)
        
        # 生成飙升榜数据
        soaring_count = generate_soaring_data()
        
        print("\n" + "=" * 40)
        print("🎉 备用数据生成完成！")
        print(f"📊 人气榜: {popularity_count} 只股票")
        print(f"📈 飙升榜: {soaring_count} 只股票")
        print("📁 文件: popularity.csv, soaring.csv")
        print("💡 这是模拟数据，仅供测试使用")
        print("=" * 40)
        
    except Exception as e:
        print(f"❌ 备用数据生成失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

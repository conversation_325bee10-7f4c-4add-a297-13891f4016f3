import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
import json
import os
from datetime import datetime
import threading
import subprocess

class StockDataVisualization:
    def __init__(self, root):
        self.root = root
        self.root.title("📊 股票数据采集系统 - 可视化仪表板")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # 设置样式
        self.setup_styles()
        
        # 创建主界面
        self.create_main_interface()
        
        # 加载数据
        self.load_data()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', font=('Microsoft YaHei', 16, 'bold'), background='#f0f0f0')
        style.configure('Header.TLabel', font=('Microsoft YaHei', 12, 'bold'), background='#f0f0f0')
        style.configure('Info.TLabel', font=('Microsoft YaHei', 10), background='#f0f0f0')
        style.configure('Success.TButton', background='#28a745')
        style.configure('Primary.TButton', background='#007bff')
        style.configure('Warning.TButton', background='#ffc107')
        
    def create_main_interface(self):
        """创建主界面"""
        # 标题栏
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=0, pady=0)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="📊 股票数据采集系统", 
                              font=('Microsoft YaHei', 20, 'bold'), 
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(title_frame, text="智能化股票数据采集与分析平台", 
                                 font=('Microsoft YaHei', 12), 
                                 fg='#ecf0f1', bg='#2c3e50')
        subtitle_label.pack()
        
        # 主内容区域
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 左侧面板
        left_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        left_frame.pack(side='left', fill='y', padx=(0, 5))
        
        self.create_control_panel(left_frame)
        self.create_stats_panel(left_frame)
        
        # 右侧面板
        right_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        self.create_chart_panel(right_frame)
        self.create_data_panel(right_frame)
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = tk.LabelFrame(parent, text="🎮 控制面板", 
                                     font=('Microsoft YaHei', 12, 'bold'),
                                     bg='white', fg='#2c3e50', padx=10, pady=10)
        control_frame.pack(fill='x', padx=10, pady=10)
        
        # 按钮样式配置
        button_config = {
            'font': ('Microsoft YaHei', 10),
            'width': 15,
            'pady': 5
        }
        
        # 采集按钮
        collect_btn = tk.Button(control_frame, text="🚀 开始采集", 
                               bg='#007bff', fg='white',
                               command=self.start_collection, **button_config)
        collect_btn.pack(pady=5)
        
        # 处理数据按钮
        process_btn = tk.Button(control_frame, text="⚙️ 处理数据", 
                               bg='#28a745', fg='white',
                               command=self.process_data, **button_config)
        process_btn.pack(pady=5)
        
        # 生成报告按钮
        report_btn = tk.Button(control_frame, text="📊 生成报告", 
                              bg='#17a2b8', fg='white',
                              command=self.generate_report, **button_config)
        report_btn.pack(pady=5)
        
        # 刷新数据按钮
        refresh_btn = tk.Button(control_frame, text="🔄 刷新数据", 
                               bg='#6c757d', fg='white',
                               command=self.refresh_data, **button_config)
        refresh_btn.pack(pady=5)
        
    def create_stats_panel(self, parent):
        """创建统计面板"""
        stats_frame = tk.LabelFrame(parent, text="📈 数据统计", 
                                   font=('Microsoft YaHei', 12, 'bold'),
                                   bg='white', fg='#2c3e50', padx=10, pady=10)
        stats_frame.pack(fill='x', padx=10, pady=10)
        
        # 统计标签
        self.total_stocks_label = tk.Label(stats_frame, text="总股票数: 0", 
                                          font=('Microsoft YaHei', 11), bg='white')
        self.total_stocks_label.pack(anchor='w', pady=2)
        
        self.sh_stocks_label = tk.Label(stats_frame, text="沪市股票: 0", 
                                       font=('Microsoft YaHei', 11), bg='white', fg='#e74c3c')
        self.sh_stocks_label.pack(anchor='w', pady=2)
        
        self.sz_stocks_label = tk.Label(stats_frame, text="深市股票: 0", 
                                       font=('Microsoft YaHei', 11), bg='white', fg='#2ecc71')
        self.sz_stocks_label.pack(anchor='w', pady=2)
        
        self.popularity_label = tk.Label(stats_frame, text="人气榜: 0", 
                                        font=('Microsoft YaHei', 11), bg='white')
        self.popularity_label.pack(anchor='w', pady=2)
        
        self.soaring_label = tk.Label(stats_frame, text="飙升榜: 0", 
                                     font=('Microsoft YaHei', 11), bg='white')
        self.soaring_label.pack(anchor='w', pady=2)
        
        # 更新时间
        self.update_time_label = tk.Label(stats_frame, text="更新时间: --", 
                                         font=('Microsoft YaHei', 9), bg='white', fg='#6c757d')
        self.update_time_label.pack(anchor='w', pady=(10, 2))
        
    def create_chart_panel(self, parent):
        """创建图表面板"""
        chart_frame = tk.LabelFrame(parent, text="📊 数据可视化", 
                                   font=('Microsoft YaHei', 12, 'bold'),
                                   bg='white', fg='#2c3e50')
        chart_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 创建matplotlib图表
        self.fig, (self.ax1, self.ax2) = plt.subplots(1, 2, figsize=(10, 4))
        self.fig.patch.set_facecolor('white')
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)
        
    def create_data_panel(self, parent):
        """创建数据面板"""
        data_frame = tk.LabelFrame(parent, text="📋 实时数据", 
                                  font=('Microsoft YaHei', 12, 'bold'),
                                  bg='white', fg='#2c3e50')
        data_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 创建Treeview表格
        columns = ('代码', '名称', '市场', '涨跌幅', '状态')
        self.tree = ttk.Treeview(data_frame, columns=columns, show='headings', height=8)
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100, anchor='center')
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(data_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side='right', fill='y', padx=(0, 10), pady=10)
        
    def load_data(self):
        """加载数据"""
        try:
            # 加载股票代码数据
            if os.path.exists('codes.txt'):
                with open('codes.txt', 'r', encoding='utf-8') as f:
                    codes = [line.strip() for line in f if line.strip()]
                    self.codes_data = codes
            else:
                self.codes_data = []
            
            # 加载缓存数据
            if os.path.exists('stock_cache.json'):
                with open('stock_cache.json', 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                    self.cached_stocks = cache_data.get('stocks', [])
            else:
                self.cached_stocks = []
            
            # 加载人气榜数据
            if os.path.exists('popularity.csv'):
                self.popularity_data = pd.read_csv('popularity.csv')
            else:
                self.popularity_data = pd.DataFrame()
            
            # 加载飙升榜数据
            if os.path.exists('soaring.csv'):
                self.soaring_data = pd.read_csv('soaring.csv')
            else:
                self.soaring_data = pd.DataFrame()
            
            # 更新界面
            self.update_stats()
            self.update_charts()
            self.update_data_table()
            
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败: {e}")
    
    def update_stats(self):
        """更新统计信息"""
        total_stocks = len(self.codes_data)
        sh_stocks = len([code for code in self.codes_data if code.startswith('6')])
        sz_stocks = len([code for code in self.codes_data if code.startswith(('00', '30'))])
        popularity_count = len(self.popularity_data)
        soaring_count = len(self.soaring_data)
        
        self.total_stocks_label.config(text=f"总股票数: {total_stocks}")
        self.sh_stocks_label.config(text=f"沪市股票: {sh_stocks}")
        self.sz_stocks_label.config(text=f"深市股票: {sz_stocks}")
        self.popularity_label.config(text=f"人气榜: {popularity_count}")
        self.soaring_label.config(text=f"飙升榜: {soaring_count}")
        self.update_time_label.config(text=f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def update_charts(self):
        """更新图表"""
        # 清空图表
        self.ax1.clear()
        self.ax2.clear()
        
        # 市场分布饼图
        if self.codes_data:
            sh_count = len([code for code in self.codes_data if code.startswith('6')])
            sz_count = len([code for code in self.codes_data if code.startswith(('00', '30'))])
            
            if sh_count > 0 or sz_count > 0:
                labels = ['沪市', '深市']
                sizes = [sh_count, sz_count]
                colors = ['#e74c3c', '#2ecc71']
                
                self.ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
                self.ax1.set_title('市场分布')
        
        # 数据量对比柱状图
        categories = ['总股票', '人气榜', '飙升榜']
        values = [len(self.codes_data), len(self.popularity_data), len(self.soaring_data)]
        colors = ['#3498db', '#f39c12', '#e74c3c']
        
        bars = self.ax2.bar(categories, values, color=colors)
        self.ax2.set_title('数据量统计')
        self.ax2.set_ylabel('数量')
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            self.ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                         f'{value}', ha='center', va='bottom')
        
        # 刷新画布
        self.canvas.draw()
    
    def update_data_table(self):
        """更新数据表格"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加人气榜数据（前10条）
        if not self.popularity_data.empty:
            for i, row in self.popularity_data.head(10).iterrows():
                code = row.get('code', '')
                name = row.get('name', '未知')
                market = 'SH' if code.startswith('6') else 'SZ'
                change = row.get('change', 'N/A')
                status = '✅'
                
                self.tree.insert('', 'end', values=(code, name, market, change, status))
    
    def start_collection(self):
        """开始数据采集"""
        def run_collection():
            try:
                messagebox.showinfo("提示", "开始执行数据采集，请稍候...")
                
                # 运行股票代码提取
                subprocess.run(['python', '全自动提取股票代码.py'], 
                             cwd=os.getcwd(), check=True)
                
                # 运行人气榜采集
                subprocess.run(['python', '采集人气榜_改进版.py'], 
                             cwd=os.getcwd(), check=True)
                
                # 刷新数据
                self.root.after(0, self.refresh_data)
                self.root.after(0, lambda: messagebox.showinfo("成功", "数据采集完成！"))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"采集失败: {e}"))
        
        # 在后台线程中运行
        threading.Thread(target=run_collection, daemon=True).start()
    
    def process_data(self):
        """处理数据"""
        def run_processing():
            try:
                subprocess.run(['python', '从codes.txt读取股票代码.py'], 
                             cwd=os.getcwd(), check=True)
                
                self.root.after(0, self.refresh_data)
                self.root.after(0, lambda: messagebox.showinfo("成功", "数据处理完成！"))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"处理失败: {e}"))
        
        threading.Thread(target=run_processing, daemon=True).start()
    
    def generate_report(self):
        """生成报告"""
        def run_report():
            try:
                subprocess.run(['python', '数据分析报告.py'], 
                             cwd=os.getcwd(), check=True)
                
                self.root.after(0, lambda: messagebox.showinfo("成功", "报告生成完成！"))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"报告生成失败: {e}"))
        
        threading.Thread(target=run_report, daemon=True).start()
    
    def refresh_data(self):
        """刷新数据"""
        self.load_data()
        messagebox.showinfo("提示", "数据已刷新！")

def main():
    root = tk.Tk()
    app = StockDataVisualization(root)
    root.mainloop()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断采集问题
检查后台采集为什么会采集到板块而不是股票
"""

import csv
import os
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import time

def check_current_data():
    """检查当前数据文件"""
    print("🔍 检查当前数据文件")
    print("=" * 40)
    
    files = ['popularity.csv', 'soaring.csv']
    
    for filename in files:
        if os.path.exists(filename):
            print(f"\n📊 {filename}:")
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    data = list(reader)
                
                print(f"  总记录数: {len(data)}")
                print(f"  字段: {list(data[0].keys()) if data else '无数据'}")
                
                if data:
                    print("  前5条记录:")
                    for i, stock in enumerate(data[:5], 1):
                        code = stock.get('code', 'N/A')
                        name = stock.get('name', 'N/A')
                        change = stock.get('change', 'N/A')
                        price = stock.get('price', 'N/A')
                        
                        # 检查是否是真实股票代码
                        is_real_code = bool(re.match(r'^[036]\d{5}$', code))
                        code_type = "✅真实股票" if is_real_code else "❌疑似板块"
                        
                        print(f"    {i}. {code} {name} {change} ¥{price} ({code_type})")
                
            except Exception as e:
                print(f"  ❌ 读取失败: {str(e)}")
        else:
            print(f"\n❌ {filename} 不存在")

def test_website_access():
    """测试网站访问"""
    print(f"\n🌐 测试网站访问")
    print("=" * 40)
    
    try:
        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 无界面模式
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        print("🚀 启动浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 访问网站
        url = "https://data.10jqka.com.cn/rank/lhb/"
        print(f"📡 访问网站: {url}")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(5)
        
        # 检查页面标题
        title = driver.title
        print(f"📄 页面标题: {title}")
        
        # 检查页面内容
        page_source = driver.page_source
        print(f"📝 页面内容长度: {len(page_source)} 字符")
        
        # 查找股票代码
        stock_codes = re.findall(r'\b([036]\d{5})\b', page_source)
        unique_codes = list(set(stock_codes))
        print(f"🔍 找到股票代码: {len(unique_codes)} 个")
        
        if unique_codes:
            print("  示例代码:", unique_codes[:10])
        else:
            print("  ❌ 未找到任何股票代码")
        
        # 查找表格或列表元素
        try:
            tables = driver.find_elements(By.TAG_NAME, "table")
            print(f"📊 找到表格: {len(tables)} 个")
            
            rows = driver.find_elements(By.TAG_NAME, "tr")
            print(f"📋 找到表格行: {len(rows)} 个")
            
            if rows:
                print("  前3行内容:")
                for i, row in enumerate(rows[:3], 1):
                    text = row.text.strip()
                    if text:
                        print(f"    {i}. {text[:100]}...")
        except Exception as e:
            print(f"  ❌ 查找表格失败: {str(e)}")
        
        driver.quit()
        print("✅ 网站访问测试完成")
        
    except Exception as e:
        print(f"❌ 网站访问失败: {str(e)}")

def analyze_extraction_logic():
    """分析提取逻辑"""
    print(f"\n🔧 分析提取逻辑")
    print("=" * 40)
    
    # 模拟一些可能的页面文本
    test_texts = [
        "600036 招商银行 +2.15% 45.67",
        "000001 平安银行 -1.23% 12.34", 
        "300015 爱尔眼科 +4.56% 23.45",
        "银行板块 +1.5%",
        "科技板块 -0.8%",
        "医药生物 +2.3%",
        "房地产 -1.2%"
    ]
    
    print("🧪 测试文本提取:")
    for text in test_texts:
        print(f"\n  测试文本: {text}")
        
        # 查找股票代码
        codes = re.findall(r'\b(\d{6})\b', text)
        valid_codes = [code for code in codes if code.startswith(('00', '30', '60'))]
        
        # 查找中文名称
        chinese_parts = re.findall(r'[\u4e00-\u9fa5]{2,8}', text)
        
        # 查找涨跌幅
        change_match = re.search(r'[+-]?\d+\.?\d*%', text)
        change = change_match.group() if change_match else ""
        
        # 查找价格
        price_match = re.search(r'(\d+\.\d{2})', text)
        price = price_match.group() if price_match else ""
        
        print(f"    股票代码: {valid_codes}")
        print(f"    中文部分: {chinese_parts}")
        print(f"    涨跌幅: {change}")
        print(f"    价格: {price}")
        
        if valid_codes and chinese_parts:
            print(f"    ✅ 可提取股票: {valid_codes[0]} {chinese_parts[0]}")
        else:
            print(f"    ❌ 无法提取有效股票信息")

def check_name_cache():
    """检查名称缓存"""
    print(f"\n🏷️ 检查股票名称缓存")
    print("=" * 40)
    
    if os.path.exists('stock_names_cache.json'):
        try:
            import json
            with open('stock_names_cache.json', 'r', encoding='utf-8') as f:
                cache = json.load(f)
                names = cache.get('names', {})
            
            print(f"📦 缓存记录数: {len(names)}")
            
            # 检查一些示例
            sample_codes = ['600036', '000001', '300015', '000002', '600519']
            print("  示例缓存:")
            for code in sample_codes:
                name = names.get(code, '未找到')
                print(f"    {code}: {name}")
                
        except Exception as e:
            print(f"❌ 读取缓存失败: {str(e)}")
    else:
        print("❌ 名称缓存文件不存在")

def main():
    """主函数"""
    print("🔧 股票数据采集问题诊断")
    print("📅 诊断时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("=" * 50)
    
    # 检查当前数据
    check_current_data()
    
    # 检查名称缓存
    check_name_cache()
    
    # 分析提取逻辑
    analyze_extraction_logic()
    
    # 测试网站访问（可选）
    try:
        choice = input("\n❓ 是否测试网站访问？(y/n): ").strip().lower()
        if choice == 'y':
            test_website_access()
    except KeyboardInterrupt:
        print("\n👋 诊断已取消")
    
    print(f"\n📋 诊断总结:")
    print(f"=" * 30)
    print(f"💡 可能的问题原因:")
    print(f"  1. 网站结构发生变化，导致提取逻辑失效")
    print(f"  2. 当前使用的是测试数据，不是真实采集数据")
    print(f"  3. 采集过程中网络问题或页面加载不完整")
    print(f"  4. 股票名称缓存问题，导致显示错误名称")
    
    print(f"\n🔧 建议解决方案:")
    print(f"  1. 重新运行后台采集获取最新数据")
    print(f"  2. 检查网站是否正常访问")
    print(f"  3. 更新股票名称缓存")
    print(f"  4. 调试采集逻辑，确保提取正确信息")

if __name__ == "__main__":
    main()

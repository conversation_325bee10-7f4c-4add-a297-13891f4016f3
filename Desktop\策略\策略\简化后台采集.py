#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版后台采集脚本
用于测试热门股选股功能，避免Unicode编码问题
"""

import csv
import random
import time
from datetime import datetime

def generate_test_data():
    """生成测试用的热门股数据"""
    print("生成测试热门股数据...")

    # 基础股票数据 - 扩展到100只真实股票
    base_stocks = [
        ["000001", "平安银行"], ["000002", "万科A"], ["000063", "中兴通讯"],
        ["000100", "TCL科技"], ["000166", "申万宏源"], ["000568", "泸州老窖"],
        ["000661", "长春高新"], ["000725", "京东方A"], ["000776", "广发证券"],
        ["000858", "五粮液"], ["002008", "大族激光"], ["002049", "紫光国微"],
        ["002097", "山河智能"], ["002142", "宁波银行"], ["002230", "科大讯飞"],
        ["002304", "洋河股份"], ["002371", "北方华创"], ["002415", "海康威视"],
        ["002460", "赣锋锂业"], ["002475", "立讯精密"], ["002594", "比亚迪"],
        ["002812", "恩捷股份"], ["002821", "凯莱英"], ["300003", "乐普医疗"],
        ["300015", "爱尔眼科"], ["300059", "东方财富"], ["300122", "智飞生物"],
        ["300124", "汇川技术"], ["300142", "沃森生物"], ["300149", "量子生物"],
        ["300274", "阳光电源"], ["300316", "晶盛机电"], ["300347", "泰格医药"],
        ["300408", "三环集团"], ["300454", "深信服"], ["300529", "健帆生物"],
        ["300595", "欧普康视"], ["300601", "康泰生物"], ["300628", "亿联网络"],
        ["300676", "华大基因"], ["300750", "宁德时代"], ["300760", "迈瑞医疗"],
        ["300782", "卓胜微"], ["300896", "爱美客"], ["600000", "浦发银行"],
        ["600010", "包钢股份"], ["600036", "招商银行"], ["600117", "西宁特钢"],
        ["600519", "贵州茅台"], ["600570", "恒生电子"], ["600887", "伊利股份"],
        ["603259", "药明康德"], ["688012", "中微公司"], ["688036", "传音控股"],
        ["688599", "天合光能"], ["688981", "中芯国际"], ["000006", "深振业A"],
        ["000009", "中国宝安"], ["000012", "南玻A"], ["000021", "深科技"],
        ["000027", "深圳能源"], ["000039", "中集集团"], ["000046", "泛海控股"],
        ["000050", "深天马A"], ["000060", "中金岭南"], ["000069", "华侨城A"],
        ["000078", "海王生物"], ["000089", "深圳机场"], ["000157", "中联重科"],
        ["000338", "潍柴动力"], ["000425", "徐工机械"], ["000538", "云南白药"],
        ["000559", "万向钱潮"], ["000596", "古井贡酒"], ["000623", "吉林敖东"],
        ["000651", "格力电器"], ["000703", "恒逸石化"], ["000768", "中航西飞"],
        ["000783", "长江证券"], ["000792", "盐湖股份"], ["000839", "中信证券"],
        ["000876", "新希望"], ["000895", "双汇发展"], ["000938", "紫光股份"],
        ["000977", "浪潮信息"], ["002001", "新和成"], ["002007", "华兰生物"],
        ["002027", "分众传媒"], ["002050", "三花智控"], ["002120", "韵达股份"],
        ["002129", "中环股份"], ["002202", "金风科技"], ["002236", "大华股份"],
        ["002241", "歌尔股份"], ["002252", "上海莱士"], ["002311", "海大集团"],
        ["002352", "顺丰控股"], ["002410", "广联达"], ["002456", "欧菲光"],
        ["002466", "天齐锂业"], ["002493", "荣盛石化"], ["002508", "老板电器"],
        ["002555", "三七互娱"], ["002601", "龙佰集团"], ["002714", "牧原股份"],
        ["002739", "万达电影"], ["002841", "视源股份"], ["002916", "深南电路"],
        ["300033", "同花顺"], ["300070", "碧水源"], ["300144", "宋城演艺"],
        ["300253", "卫宁健康"], ["300285", "国瓷材料"], ["300308", "中际旭创"],
        ["300413", "芒果超媒"], ["300498", "温氏股份"], ["300558", "贝达药业"],
        ["300618", "寒锐钴业"], ["300661", "圣邦股份"], ["300724", "捷佳伟创"]
    ]

    # 生成100只股票数据 - 现在有足够的真实股票数据
    test_stocks = []
    for i in range(min(100, len(base_stocks))):  # 确保不超过基础数据数量
        code, name = base_stocks[i]

        # 随机生成价格和涨跌幅
        price = round(random.uniform(5.0, 500.0), 2)
        change = round(random.uniform(-10.0, 10.0), 2)
        change_str = f"{change:+.2f}%"

        test_stocks.append([code, name, f"{price:.2f}", change_str])

    # 随机打乱顺序
    random.shuffle(test_stocks)

    return test_stocks

def save_test_data(data, filename, tab_name):
    """保存测试数据到CSV文件"""
    try:
        print(f"保存{tab_name}数据到 {filename}...")
        
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入标题行
            writer.writerow(['股票代码', '股票名称', '当前价格', '涨跌幅'])
            
            # 写入数据
            writer.writerows(data)
        
        print(f"{filename} 已保存，共 {len(data)} 条数据")
        return True
        
    except Exception as e:
        print(f"保存 {filename} 失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("简化版股票数据采集系统")
    print("=" * 50)
    print("模拟模式：生成测试数据用于功能验证")
    print("=" * 50)
    
    try:
        # 生成人气榜测试数据
        print("\n第一步：生成人气榜数据")
        print("=" * 40)
        
        popularity_data = generate_test_data()
        save_test_data(popularity_data, "popularity.csv", "人气榜")
        
        # 模拟等待时间
        print("等待1秒...")
        time.sleep(1)
        
        # 生成飙升榜测试数据（稍微不同的数据）
        print("\n第二步：生成飙升榜数据")
        print("=" * 40)
        
        soaring_data = generate_test_data()
        # 修改一些价格和涨跌幅，模拟不同榜单
        for i in range(len(soaring_data)):
            if i < 10:  # 前10只股票修改数据
                price = float(soaring_data[i][2])
                change_val = random.uniform(-5, 8)
                soaring_data[i][2] = f"{price + random.uniform(-5, 5):.2f}"
                soaring_data[i][3] = f"{change_val:+.2f}%"
        
        save_test_data(soaring_data, "soaring.csv", "飙升榜")
        
        # 数据对比
        print("\n采集结果统计:")
        print(f"  人气榜: {len(popularity_data)} 只股票")
        print(f"  飙升榜: {len(soaring_data)} 只股票")
        
        # 检查重复股票
        pop_codes = set(item[0] for item in popularity_data)
        soar_codes = set(item[0] for item in soaring_data)
        common_codes = pop_codes & soar_codes
        print(f"  重复股票: {len(common_codes)} 只")
        
        print("\n采集任务完成！")
        print("生成的文件: popularity.csv, soaring.csv")
        print("您可以打开CSV文件查看采集结果")
        
        return True
        
    except Exception as e:
        print(f"采集出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n测试数据生成成功！")
    else:
        print("\n测试数据生成失败！")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
一键启动所有功能
"""

import os
import sys
import subprocess
import webbrowser
from datetime import datetime

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🚀 股票数据采集与分析系统 - 快速启动")
    print("=" * 60)
    print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def show_menu():
    """显示菜单"""
    print("📋 功能菜单:")
    print("  1. 🔥 启动可视化主程序 (GUI界面)")
    print("  2. 🚀 运行后台数据采集")
    print("  3. 📊 生成智能数据分析报告")
    print("  4. 🎨 显示增强数据报告 (股票+板块)")
    print("  5. 🌐 打开所有HTML报告")
    print("  6. 🧪 运行系统诊断")
    print("  7. 🔄 刷新所有数据")
    print("  0. 👋 退出")
    print()

def run_script(script_name, description, wait=False):
    """运行脚本"""
    try:
        if not os.path.exists(script_name):
            print(f"❌ 脚本不存在: {script_name}")
            return False
        
        print(f"🚀 启动 {description}...")
        
        if wait:
            # 等待执行完成
            result = subprocess.run([sys.executable, script_name], 
                                  capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                print(f"✅ {description} 执行完成")
                return True
            else:
                print(f"❌ {description} 执行失败")
                if result.stderr:
                    print(f"错误信息: {result.stderr[:200]}")
                return False
        else:
            # 后台运行
            subprocess.Popen([sys.executable, script_name])
            print(f"✅ {description} 已启动")
            return True
            
    except Exception as e:
        print(f"❌ 启动 {description} 失败: {str(e)}")
        return False

def open_html_reports():
    """打开所有HTML报告"""
    reports = [
        ('股票分析报告.html', '智能数据分析报告'),
        ('股票板块整合报告.html', '股票板块整合报告'),
        ('股票分析报告_图表版.html', '图表版分析报告'),
        ('测试报告.html', '测试报告'),
        ('可视化界面_实时版.html', '实时Web界面')
    ]
    
    print("🌐 打开HTML报告...")
    opened_count = 0
    
    for filename, description in reports:
        if os.path.exists(filename):
            try:
                abs_path = os.path.abspath(filename)
                if os.name == 'nt':  # Windows
                    url = f"file:///{abs_path.replace(os.sep, '/')}"
                else:
                    url = f"file://{abs_path}"
                
                webbrowser.open(url)
                print(f"  ✅ {description}")
                opened_count += 1
            except Exception as e:
                print(f"  ❌ {description}: {str(e)}")
        else:
            print(f"  ⚠️ {description}: 文件不存在")
    
    print(f"📊 共打开 {opened_count} 个报告")

def refresh_all_data():
    """刷新所有数据"""
    print("🔄 刷新所有数据...")
    
    steps = [
        ('后台采集.py', '数据采集', True),
        ('智能数据分析报告.py', '分析报告生成', True),
        ('增强数据显示.py', '增强报告生成', True)
    ]
    
    success_count = 0
    for script, desc, wait in steps:
        if run_script(script, desc, wait):
            success_count += 1
    
    print(f"✅ 数据刷新完成，成功执行 {success_count}/{len(steps)} 个步骤")

def run_diagnostics():
    """运行系统诊断"""
    print("🧪 运行系统诊断...")
    
    diagnostic_scripts = [
        ('快速诊断.py', '快速诊断'),
        ('验证刷新频率.py', '刷新频率验证'),
        ('检查数据文件.py', '数据文件检查')
    ]
    
    for script, desc in diagnostic_scripts:
        if os.path.exists(script):
            run_script(script, desc, True)
        else:
            print(f"⚠️ {desc} 脚本不存在: {script}")

def main():
    """主函数"""
    print_banner()
    
    while True:
        show_menu()
        
        try:
            choice = input("请选择功能 (0-7): ").strip()
            print()
            
            if choice == '0':
                print("👋 感谢使用股票数据采集与分析系统！")
                break
            elif choice == '1':
                run_script('可视化主程序.py', 'GUI可视化界面')
            elif choice == '2':
                run_script('后台采集.py', '后台数据采集', True)
            elif choice == '3':
                run_script('智能数据分析报告.py', '智能数据分析报告', True)
            elif choice == '4':
                run_script('增强数据显示.py', '增强数据显示', True)
            elif choice == '5':
                open_html_reports()
            elif choice == '6':
                run_diagnostics()
            elif choice == '7':
                refresh_all_data()
            else:
                print("❌ 无效选择，请输入 0-7")
            
            print()
            input("按回车键继续...")
            print()
            
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            break
        except Exception as e:
            print(f"❌ 操作失败: {str(e)}")

if __name__ == "__main__":
    main()

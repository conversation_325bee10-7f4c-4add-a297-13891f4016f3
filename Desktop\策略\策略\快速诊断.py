#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速诊断采集问题
"""

import csv
import os
import re
import json

def quick_diagnosis():
    """快速诊断"""
    print("🔧 快速诊断采集问题")
    print("=" * 40)
    
    # 检查当前数据
    print("\n📊 检查当前数据:")
    
    files = ['popularity.csv', 'soaring.csv']
    for filename in files:
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    data = list(reader)
                
                print(f"\n{filename}:")
                print(f"  记录数: {len(data)}")
                
                if data:
                    real_stocks = 0
                    board_like = 0
                    
                    print("  前5条:")
                    for i, stock in enumerate(data[:5], 1):
                        code = stock.get('code', '')
                        name = stock.get('name', '')
                        
                        # 检查是否是真实股票代码
                        is_real_code = bool(re.match(r'^[036]\d{5}$', code))
                        if is_real_code:
                            real_stocks += 1
                        else:
                            board_like += 1
                        
                        status = "✅股票" if is_real_code else "❌板块"
                        print(f"    {i}. {code} {name} ({status})")
                    
                    print(f"  统计: 真实股票 {real_stocks}/5, 疑似板块 {board_like}/5")
                    
            except Exception as e:
                print(f"  ❌ 读取失败: {str(e)}")
        else:
            print(f"\n❌ {filename} 不存在")
    
    # 检查名称缓存
    print(f"\n🏷️ 检查名称缓存:")
    if os.path.exists('stock_names_cache.json'):
        try:
            with open('stock_names_cache.json', 'r', encoding='utf-8') as f:
                cache = json.load(f)
                names = cache.get('names', {})
            
            print(f"  缓存记录: {len(names)} 个")
            
            # 检查示例
            samples = ['600036', '000001', '300015']
            for code in samples:
                name = names.get(code, '未找到')
                print(f"  {code}: {name}")
                
        except Exception as e:
            print(f"  ❌ 缓存读取失败: {str(e)}")
    else:
        print("  ❌ 缓存文件不存在")
    
    # 问题分析
    print(f"\n🔍 问题分析:")
    print(f"  当前数据文件中的名称看起来像板块名称")
    print(f"  这可能是因为:")
    print(f"  1. 使用的是测试数据（我们之前生成的）")
    print(f"  2. 真实采集时网站结构变化")
    print(f"  3. 股票名称缓存问题")
    
    print(f"\n💡 解决方案:")
    print(f"  1. 重新运行真实的后台采集")
    print(f"  2. 检查采集网站是否正常")
    print(f"  3. 更新股票名称获取逻辑")

if __name__ == "__main__":
    quick_diagnosis()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强数据显示系统
包含股票和板块数据的整齐显示
"""

import csv
import os
import json
from datetime import datetime
import requests
import re
from 行业板块分类 import IndustryClassifier

class EnhancedDataDisplay:
    def __init__(self):
        self.stock_data = []
        self.sector_data = []
        self.industry_classifier = IndustryClassifier()
        self.load_data()
    
    def load_data(self):
        """加载股票和板块数据"""
        print("📊 加载数据...")
        
        # 加载股票数据
        self.load_stock_data()
        
        # 获取板块数据
        self.get_sector_data()
    
    def load_stock_data(self):
        """加载股票数据"""
        files = ['popularity.csv', 'soaring.csv']
        
        for filename in files:
            if os.path.exists(filename):
                try:
                    with open(filename, 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        data = list(reader)
                    
                    for stock in data:
                        stock['source'] = '人气榜' if 'popularity' in filename else '飙升榜'
                        # 添加行业信息
                        stock['industry'] = self.industry_classifier.get_industry(
                            stock.get('code', ''),
                            stock.get('name', '')
                        )
                        self.stock_data.append(stock)
                    
                    print(f"✅ {filename}: {len(data)} 条股票数据")
                except Exception as e:
                    print(f"❌ 加载 {filename} 失败: {str(e)}")
    
    def get_stock_sector(self, code):
        """获取股票所属板块"""
        # 简单的板块分类逻辑
        if code.startswith('60'):
            if code.startswith('600'):
                return '沪市主板'
            elif code.startswith('601'):
                return '沪市大盘'
            elif code.startswith('603'):
                return '沪市中盘'
            else:
                return '沪市其他'
        elif code.startswith('00'):
            if code.startswith('000'):
                return '深市主板'
            elif code.startswith('002'):
                return '中小板'
            else:
                return '深市其他'
        elif code.startswith('30'):
            return '创业板'
        elif code.startswith('68'):
            return '科创板'
        elif code.startswith('8'):
            return '北交所'
        else:
            return '其他'
    
    def get_sector_data(self):
        """获取行业数据（基于实际股票数据统计）"""
        # 统计各行业的股票数量和表现
        industry_stats = {}

        for stock in self.stock_data:
            industry = stock.get('industry', '其他')
            change_str = stock.get('change', '0%')

            if industry not in industry_stats:
                industry_stats[industry] = {
                    'stocks': [],
                    'changes': [],
                    'count': 0
                }

            industry_stats[industry]['stocks'].append(stock)
            industry_stats[industry]['count'] += 1

            # 解析涨跌幅
            try:
                change_val = self.parse_change(change_str)
                industry_stats[industry]['changes'].append(change_val)
            except:
                pass

        # 生成行业数据
        self.sector_data = []
        for industry, stats in industry_stats.items():
            if stats['count'] > 0:
                # 计算平均涨跌幅
                if stats['changes']:
                    avg_change = sum(stats['changes']) / len(stats['changes'])
                    change_str = f"+{avg_change:.2f}%" if avg_change >= 0 else f"{avg_change:.2f}%"
                else:
                    change_str = "0.00%"

                # 找出表现最好的股票作为龙头
                best_stock = max(stats['stocks'],
                               key=lambda x: self.parse_change(x.get('change', '0%')))

                self.sector_data.append({
                    'name': industry,
                    'change': change_str,
                    'leader': best_stock.get('name', '未知'),
                    'count': stats['count']
                })

        # 按股票数量排序
        self.sector_data.sort(key=lambda x: x['count'], reverse=True)
        print(f"✅ 行业数据: {len(self.sector_data)} 个行业")
    
    def display_integrated_data(self):
        """显示整合的股票和板块数据"""
        print("\n" + "="*80)
        print("🚀 股票与行业数据整合显示")
        print("="*80)

        # 显示行业概览
        self.display_sector_overview()

        # 显示股票详细数据
        self.display_stock_details()

        # 显示行业排行
        self.display_sector_ranking()
    
    def display_sector_overview(self):
        """显示行业概览"""
        print(f"\n📊 行业概览 ({len(self.sector_data)} 个行业)")
        print("-" * 80)
        print(f"{'行业名称':<12} {'涨跌幅':<10} {'龙头股票':<12} {'成分股数':<8} {'市场表现'}")
        print("-" * 80)
        
        for sector in self.sector_data:
            name = sector['name']
            change = sector['change']
            leader = sector['leader']
            count = sector['count']
            
            # 判断表现
            if change.startswith('+'):
                performance = "🔥 强势"
            elif change.startswith('-'):
                performance = "❄️ 弱势"
            else:
                performance = "➖ 平稳"
            
            print(f"{name:<12} {change:<10} {leader:<12} {count:<8} {performance}")
    
    def display_stock_details(self):
        """显示股票详细数据"""
        print(f"\n📈 股票详细数据 ({len(self.stock_data)} 只股票)")
        print("-" * 100)
        print(f"{'序号':<4} {'代码':<8} {'名称':<12} {'价格':<8} {'涨跌幅':<10} {'行业':<10} {'来源':<8} {'表现'}")
        print("-" * 100)
        
        # 按涨跌幅排序
        sorted_stocks = sorted(self.stock_data, key=lambda x: self.parse_change(x.get('change', '0%')), reverse=True)
        
        for i, stock in enumerate(sorted_stocks[:50], 1):  # 显示前50只
            code = stock.get('code', 'N/A')
            name = stock.get('name', '未知')[:10]  # 限制名称长度
            price = stock.get('price', '--')
            change = stock.get('change', 'N/A')
            industry = stock.get('industry', '未知')
            source = stock.get('source', '未知')
            
            # 格式化价格
            if price and price != '--':
                try:
                    price_val = float(price)
                    price_display = f"¥{price_val:.2f}"
                except:
                    price_display = f"¥{price}"
            else:
                price_display = "¥--"
            
            # 判断表现
            change_val = self.parse_change(change)
            if change_val >= 5:
                performance = "🚀 暴涨"
            elif change_val >= 2:
                performance = "📈 上涨"
            elif change_val >= 0:
                performance = "🔼 微涨"
            elif change_val >= -2:
                performance = "🔽 微跌"
            elif change_val >= -5:
                performance = "📉 下跌"
            else:
                performance = "💥 暴跌"
            
            print(f"{i:<4} {code:<8} {name:<12} {price_display:<8} {change:<10} {industry:<10} {source:<8} {performance}")
        
        if len(sorted_stocks) > 50:
            print(f"... 还有 {len(sorted_stocks) - 50} 只股票")
    
    def display_sector_ranking(self):
        """显示行业排行"""
        print(f"\n🏆 行业涨跌排行")
        print("-" * 60)

        # 按涨跌幅排序行业
        sorted_sectors = sorted(self.sector_data, key=lambda x: self.parse_change(x['change']), reverse=True)

        print("📈 涨幅榜:")
        for i, sector in enumerate(sorted_sectors[:8], 1):  # 显示前8个
            change_val = self.parse_change(sector['change'])
            if change_val > 0:
                print(f"  {i}. {sector['name']:<12} {sector['change']:<8} (龙头: {sector['leader']}) [{sector['count']}只]")

        print("\n📉 跌幅榜:")
        declining_sectors = [s for s in sorted_sectors if self.parse_change(s['change']) < 0]
        for i, sector in enumerate(declining_sectors[:5], 1):
            print(f"  {i}. {sector['name']:<12} {sector['change']:<8} (龙头: {sector['leader']}) [{sector['count']}只]")
    
    def parse_change(self, change_str):
        """解析涨跌幅字符串为数值"""
        try:
            # 移除 + 号和 % 号，转换为浮点数
            clean_str = change_str.replace('+', '').replace('%', '').strip()
            return float(clean_str)
        except:
            return 0.0
    
    def generate_html_report(self):
        """生成HTML报告"""
        print(f"\n📄 生成HTML报告...")
        
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票与行业数据整合报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .content {{
            padding: 30px;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background: #34495e;
            color: white;
            font-weight: bold;
        }}
        tr:hover {{
            background: #f8f9fa;
        }}
        .positive {{ color: #e74c3c; font-weight: bold; }}
        .negative {{ color: #27ae60; font-weight: bold; }}
        .sector-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .sector-card {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }}
        .sector-card h3 {{
            margin: 0 0 10px 0;
            color: #2c3e50;
        }}
        .sector-change {{
            font-size: 1.2em;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 股票与行业数据整合报告</h1>
            <p>📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📊 行业概览</h2>
                <div class="sector-grid">
        """
        
        # 添加板块卡片
        for sector in self.sector_data:
            change_class = "positive" if sector['change'].startswith('+') else "negative"
            html_content += f"""
                    <div class="sector-card">
                        <h3>{sector['name']}</h3>
                        <div class="sector-change {change_class}">{sector['change']}</div>
                        <p>龙头: {sector['leader']}</p>
                        <p>成分股: {sector['count']} 只</p>
                    </div>
            """
        
        html_content += """
                </div>
            </div>
            
            <div class="section">
                <h2>📈 股票详细数据</h2>
                <table>
                    <tr>
                        <th>序号</th>
                        <th>股票代码</th>
                        <th>股票名称</th>
                        <th>价格</th>
                        <th>涨跌幅</th>
                        <th>所属行业</th>
                        <th>数据来源</th>
                    </tr>
        """
        
        # 添加股票数据
        sorted_stocks = sorted(self.stock_data, key=lambda x: self.parse_change(x.get('change', '0%')), reverse=True)
        
        for i, stock in enumerate(sorted_stocks[:100], 1):  # 显示前100只
            code = stock.get('code', 'N/A')
            name = stock.get('name', '未知')
            price = stock.get('price', '--')
            change = stock.get('change', 'N/A')
            industry = stock.get('industry', '未知')
            source = stock.get('source', '未知')
            
            # 格式化价格
            if price and price != '--':
                try:
                    price_val = float(price)
                    price_display = f"¥{price_val:.2f}"
                except:
                    price_display = f"¥{price}"
            else:
                price_display = "¥--"
            
            change_class = "positive" if change.startswith('+') else "negative" if change.startswith('-') else ""
            
            html_content += f"""
                    <tr>
                        <td>{i}</td>
                        <td>{code}</td>
                        <td>{name}</td>
                        <td>{price_display}</td>
                        <td class="{change_class}">{change}</td>
                        <td>{industry}</td>
                        <td>{source}</td>
                    </tr>
            """
        
        html_content += """
                </table>
            </div>
        </div>
    </div>
</body>
</html>
        """
        
        # 保存HTML文件
        try:
            with open('股票行业整合报告.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print("✅ HTML报告已生成: 股票行业整合报告.html")
            return True
        except Exception as e:
            print(f"❌ HTML报告生成失败: {str(e)}")
            return False

def main():
    """主函数"""
    print("🚀 增强数据显示系统")
    print("=" * 50)
    
    # 创建显示系统
    display = EnhancedDataDisplay()
    
    # 显示整合数据
    display.display_integrated_data()
    
    # 生成HTML报告
    display.generate_html_report()
    
    print(f"\n🎉 数据显示完成！")
    print(f"💡 特色功能:")
    print(f"  • 股票与行业数据整合显示")
    print(f"  • 数据排列整齐，易于阅读")
    print(f"  • 包含价格、涨跌幅、行业信息")
    print(f"  • 智能行业分类，基于股票名称和代码")
    print(f"  • 生成美观的HTML报告")

if __name__ == "__main__":
    main()

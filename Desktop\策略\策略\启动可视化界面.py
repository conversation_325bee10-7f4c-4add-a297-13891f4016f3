#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据采集系统 - 可视化界面启动器
支持多种可视化方式：HTML、Tkinter、Streamlit
"""

import os
import sys
import subprocess
import webbrowser
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    📊 股票数据采集系统                        ║
    ║                   可视化界面启动器 v1.0                       ║
    ║                                                              ║
    ║              智能化股票数据采集与分析平台                      ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查系统依赖...")
    
    required_packages = {
        'tkinter': '内置包',
        'matplotlib': 'pip install matplotlib',
        'pandas': 'pip install pandas',
        'streamlit': 'pip install streamlit',
        'plotly': 'pip install plotly'
    }
    
    missing_packages = []
    
    for package, install_cmd in required_packages.items():
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'matplotlib':
                import matplotlib
            elif package == 'pandas':
                import pandas
            elif package == 'streamlit':
                import streamlit
            elif package == 'plotly':
                import plotly
            
            print(f"  ✅ {package} - 已安装")
            
        except ImportError:
            print(f"  ❌ {package} - 未安装")
            missing_packages.append((package, install_cmd))
    
    if missing_packages:
        print("\n⚠️  缺少以下依赖包:")
        for package, install_cmd in missing_packages:
            print(f"   {package}: {install_cmd}")
        print("\n请先安装缺少的依赖包，然后重新运行此脚本。")
        return False
    
    print("✅ 所有依赖包检查完成！\n")
    return True

def launch_html_interface():
    """启动HTML界面"""
    print("🌐 启动HTML可视化界面...")
    
    html_file = Path("可视化界面.html")
    if html_file.exists():
        file_url = f"file:///{html_file.absolute().as_posix()}"
        webbrowser.open(file_url)
        print(f"✅ HTML界面已在浏览器中打开: {file_url}")
    else:
        print("❌ 找不到HTML界面文件: 可视化界面.html")

def launch_tkinter_interface():
    """启动Tkinter界面"""
    print("🖥️  启动Tkinter桌面应用...")
    
    try:
        subprocess.run([sys.executable, "可视化仪表板.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Tkinter界面启动失败: {e}")
    except FileNotFoundError:
        print("❌ 找不到Tkinter界面文件: 可视化仪表板.py")

def launch_streamlit_interface():
    """启动Streamlit界面"""
    print("🚀 启动Streamlit Web应用...")
    
    try:
        # 检查streamlit是否安装
        import streamlit
        
        # 启动streamlit应用
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "streamlit_可视化.py",
            "--server.port", "8501",
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ], check=True)
        
    except ImportError:
        print("❌ Streamlit未安装，请运行: pip install streamlit")
    except subprocess.CalledProcessError as e:
        print(f"❌ Streamlit界面启动失败: {e}")
    except FileNotFoundError:
        print("❌ 找不到Streamlit界面文件: streamlit_可视化.py")

def show_menu():
    """显示菜单"""
    menu = """
    🎮 请选择要启动的可视化界面:
    
    1. 🌐 HTML静态界面    - 轻量级，无需额外依赖
    2. 🖥️  Tkinter桌面应用 - 原生桌面应用，功能完整
    3. 🚀 Streamlit Web应用 - 现代化Web界面，交互性强
    4. 🔍 检查系统状态    - 查看数据文件和系统信息
    5. 📊 运行数据采集    - 执行完整的数据采集流程
    6. ❌ 退出程序
    
    """
    print(menu)

def check_system_status():
    """检查系统状态"""
    print("🔍 检查系统状态...")
    print("=" * 50)
    
    # 检查数据文件
    data_files = [
        "codes.txt",
        "popularity.csv", 
        "soaring.csv",
        "stock_cache.json"
    ]
    
    print("📁 数据文件状态:")
    for file in data_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"  ✅ {file} ({size} bytes)")
        else:
            print(f"  ❌ {file} (不存在)")
    
    print("\n🐍 Python脚本文件:")
    script_files = [
        "全自动提取股票代码.py",
        "采集人气榜_改进版.py",
        "从codes.txt读取股票代码.py",
        "数据分析报告.py"
    ]
    
    for file in script_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (不存在)")
    
    print("\n🌐 可视化界面文件:")
    ui_files = [
        "可视化界面.html",
        "可视化仪表板.py", 
        "streamlit_可视化.py"
    ]
    
    for file in ui_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (不存在)")
    
    print("=" * 50)

def run_data_collection():
    """运行数据采集"""
    print("📊 开始执行完整的数据采集流程...")
    print("=" * 50)
    
    scripts = [
        ("全自动提取股票代码.py", "股票代码提取"),
        ("采集人气榜_改进版.py", "人气榜数据采集"),
        ("从codes.txt读取股票代码.py", "数据处理与缓存"),
        ("数据分析报告.py", "生成分析报告")
    ]
    
    for script, description in scripts:
        if os.path.exists(script):
            print(f"\n🚀 执行: {description}")
            try:
                result = subprocess.run([sys.executable, script], 
                                      capture_output=True, text=True, 
                                      encoding='utf-8')
                if result.returncode == 0:
                    print(f"✅ {description} 完成")
                else:
                    print(f"❌ {description} 失败: {result.stderr}")
            except Exception as e:
                print(f"❌ {description} 执行出错: {e}")
        else:
            print(f"❌ 找不到脚本文件: {script}")
    
    print("\n🎉 数据采集流程完成！")
    print("=" * 50)

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("请输入选项 (1-6): ").strip()
            
            if choice == "1":
                launch_html_interface()
            elif choice == "2":
                launch_tkinter_interface()
            elif choice == "3":
                launch_streamlit_interface()
            elif choice == "4":
                check_system_status()
            elif choice == "5":
                run_data_collection()
            elif choice == "6":
                print("👋 感谢使用股票数据采集系统！")
                break
            else:
                print("❌ 无效选项，请重新选择！")
            
            if choice in ["1", "2", "3"]:
                break
                
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，退出...")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析实时显示Web服务器
在Web页面上实时显示AI分析过程
"""

import http.server
import socketserver
import threading
import json
import time
import os
from datetime import datetime

class AIAnalysisWebServer:
    def __init__(self, port=8080):
        self.port = port
        self.server = None
        self.analysis_log = []
        self.is_running = False
        
    def add_log(self, message, level="INFO"):
        """添加分析日志"""
        log_entry = {
            'timestamp': datetime.now().strftime('%H:%M:%S'),
            'message': message,
            'level': level
        }
        self.analysis_log.append(log_entry)
        
        # 保持最新100条记录
        if len(self.analysis_log) > 100:
            self.analysis_log = self.analysis_log[-100:]
    
    def get_html_page(self):
        """生成HTML页面"""
        return f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 AI智能选股分析 - 实时监控</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }}
        
        .header h1 {{
            font-size: 2em;
            margin-bottom: 10px;
        }}
        
        .status {{
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }}
        
        .status-indicator {{
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }}
        
        .status-running {{
            background: #28a745;
        }}
        
        .status-idle {{
            background: #6c757d;
        }}
        
        @keyframes pulse {{
            0% {{ opacity: 1; }}
            50% {{ opacity: 0.5; }}
            100% {{ opacity: 1; }}
        }}
        
        .log-container {{
            height: 500px;
            overflow-y: auto;
            padding: 20px;
            background: #1e1e1e;
            color: #fff;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
        }}
        
        .log-entry {{
            margin-bottom: 8px;
            padding: 5px 10px;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }}
        
        .log-info {{
            background: rgba(52, 152, 219, 0.1);
            border-left-color: #3498db;
        }}
        
        .log-success {{
            background: rgba(39, 174, 96, 0.1);
            border-left-color: #27ae60;
        }}
        
        .log-warning {{
            background: rgba(243, 156, 18, 0.1);
            border-left-color: #f39c12;
        }}
        
        .log-error {{
            background: rgba(231, 76, 60, 0.1);
            border-left-color: #e74c3c;
        }}
        
        .timestamp {{
            color: #95a5a6;
            margin-right: 10px;
        }}
        
        .controls {{
            padding: 20px;
            background: #f8f9fa;
            text-align: center;
        }}
        
        .btn {{
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
        }}
        
        .btn:hover {{
            background: #2980b9;
        }}
        
        .btn-success {{
            background: #27ae60;
        }}
        
        .btn-success:hover {{
            background: #229954;
        }}
        
        .progress-bar {{
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }}
        
        .progress-fill {{
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
            animation: progress-animation 2s infinite;
        }}
        
        @keyframes progress-animation {{
            0% {{ background-position: 0% 50%; }}
            50% {{ background-position: 100% 50%; }}
            100% {{ background-position: 0% 50%; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI智能选股分析</h1>
            <p>实时监控分析过程</p>
        </div>
        
        <div class="status">
            <div>
                <span class="status-indicator status-idle" id="statusIndicator"></span>
                <span id="statusText">等待分析开始...</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
        
        <div class="log-container" id="logContainer">
            <div class="log-entry log-info">
                <span class="timestamp">{datetime.now().strftime('%H:%M:%S')}</span>
                <span>🤖 AI智能选股分析系统已启动</span>
            </div>
            <div class="log-entry log-info">
                <span class="timestamp">{datetime.now().strftime('%H:%M:%S')}</span>
                <span>📊 等待分析任务...</span>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="refreshLogs()">🔄 刷新日志</button>
            <button class="btn" onclick="clearLogs()">🗑️ 清空日志</button>
            <button class="btn btn-success" onclick="openResults()">📊 查看结果</button>
        </div>
    </div>
    
    <script>
        let isAnalyzing = false;
        let logCount = 0;
        
        function updateStatus(running) {{
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            const progressFill = document.getElementById('progressFill');
            
            if (running) {{
                indicator.className = 'status-indicator status-running';
                statusText.textContent = '🤖 AI分析进行中...';
                progressFill.style.width = '100%';
                isAnalyzing = true;
            }} else {{
                indicator.className = 'status-indicator status-idle';
                statusText.textContent = '⏸️ 分析已完成';
                progressFill.style.width = '0%';
                isAnalyzing = false;
            }}
        }}
        
        function addLogEntry(timestamp, message, level) {{
            const container = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${{level.toLowerCase()}}`;
            entry.innerHTML = `
                <span class="timestamp">${{timestamp}}</span>
                <span>${{message}}</span>
            `;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
            logCount++;
        }}
        
        function refreshLogs() {{
            fetch('/api/logs')
                .then(response => response.json())
                .then(data => {{
                    const container = document.getElementById('logContainer');
                    container.innerHTML = '';
                    
                    data.logs.forEach(log => {{
                        addLogEntry(log.timestamp, log.message, log.level);
                    }});
                    
                    updateStatus(data.is_running);
                }})
                .catch(error => {{
                    console.error('获取日志失败:', error);
                }});
        }}
        
        function clearLogs() {{
            fetch('/api/clear', {{method: 'POST'}})
                .then(() => {{
                    document.getElementById('logContainer').innerHTML = '';
                    logCount = 0;
                }});
        }}
        
        function openResults() {{
            window.open('AI智能选股分析报告.html', '_blank');
        }}
        
        // 自动刷新日志
        setInterval(refreshLogs, 2000);
        
        // 初始化
        refreshLogs();
    </script>
</body>
</html>
        """
    
    def start_server(self):
        """启动Web服务器"""
        try:
            class RequestHandler(http.server.SimpleHTTPRequestHandler):
                def __init__(self, *args, **kwargs):
                    self.server_instance = kwargs.pop('server_instance')
                    super().__init__(*args, **kwargs)
                
                def do_GET(self):
                    if self.path == '/':
                        self.send_response(200)
                        self.send_header('Content-type', 'text/html; charset=utf-8')
                        self.end_headers()
                        self.wfile.write(self.server_instance.get_html_page().encode('utf-8'))
                    elif self.path == '/api/logs':
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json; charset=utf-8')
                        self.end_headers()
                        response = {
                            'logs': self.server_instance.analysis_log,
                            'is_running': self.server_instance.is_running
                        }
                        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
                    else:
                        super().do_GET()
                
                def do_POST(self):
                    if self.path == '/api/clear':
                        self.server_instance.analysis_log.clear()
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        self.wfile.write(b'{"status": "ok"}')
                    else:
                        self.send_response(404)
                        self.end_headers()
                
                def log_message(self, format, *args):
                    # 禁用默认日志输出
                    pass
            
            # 创建服务器
            handler = lambda *args, **kwargs: RequestHandler(*args, server_instance=self, **kwargs)
            self.server = socketserver.TCPServer(("", self.port), handler)
            
            print(f"🌐 AI分析Web服务器启动: http://localhost:{self.port}")
            self.server.serve_forever()
            
        except Exception as e:
            print(f"❌ Web服务器启动失败: {str(e)}")
    
    def start_in_thread(self):
        """在线程中启动服务器"""
        thread = threading.Thread(target=self.start_server, daemon=True)
        thread.start()
        return thread
    
    def stop_server(self):
        """停止服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()

# 全局Web服务器实例
web_server = None

def start_ai_web_monitor():
    """启动AI分析Web监控"""
    global web_server
    
    if web_server is None:
        web_server = AIAnalysisWebServer(port=8080)
        web_server.start_in_thread()
        time.sleep(1)  # 等待服务器启动
    
    return web_server

def log_ai_analysis(message, level="INFO"):
    """记录AI分析日志"""
    global web_server
    
    if web_server:
        web_server.add_log(message, level)
        if level in ["SUCCESS", "ERROR"]:
            web_server.is_running = False
        else:
            web_server.is_running = True

if __name__ == "__main__":
    # 测试Web服务器
    server = start_ai_web_monitor()
    
    # 模拟分析过程
    import time
    
    log_ai_analysis("🤖 开始AI智能选股分析", "INFO")
    time.sleep(2)
    log_ai_analysis("📊 加载热门股数据: 30只", "INFO")
    time.sleep(1)
    log_ai_analysis("📰 加载消息面数据: 49只", "INFO")
    time.sleep(1)
    log_ai_analysis("🔄 合并股票数据: 79只", "INFO")
    time.sleep(2)
    log_ai_analysis("🤖 AI综合分析中...", "INFO")
    time.sleep(3)
    log_ai_analysis("✅ AI分析完成: 13只高分股票", "SUCCESS")
    
    print("Web服务器运行中，按Ctrl+C停止")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("停止Web服务器")
        server.stop_server()

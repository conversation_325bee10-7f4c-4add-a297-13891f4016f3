#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成完整的热门股数据
确保人气榜和飙升榜各有100只股票，包含完整信息
"""

import csv
import random
import requests
import json
from datetime import datetime

def get_real_stock_list():
    """获取真实的股票列表"""
    # 常见的A股股票代码和名称
    stocks = [
        ('000001', '平安银行'), ('000002', '万科A'), ('000858', '五粮液'), ('000876', '新希望'),
        ('000895', '双汇发展'), ('000938', '紫光股份'), ('000963', '华东医药'), ('001979', '招商蛇口'),
        ('002001', '新和成'), ('002007', '华兰生物'), ('002024', '苏宁易购'), ('002027', '分众传媒'),
        ('002050', '三花智控'), ('002142', '宁波银行'), ('002230', '科大讯飞'), ('002236', '大华股份'),
        ('002241', '歌尔股份'), ('002304', '洋河股份'), ('002311', '海大集团'), ('002352', '顺丰控股'),
        ('002415', '海康威视'), ('002460', '赣锋锂业'), ('002475', '立讯精密'), ('002493', '荣盛石化'),
        ('002508', '老板电器'), ('002555', '三七互娱'), ('002594', '比亚迪'), ('002601', '龙佰集团'),
        ('002714', '牧原股份'), ('002736', '国信证券'), ('002739', '万达电影'), ('002841', '视源股份'),
        ('300003', '乐普医疗'), ('300015', '爱尔眼科'), ('300033', '同花顺'), ('300059', '东方财富'),
        ('300122', '智飞生物'), ('300124', '汇川技术'), ('300142', '沃森生物'), ('300144', '宋城演艺'),
        ('300347', '泰格医药'), ('300408', '三环集团'), ('300413', '芒果超媒'), ('300450', '先导智能'),
        ('300498', '温氏股份'), ('300601', '康泰生物'), ('300628', '亿联网络'), ('300750', '宁德时代'),
        ('300760', '迈瑞医疗'), ('300896', '爱美客'), ('600000', '浦发银行'), ('600009', '上海机场'),
        ('600019', '宝钢股份'), ('600028', '中国石化'), ('600030', '中信证券'), ('600036', '招商银行'),
        ('600048', '保利发展'), ('600050', '中国联通'), ('600104', '上汽集团'), ('600111', '北方稀土'),
        ('600196', '复星医药'), ('600276', '恒瑞医药'), ('600309', '万华化学'), ('600346', '恒力石化'),
        ('600438', '通威股份'), ('600519', '贵州茅台'), ('600547', '山东黄金'), ('600570', '恒生电子'),
        ('600584', '长电科技'), ('600588', '用友网络'), ('600690', '海尔智家'), ('600703', '三安光电'),
        ('600745', '闻泰科技'), ('600760', '中航沈飞'), ('600809', '山西汾酒'), ('600837', '海通证券'),
        ('600887', '伊利股份'), ('600893', '航发动力'), ('600900', '长江电力'), ('600919', '江苏银行'),
        ('600941', '中国移动'), ('601012', '隆基绿能'), ('601066', '中信建投'), ('601088', '中国神华'),
        ('601138', '工业富联'), ('601166', '兴业银行'), ('601169', '北京银行'), ('601186', '中国铁建'),
        ('601211', '国泰君安'), ('601225', '陕西煤业'), ('601236', '红塔证券'), ('601288', '农业银行'),
        ('601318', '中国平安'), ('601319', '中国人保'), ('601328', '交通银行'), ('601336', '新华保险'),
        ('601398', '工商银行'), ('601601', '中国太保'), ('601628', '中国人寿'), ('601633', '长城汽车'),
        ('601668', '中国建筑'), ('601688', '华泰证券'), ('601728', '中国电信'), ('601766', '中国中车'),
        ('601818', '光大银行'), ('601857', '中国石油'), ('601888', '中国中免'), ('601899', '紫金矿业'),
        ('601919', '中远海控'), ('601939', '建设银行'), ('601985', '中国核电'), ('601995', '中金公司'),
        ('603259', '药明康德'), ('603288', '海天味业'), ('603501', '韦尔股份'), ('603799', '华友钴业'),
        ('603986', '兆易创新'), ('688009', '中国通号'), ('688012', '中微公司'), ('688036', '传音控股'),
        ('688111', '金山办公'), ('688122', '西部超导'), ('688169', '石头科技'), ('688187', '时代电气'),
        ('688223', '晶科能源'), ('688303', '大全能源'), ('688599', '天合光能')
    ]
    
    return stocks

def generate_price_and_change():
    """生成随机的股价和涨跌幅"""
    # 生成合理的股价范围
    price = round(random.uniform(5.0, 200.0), 2)
    
    # 生成涨跌幅 (-10% 到 +10%)
    change_percent = round(random.uniform(-10.0, 10.0), 2)
    
    return f"{price:.2f}", f"{change_percent:+.2f}%"

def generate_rank_change():
    """生成排名变化"""
    changes = ['↑1', '↑2', '↑3', '↑5', '↑8', '↓1', '↓2', '↓3', '↓5', '↓8', '-', 'new']
    weights = [15, 12, 10, 8, 5, 15, 12, 10, 8, 5, 15, 5]  # 权重分布
    return random.choices(changes, weights=weights)[0]

def generate_hot_stocks_data():
    """生成完整的热门股数据"""
    print("开始生成完整的热门股数据...")
    
    # 获取股票列表
    all_stocks = get_real_stock_list()
    
    # 确保有足够的股票
    if len(all_stocks) < 200:
        # 如果股票不够，复制一些并修改代码
        additional_stocks = []
        for i in range(200 - len(all_stocks)):
            code, name = all_stocks[i % len(all_stocks)]
            # 修改代码避免重复
            new_code = f"{int(code) + 100000}"[:6]
            additional_stocks.append((new_code, f"{name}B"))
        all_stocks.extend(additional_stocks)
    
    # 随机选择200只股票
    selected_stocks = random.sample(all_stocks, 200)
    
    # 分成两组：人气榜100只，飙升榜100只
    popularity_stocks = selected_stocks[:100]
    soaring_stocks = selected_stocks[100:200]
    
    # 生成人气榜数据
    print("生成人气榜数据...")
    popularity_data = []
    for i, (code, name) in enumerate(popularity_stocks, 1):
        price, change = generate_price_and_change()
        rank_change = generate_rank_change()
        
        popularity_data.append([
            code,           # 股票代码
            name,           # 股票名称
            price,          # 当前价格
            change,         # 涨跌幅
            str(i),         # 排名
            rank_change     # 较昨日排名
        ])
    
    # 保存人气榜数据
    with open('popularity.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['股票代码', '股票名称', '当前价格', '涨跌幅', '排名', '较昨日排名'])
        writer.writerows(popularity_data)
    
    print(f"✅ 人气榜数据已保存: {len(popularity_data)} 只股票")
    
    # 生成飙升榜数据
    print("生成飙升榜数据...")
    soaring_data = []
    for i, (code, name) in enumerate(soaring_stocks, 1):
        # 飙升榜的涨跌幅偏向正值
        price = round(random.uniform(5.0, 200.0), 2)
        change_percent = round(random.uniform(-2.0, 10.0), 2)  # 偏向上涨
        price_str = f"{price:.2f}"
        change_str = f"{change_percent:+.2f}%"
        rank_change = generate_rank_change()
        
        soaring_data.append([
            code,           # 股票代码
            name,           # 股票名称
            price_str,      # 当前价格
            change_str,     # 涨跌幅
            str(i),         # 排名
            rank_change     # 较昨日排名
        ])
    
    # 保存飙升榜数据
    with open('soaring.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['股票代码', '股票名称', '当前价格', '涨跌幅', '排名', '较昨日排名'])
        writer.writerows(soaring_data)
    
    print(f"✅ 飙升榜数据已保存: {len(soaring_data)} 只股票")
    
    # 生成统计信息
    stats = {
        'popularity_count': len(popularity_data),
        'soaring_count': len(soaring_data),
        'total_count': len(popularity_data) + len(soaring_data),
        'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    print(f"\n📊 数据生成完成:")
    print(f"  人气榜: {stats['popularity_count']} 只")
    print(f"  飙升榜: {stats['soaring_count']} 只")
    print(f"  总计: {stats['total_count']} 只")
    print(f"  生成时间: {stats['generation_time']}")
    
    return stats

def main():
    """主函数"""
    try:
        stats = generate_hot_stocks_data()
        print("\n🎉 完整热门股数据生成成功！")
        return True
    except Exception as e:
        print(f"\n❌ 数据生成失败: {str(e)}")
        return False

if __name__ == "__main__":
    main()

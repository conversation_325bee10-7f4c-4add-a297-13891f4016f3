#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选股报告生成器
生成美观的HTML选股报告
"""

import json
import os
from datetime import datetime

def generate_stock_selection_report():
    """生成股票选股HTML报告"""
    
    # 读取选股结果
    if not os.path.exists('选股结果.json'):
        print("❌ 选股结果文件不存在，请先运行智能选股系统")
        return False
    
    try:
        with open('选股结果.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"❌ 读取选股结果失败: {str(e)}")
        return False
    
    # 生成HTML内容
    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 智能选股分析报告</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }}
        
        .header::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }}
        
        .header p {{
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }}
        
        .content {{
            padding: 40px;
        }}
        
        .summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }}
        
        .summary-card {{
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(116, 185, 255, 0.3);
        }}
        
        .summary-card h3 {{
            font-size: 2.5em;
            margin-bottom: 10px;
        }}
        
        .summary-card p {{
            font-size: 1.1em;
            opacity: 0.9;
        }}
        
        .strategy-section {{
            margin-bottom: 50px;
        }}
        
        .strategy-title {{
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 15px 15px 0 0;
            font-size: 1.5em;
            font-weight: bold;
        }}
        
        .stock-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 0 0 15px 15px;
        }}
        
        .stock-card {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid #00b894;
        }}
        
        .stock-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }}
        
        .stock-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }}
        
        .stock-code {{
            font-size: 1.2em;
            font-weight: bold;
            color: #2d3436;
        }}
        
        .stock-change {{
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }}
        
        .positive {{
            background: #00b894;
            color: white;
        }}
        
        .negative {{
            background: #e17055;
            color: white;
        }}
        
        .stock-name {{
            font-size: 1.1em;
            color: #636e72;
            margin-bottom: 10px;
        }}
        
        .stock-details {{
            display: flex;
            justify-content: space-between;
            font-size: 0.9em;
            color: #74b9ff;
        }}
        
        .consensus-section {{
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-top: 40px;
        }}
        
        .consensus-title {{
            font-size: 2em;
            text-align: center;
            margin-bottom: 30px;
        }}
        
        .consensus-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }}
        
        .consensus-card {{
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255,255,255,0.2);
        }}
        
        .votes-badge {{
            background: #fdcb6e;
            color: #2d3436;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }}
        
        .footer {{
            background: #2d3436;
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .disclaimer {{
            background: #ffeaa7;
            color: #2d3436;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #fdcb6e;
        }}
        
        @media (max-width: 768px) {{
            .header h1 {{
                font-size: 2em;
            }}
            
            .content {{
                padding: 20px;
            }}
            
            .summary {{
                grid-template-columns: 1fr;
            }}
            
            .stock-grid {{
                grid-template-columns: 1fr;
            }}
            
            .consensus-grid {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 智能选股分析报告</h1>
            <p>📅 生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        </div>
        
        <div class="content">
            <div class="summary">
                <div class="summary-card">
                    <h3>{data['total_stocks']}</h3>
                    <p>分析股票总数</p>
                </div>
                <div class="summary-card">
                    <h3>{len(data['strategies'])}</h3>
                    <p>选股策略数量</p>
                </div>
                <div class="summary-card">
                    <h3>{sum(len(stocks) for stocks in data['strategies'].values())}</h3>
                    <p>推荐股票总数</p>
                </div>
            </div>
    """
    
    # 添加各策略的股票推荐
    strategy_names = {
        'momentum': '🚀 动量选股策略',
        'value': '💎 价值选股策略', 
        'growth': '🌱 成长选股策略',
        'balanced': '⚖️ 均衡选股策略',
        'hot_sector': '🔥 热门板块选股',
        'risk_control': '🛡️ 风险控制选股'
    }
    
    for strategy_key, stocks in data['strategies'].items():
        strategy_name = strategy_names.get(strategy_key, strategy_key)
        
        html_content += f"""
            <div class="strategy-section">
                <div class="strategy-title">
                    {strategy_name} ({len(stocks)} 只推荐股票)
                </div>
                <div class="stock-grid">
        """
        
        for stock in stocks:
            change = stock['change']
            change_class = 'positive' if change.startswith('+') else 'negative' if change.startswith('-') else 'positive'
            
            html_content += f"""
                    <div class="stock-card">
                        <div class="stock-header">
                            <div class="stock-code">{stock['code']}</div>
                            <div class="stock-change {change_class}">{change}</div>
                        </div>
                        <div class="stock-name">{stock['name']}</div>
                        <div class="stock-details">
                            <span>💰 ¥{stock['price']}</span>
                            <span>🏭 {stock['industry']}</span>
                            <span>📊 {stock['source']}</span>
                        </div>
                    </div>
            """
        
        html_content += """
                </div>
            </div>
        """
    
    # 计算共识推荐
    stock_votes = {}
    for strategy, stocks in data['strategies'].items():
        for stock in stocks:
            code = stock['code']
            if code not in stock_votes:
                stock_votes[code] = {'stock': stock, 'strategies': [], 'votes': 0}
            stock_votes[code]['strategies'].append(strategy_names.get(strategy, strategy))
            stock_votes[code]['votes'] += 1
    
    consensus_picks = sorted(stock_votes.values(), key=lambda x: x['votes'], reverse=True)
    consensus_picks = [item for item in consensus_picks if item['votes'] >= 2]  # 至少2个策略推荐
    
    # 添加共识推荐部分
    html_content += f"""
            <div class="consensus-section">
                <div class="consensus-title">
                    🏆 多策略共识推荐 ({len(consensus_picks)} 只股票)
                </div>
                <div class="consensus-grid">
    """
    
    for item in consensus_picks[:12]:  # 显示前12只
        stock = item['stock']
        votes = item['votes']
        strategies = ', '.join(item['strategies'][:3])  # 显示前3个策略
        
        change = stock['change']
        change_class = 'positive' if change.startswith('+') else 'negative' if change.startswith('-') else 'positive'
        
        html_content += f"""
                    <div class="consensus-card">
                        <div class="votes-badge">{votes} 个策略推荐</div>
                        <div class="stock-header">
                            <div class="stock-code">{stock['code']}</div>
                            <div class="stock-change {change_class}">{change}</div>
                        </div>
                        <div class="stock-name">{stock['name']}</div>
                        <div class="stock-details">
                            <span>💰 ¥{stock['price']}</span>
                            <span>🏭 {stock['industry']}</span>
                        </div>
                        <div style="margin-top: 10px; font-size: 0.9em; opacity: 0.8;">
                            推荐策略: {strategies}
                        </div>
                    </div>
        """
    
    html_content += """
                </div>
            </div>
            
            <div class="disclaimer">
                <h3>⚠️ 投资风险提示</h3>
                <p>本报告仅供参考，不构成投资建议。股市有风险，投资需谨慎。请根据自身风险承受能力和投资目标做出决策。建议分散投资，控制仓位，及时止损。</p>
            </div>
        </div>
        
        <div class="footer">
            <p>📊 智能选股系统 | 基于热门股票数据的多维度分析</p>
            <p>🤖 AI驱动的投资决策支持工具</p>
        </div>
    </div>
</body>
</html>
    """
    
    # 保存HTML文件
    try:
        with open('智能选股分析报告.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("✅ 智能选股分析报告已生成: 智能选股分析报告.html")
        return True
    except Exception as e:
        print(f"❌ 生成报告失败: {str(e)}")
        return False

def main():
    """主函数"""
    try:
        print("📋 选股报告生成器")
        print("=" * 40)
    except UnicodeEncodeError:
        print("选股报告生成器")
        print("=" * 40)
    
    success = generate_stock_selection_report()
    
    if success:
        try:
            print("🎉 选股报告生成完成！")
            print("💡 报告特色:")
            print("  • 美观的响应式设计")
            print("  • 多策略选股结果展示")
            print("  • 共识推荐股票突出显示")
            print("  • 详细的股票信息卡片")
            print("  • 投资风险提示")
        except UnicodeEncodeError:
            print("选股报告生成完成！")
            print("报告特色:")
            print("  • 美观的响应式设计")
            print("  • 多策略选股结果展示")
            print("  • 共识推荐股票突出显示")
            print("  • 详细的股票信息卡片")
            print("  • 投资风险提示")

if __name__ == "__main__":
    main()

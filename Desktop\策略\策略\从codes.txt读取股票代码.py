import os
import json
import datetime
import re

def read_stock_codes_from_file(file_path="codes.txt"):
    """
    从codes.txt文件读取股票代码
    
    Args:
        file_path: 股票代码文件路径
        
    Returns:
        格式化后的股票代码列表
    """
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取6位数字股票代码
        stock_codes = re.findall(r'(\d{6})', content)
        
        if not stock_codes:
            print(f"❌ 文件中未找到有效的股票代码")
            return []
        
        # 去重并格式化
        unique_codes = set()
        formatted_stocks = []
        
        for code in stock_codes:
            if code in unique_codes:
                continue
            unique_codes.add(code)
            
            # 根据代码前缀添加市场后缀
            if code.startswith('6'):
                formatted_code = f"{code}.SH"
            else:
                formatted_code = f"{code}.SZ"
            formatted_stocks.append(formatted_code)
        
        print(f"✅ 成功从 {file_path} 提取 {len(formatted_stocks)} 只股票")
        return formatted_stocks
        
    except Exception as e:
        print(f"❌ 读取股票代码失败: {e}")
        return []

def get_stock_list_with_cache(file_path="codes.txt", cache_file="stock_cache.json", cache_days=10):
    """
    获取股票列表，带缓存功能
    
    Args:
        file_path: 股票代码文件路径
        cache_file: 缓存文件路径
        cache_days: 缓存有效期（天）
        
    Returns:
        股票代码列表
    """
    today = datetime.date.today()
    
    # 尝试使用缓存
    try:
        if os.path.exists(cache_file):
            with open(cache_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                last_date = datetime.datetime.strptime(data["date"], "%Y-%m-%d").date()
                if (today - last_date).days < cache_days:
                    print(f"📋 使用缓存股票列表 ({len(data['stocks'])} 只)")
                    return data["stocks"]
    except Exception as e:
        print(f"⚠️ 读取缓存失败: {e}")
    
    # 重新读取股票代码
    print("📋 重新读取股票代码文件...")
    stocks = read_stock_codes_from_file(file_path)
    
    if stocks:
        # 保存缓存
        try:
            with open(cache_file, "w", encoding="utf-8") as f:
                json.dump({"date": today.strftime("%Y-%m-%d"), "stocks": stocks}, f, ensure_ascii=False)
            print(f"✅ 缓存已更新")
        except Exception as e:
            print(f"⚠️ 保存缓存失败: {e}")
    
    return stocks

def is_stock_code(code):
    """判断是否为有效的股票代码"""
    if not isinstance(code, str):
        return False
    
    # 提取股票代码（去掉可能的后缀）
    if '.' in code:
        code = code.split('.')[0]
    
    # 检查是否为6位数字
    if not re.match(r'^\d{6}$', code):
        return False
    
    # 检查市场前缀
    return (code.startswith('6') or 
            code.startswith('00') or 
            code.startswith('002') or 
            code.startswith('003') or 
            code.startswith('3'))

# 测试函数
if __name__ == "__main__":
    # 测试读取股票代码
    stocks = get_stock_list_with_cache()
    print(f"\n📊 股票代码统计:")
    print(f"总数: {len(stocks)}")
    
    # 按市场分类统计
    sh_count = len([s for s in stocks if s.endswith('.SH')])
    sz_count = len([s for s in stocks if s.endswith('.SZ')])
    print(f"沪市: {sh_count} 只")
    print(f"深市: {sz_count} 只")
    
    # 显示前10只股票
    print(f"\n📋 前10只股票:")
    for i, stock in enumerate(stocks[:10], 1):
        print(f"{i:2d}. {stock}")
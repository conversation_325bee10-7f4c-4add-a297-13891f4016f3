# 文件处理修复总结

## 问题描述

用户遇到了以下错误：
```
[ERROR] 加载下单时间间隔数据失败: [Errno 2] No such file or directory: 'order_interval.json'
```

## 根本原因

程序在尝试加载不存在的JSON文件时，没有进行适当的错误处理，导致抛出 `FileNotFoundError` 异常。

## 修复内容

### 1. 修复 `load_order_interval_data()` 函数

**修复前：**
```python
def load_order_interval_data():
    """加载下单时间间隔数据"""
    try:
        with open(ORDER_INTERVAL_FILE, "r", encoding="utf-8") as f:
            data = json.load(f)
            # ... 处理逻辑
            return result
    except Exception as e:
        log_message('ERROR', f"加载下单时间间隔数据失败: {e}")
        return {}
```

**修复后：**
```python
def load_order_interval_data():
    """加载下单时间间隔数据"""
    try:
        # 检查文件是否存在
        if not os.path.exists(ORDER_INTERVAL_FILE):
            print(f"下单时间间隔文件 {ORDER_INTERVAL_FILE} 不存在，将创建新文件")
            return {}
        
        with open(ORDER_INTERVAL_FILE, "r", encoding="utf-8") as f:
            data = json.load(f)
            # ... 处理逻辑
            return result
    except FileNotFoundError:
        print(f"下单时间间隔文件 {ORDER_INTERVAL_FILE} 不存在，将创建新文件")
        return {}
    except json.JSONDecodeError as e:
        print(f"下单时间间隔文件格式错误: {e}，将重新创建文件")
        return {}
    except Exception as e:
        log_message('ERROR', f"加载下单时间间隔数据失败: {e}")
        return {}
```

### 2. 修复 `save_order_interval_data()` 函数

**修复前：**
```python
def save_order_interval_data(order_times):
    """保存下单时间间隔数据"""
    try:
        # 转换datetime对象为字符串
        data = {}
        for stock_code, order_time in order_times.items():
            data[stock_code] = order_time.isoformat()
        
        with open(ORDER_INTERVAL_FILE, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        log_message('ERROR', f"保存下单时间间隔数据失败: {e}")
```

**修复后：**
```python
def save_order_interval_data(order_times):
    """保存下单时间间隔数据"""
    try:
        # 转换datetime对象为字符串
        data = {}
        for stock_code, order_time in order_times.items():
            data[stock_code] = order_time.isoformat()
        
        # 确保目录存在
        file_dir = os.path.dirname(ORDER_INTERVAL_FILE)
        if file_dir and not os.path.exists(file_dir):
            os.makedirs(file_dir)
        
        with open(ORDER_INTERVAL_FILE, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"下单时间间隔数据已保存到 {ORDER_INTERVAL_FILE}")
    except Exception as e:
        log_message('ERROR', f"保存下单时间间隔数据失败: {e}")
```

### 3. 修复 `load_set_from_file()` 函数

**修复前：**
```python
def load_set_from_file(filename):
    """从文件加载集合"""
    try:
        with open(filename, "r", encoding="utf-8") as f:
            return set(json.load(f))
    except Exception as e:
        log_message('WARNING', f"加载文件 {filename} 失败: {e}")
        return set()
```

**修复后：**
```python
def load_set_from_file(filename):
    """从文件加载集合"""
    try:
        # 检查文件是否存在
        if not os.path.exists(filename):
            print(f"文件 {filename} 不存在，将创建新文件")
            return set()
        
        with open(filename, "r", encoding="utf-8") as f:
            return set(json.load(f))
    except FileNotFoundError:
        print(f"文件 {filename} 不存在，将创建新文件")
        return set()
    except json.JSONDecodeError as e:
        print(f"文件 {filename} 格式错误: {e}，将重新创建文件")
        return set()
    except Exception as e:
        log_message('WARNING', f"加载文件 {filename} 失败: {e}")
        return set()
```

## 修复原理

### 1. 文件存在性检查
- 在打开文件前使用 `os.path.exists()` 检查文件是否存在
- 如果文件不存在，提供友好的提示信息并返回默认值

### 2. 异常类型细分
- 使用 `try-except` 分别捕获不同类型的异常：
  - `FileNotFoundError`：文件不存在
  - `json.JSONDecodeError`：JSON格式错误
  - `Exception`：其他未知错误

### 3. 目录创建
- 在保存文件时检查目录是否存在
- 如果目录不存在，自动创建目录

### 4. 友好提示
- 提供详细的错误信息，帮助用户理解问题
- 使用中文提示，提高用户体验

## 测试验证

创建了测试脚本 `策略/测试下单时间间隔修复.py`，验证修复效果：

### 测试结果
```
🎉 所有测试通过！下单时间间隔修复成功
💡 现在程序可以优雅地处理文件不存在的情况:
  ✅ 文件不存在时不会抛出异常
  ✅ 自动创建新文件
  ✅ 提供友好的错误信息
  ✅ 确保目录存在
```

## 影响范围

### 修复的文件
- `730开市重启加等待.py`：主策略文件
- 其他策略文件也需要类似的修复

### 涉及的功能
- 下单时间间隔管理
- 黑名单管理
- 统一待卖列表管理
- 其他JSON文件加载功能

## 后续建议

### 1. 统一文件处理模式
建议在其他策略文件中应用相同的修复模式，确保所有文件操作都有适当的错误处理。

### 2. 创建通用工具函数
可以考虑创建一个通用的文件加载函数，统一处理各种文件加载场景。

### 3. 监控和日志
建议添加更详细的日志记录，便于问题排查和系统监控。

## 总结

通过这次修复，程序现在能够：
1. **优雅处理文件不存在的情况**，不再抛出异常
2. **自动创建必要的文件和目录**
3. **提供友好的错误信息**
4. **保持系统稳定运行**

这大大提高了程序的健壮性和用户体验。 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能数据分析报告
生成股票数据的智能分析报告
"""

import csv
import os
import json
from datetime import datetime
from 行业板块分类 import IndustryClassifier

def load_stock_data():
    """加载股票数据"""
    stock_data = []
    
    files = ['popularity.csv', 'soaring.csv']
    for filename in files:
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    data = list(reader)
                
                for stock in data:
                    stock['source'] = '人气榜' if 'popularity' in filename else '飙升榜'
                    stock_data.append(stock)
                    
                print(f"✅ 加载 {filename}: {len(data)} 条数据")
            except Exception as e:
                print(f"❌ 加载 {filename} 失败: {str(e)}")
    
    return stock_data

def analyze_data(stock_data):
    """分析股票数据"""
    if not stock_data:
        return None
    
    # 基础统计
    total_stocks = len(stock_data)
    popularity_stocks = [s for s in stock_data if s['source'] == '人气榜']
    soaring_stocks = [s for s in stock_data if s['source'] == '飙升榜']
    
    # 涨跌分析
    rising_stocks = []
    falling_stocks = []
    
    for stock in stock_data:
        change_str = stock.get('change', '0%')
        try:
            change_val = float(change_str.replace('+', '').replace('%', '').strip())
            if change_val > 0:
                rising_stocks.append((stock, change_val))
            elif change_val < 0:
                falling_stocks.append((stock, change_val))
        except:
            pass
    
    # 排序
    rising_stocks.sort(key=lambda x: x[1], reverse=True)
    falling_stocks.sort(key=lambda x: x[1])
    
    # 行业分析
    industry_classifier = IndustryClassifier()
    industry_stats = {}
    
    for stock in stock_data:
        industry = industry_classifier.get_industry(
            stock.get('code', ''), 
            stock.get('name', '')
        )
        
        if industry not in industry_stats:
            industry_stats[industry] = {'count': 0, 'stocks': []}
        
        industry_stats[industry]['count'] += 1
        industry_stats[industry]['stocks'].append(stock)
    
    return {
        'total_stocks': total_stocks,
        'popularity_count': len(popularity_stocks),
        'soaring_count': len(soaring_stocks),
        'rising_stocks': rising_stocks,
        'falling_stocks': falling_stocks,
        'industry_stats': industry_stats
    }

def generate_html_report(analysis):
    """生成HTML报告"""
    if not analysis:
        print("❌ 没有数据可分析")
        return False
    
    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 股票数据智能分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .content {{
            padding: 30px;
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .stat-card {{
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        
        .section {{
            margin-bottom: 30px;
        }}
        
        .section h2 {{
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }}
        
        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        
        th {{
            background: #34495e;
            color: white;
        }}
        
        tr:hover {{
            background: #f8f9fa;
        }}
        
        .positive {{ color: #e74c3c; font-weight: bold; }}
        .negative {{ color: #27ae60; font-weight: bold; }}
        
        .industry-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }}
        
        .industry-card {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 股票数据智能分析报告</h1>
            <p>📅 生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        </div>
        
        <div class="content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{analysis['total_stocks']}</div>
                    <div>总股票数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{analysis['popularity_count']}</div>
                    <div>人气榜股票</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{analysis['soaring_count']}</div>
                    <div>飙升榜股票</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{len(analysis['rising_stocks'])}</div>
                    <div>上涨股票</div>
                </div>
            </div>
            
            <div class="section">
                <h2>📈 表现最佳股票</h2>
                <table>
                    <tr>
                        <th>排名</th>
                        <th>股票代码</th>
                        <th>股票名称</th>
                        <th>涨跌幅</th>
                        <th>价格</th>
                        <th>来源</th>
                    </tr>
    """
    
    # 添加上涨股票
    for i, (stock, change_val) in enumerate(analysis['rising_stocks'][:20], 1):
        change_class = "positive"
        html_content += f"""
                    <tr>
                        <td>{i}</td>
                        <td>{stock.get('code', 'N/A')}</td>
                        <td>{stock.get('name', 'N/A')}</td>
                        <td class="{change_class}">{stock.get('change', 'N/A')}</td>
                        <td>¥{stock.get('price', '--')}</td>
                        <td>{stock.get('source', 'N/A')}</td>
                    </tr>
        """
    
    html_content += """
                </table>
            </div>
            
            <div class="section">
                <h2>📉 表现最差股票</h2>
                <table>
                    <tr>
                        <th>排名</th>
                        <th>股票代码</th>
                        <th>股票名称</th>
                        <th>涨跌幅</th>
                        <th>价格</th>
                        <th>来源</th>
                    </tr>
    """
    
    # 添加下跌股票
    for i, (stock, change_val) in enumerate(analysis['falling_stocks'][:10], 1):
        change_class = "negative"
        html_content += f"""
                    <tr>
                        <td>{i}</td>
                        <td>{stock.get('code', 'N/A')}</td>
                        <td>{stock.get('name', 'N/A')}</td>
                        <td class="{change_class}">{stock.get('change', 'N/A')}</td>
                        <td>¥{stock.get('price', '--')}</td>
                        <td>{stock.get('source', 'N/A')}</td>
                    </tr>
        """
    
    html_content += """
                </table>
            </div>
            
            <div class="section">
                <h2>🏭 行业分布</h2>
                <div class="industry-grid">
    """
    
    # 添加行业统计
    sorted_industries = sorted(analysis['industry_stats'].items(), 
                             key=lambda x: x[1]['count'], reverse=True)
    
    for industry, stats in sorted_industries[:12]:
        html_content += f"""
                    <div class="industry-card">
                        <h3>{industry}</h3>
                        <p>股票数量: {stats['count']} 只</p>
                    </div>
        """
    
    html_content += """
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    """
    
    # 保存HTML文件
    try:
        with open('股票分析报告.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("✅ HTML报告已生成: 股票分析报告.html")
        return True
    except Exception as e:
        print(f"❌ 生成HTML报告失败: {str(e)}")
        return False

def main():
    """主函数"""
    try:
        print("📊 智能数据分析报告")
        print("=" * 40)
        
        # 加载数据
        print("📁 加载股票数据...")
        stock_data = load_stock_data()
        
        if not stock_data:
            print("❌ 没有找到股票数据文件")
            return
        
        # 分析数据
        print("🔍 分析股票数据...")
        analysis = analyze_data(stock_data)
        
        # 生成报告
        print("📋 生成HTML报告...")
        success = generate_html_report(analysis)
        
        if success:
            print("🎉 智能分析报告生成完成！")
            print("💡 报告文件: 股票分析报告.html")
        else:
            print("❌ 报告生成失败")
            
    except Exception as e:
        print(f"❌ 程序运行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

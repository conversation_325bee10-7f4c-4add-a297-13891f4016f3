#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据采集系统 - 简化可视化界面
使用Tkinter创建简洁的可视化界面，无需额外依赖
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import os
import csv
from datetime import datetime
import threading
import subprocess

class SimpleStockVisualization:
    def __init__(self, root):
        self.root = root
        self.root.title("📊 股票数据采集系统 - 简化可视化界面")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # 数据存储
        self.data = {
            'codes': [],
            'cached_stocks': [],
            'popularity': [],
            'soaring': []
        }
        
        self.create_interface()
        self.load_data()
        
    def create_interface(self):
        """创建界面"""
        # 标题栏
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="📊 股票数据采集系统", 
                              font=('Microsoft YaHei', 18, 'bold'), 
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # 主容器
        main_container = tk.Frame(self.root, bg='#f0f0f0')
        main_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 左侧面板
        left_panel = tk.Frame(main_container, bg='white', relief='raised', bd=2, width=300)
        left_panel.pack(side='left', fill='y', padx=(0, 5))
        left_panel.pack_propagate(False)
        
        self.create_control_panel(left_panel)
        self.create_stats_panel(left_panel)
        
        # 右侧面板
        right_panel = tk.Frame(main_container, bg='white', relief='raised', bd=2)
        right_panel.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        self.create_data_display(right_panel)
        self.create_log_panel(right_panel)
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = tk.LabelFrame(parent, text="🎮 控制面板", 
                                     font=('Microsoft YaHei', 12, 'bold'),
                                     bg='white', padx=10, pady=10)
        control_frame.pack(fill='x', padx=10, pady=10)
        
        # 按钮配置
        btn_config = {
            'font': ('Microsoft YaHei', 10),
            'width': 20,
            'pady': 8
        }
        
        # 采集按钮
        self.collect_btn = tk.Button(control_frame, text="🚀 开始采集", 
                                    bg='#007bff', fg='white',
                                    command=self.start_collection, **btn_config)
        self.collect_btn.pack(pady=5)
        
        # 处理数据按钮
        self.process_btn = tk.Button(control_frame, text="⚙️ 处理数据", 
                                    bg='#28a745', fg='white',
                                    command=self.process_data, **btn_config)
        self.process_btn.pack(pady=5)
        
        # 生成报告按钮
        self.report_btn = tk.Button(control_frame, text="📊 生成报告", 
                                   bg='#17a2b8', fg='white',
                                   command=self.generate_report, **btn_config)
        self.report_btn.pack(pady=5)
        
        # 刷新数据按钮
        self.refresh_btn = tk.Button(control_frame, text="🔄 刷新数据", 
                                    bg='#6c757d', fg='white',
                                    command=self.refresh_data, **btn_config)
        self.refresh_btn.pack(pady=5)
        
        # 打开HTML界面按钮
        self.html_btn = tk.Button(control_frame, text="🌐 打开HTML界面", 
                                 bg='#fd7e14', fg='white',
                                 command=self.open_html_interface, **btn_config)
        self.html_btn.pack(pady=5)
        
    def create_stats_panel(self, parent):
        """创建统计面板"""
        stats_frame = tk.LabelFrame(parent, text="📈 数据统计", 
                                   font=('Microsoft YaHei', 12, 'bold'),
                                   bg='white', padx=10, pady=10)
        stats_frame.pack(fill='x', padx=10, pady=10)
        
        # 统计标签
        self.stats_labels = {}
        
        stats_info = [
            ('total', '总股票数', '#333333'),
            ('sh', '沪市股票', '#e74c3c'),
            ('sz', '深市股票', '#2ecc71'),
            ('popularity', '人气榜', '#f39c12'),
            ('soaring', '飙升榜', '#9b59b6')
        ]
        
        for key, text, color in stats_info:
            label = tk.Label(stats_frame, text=f"{text}: 0", 
                           font=('Microsoft YaHei', 11), 
                           bg='white', fg=color)
            label.pack(anchor='w', pady=3)
            self.stats_labels[key] = label
        
        # 更新时间
        self.update_time_label = tk.Label(stats_frame, text="更新时间: --", 
                                         font=('Microsoft YaHei', 9), 
                                         bg='white', fg='#6c757d')
        self.update_time_label.pack(anchor='w', pady=(10, 0))
        
        # 进度条
        self.progress = ttk.Progressbar(stats_frame, mode='indeterminate')
        self.progress.pack(fill='x', pady=(10, 0))
        
    def create_data_display(self, parent):
        """创建数据显示区域"""
        data_frame = tk.LabelFrame(parent, text="📋 数据预览", 
                                  font=('Microsoft YaHei', 12, 'bold'),
                                  bg='white')
        data_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 创建Notebook（标签页）
        self.notebook = ttk.Notebook(data_frame)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 股票代码标签页
        codes_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(codes_frame, text='股票代码')
        
        self.codes_listbox = tk.Listbox(codes_frame, font=('Consolas', 10))
        codes_scrollbar = tk.Scrollbar(codes_frame, orient='vertical', command=self.codes_listbox.yview)
        self.codes_listbox.configure(yscrollcommand=codes_scrollbar.set)
        
        self.codes_listbox.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        codes_scrollbar.pack(side='right', fill='y', padx=(0, 5), pady=5)
        
        # 人气榜标签页
        popularity_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(popularity_frame, text='人气榜')
        
        self.popularity_listbox = tk.Listbox(popularity_frame, font=('Consolas', 10))
        pop_scrollbar = tk.Scrollbar(popularity_frame, orient='vertical', command=self.popularity_listbox.yview)
        self.popularity_listbox.configure(yscrollcommand=pop_scrollbar.set)
        
        self.popularity_listbox.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        pop_scrollbar.pack(side='right', fill='y', padx=(0, 5), pady=5)
        
        # 飙升榜标签页
        soaring_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(soaring_frame, text='飙升榜')
        
        self.soaring_listbox = tk.Listbox(soaring_frame, font=('Consolas', 10))
        soar_scrollbar = tk.Scrollbar(soaring_frame, orient='vertical', command=self.soaring_listbox.yview)
        self.soaring_listbox.configure(yscrollcommand=soar_scrollbar.set)
        
        self.soaring_listbox.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        soar_scrollbar.pack(side='right', fill='y', padx=(0, 5), pady=5)
        
    def create_log_panel(self, parent):
        """创建日志面板"""
        log_frame = tk.LabelFrame(parent, text="📝 系统日志", 
                                 font=('Microsoft YaHei', 12, 'bold'),
                                 bg='white')
        log_frame.pack(fill='x', padx=10, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, 
                                                 font=('Consolas', 9),
                                                 bg='#2c3e50', fg='#ecf0f1')
        self.log_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 添加初始日志
        self.add_log("系统初始化完成", "INFO")
        
    def load_data(self):
        """加载数据"""
        try:
            # 加载股票代码
            if os.path.exists('codes.txt'):
                with open('codes.txt', 'r', encoding='utf-8') as f:
                    self.data['codes'] = [line.strip() for line in f if line.strip()]
            
            # 加载缓存数据
            if os.path.exists('stock_cache.json'):
                with open('stock_cache.json', 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                    self.data['cached_stocks'] = cache_data.get('stocks', [])
            
            # 加载人气榜数据
            if os.path.exists('popularity.csv'):
                with open('popularity.csv', 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    self.data['popularity'] = list(reader)
            
            # 加载飙升榜数据
            if os.path.exists('soaring.csv'):
                with open('soaring.csv', 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    self.data['soaring'] = list(reader)
            
            self.update_display()
            self.add_log("数据加载完成", "SUCCESS")
            
        except Exception as e:
            self.add_log(f"数据加载失败: {e}", "ERROR")
    
    def update_display(self):
        """更新显示"""
        # 更新统计信息
        total_stocks = len(self.data['codes'])
        sh_stocks = len([code for code in self.data['codes'] if code.startswith('6')])
        sz_stocks = len([code for code in self.data['codes'] if code.startswith(('00', '30'))])
        popularity_count = len(self.data['popularity'])
        soaring_count = len(self.data['soaring'])
        
        self.stats_labels['total'].config(text=f"总股票数: {total_stocks}")
        self.stats_labels['sh'].config(text=f"沪市股票: {sh_stocks}")
        self.stats_labels['sz'].config(text=f"深市股票: {sz_stocks}")
        self.stats_labels['popularity'].config(text=f"人气榜: {popularity_count}")
        self.stats_labels['soaring'].config(text=f"飙升榜: {soaring_count}")
        self.update_time_label.config(text=f"更新时间: {datetime.now().strftime('%H:%M:%S')}")
        
        # 更新列表框
        self.update_listbox(self.codes_listbox, 
                           [f"{i+1:3d}. {code}" for i, code in enumerate(self.data['codes'])])
        
        self.update_listbox(self.popularity_listbox,
                           [f"{i+1:3d}. {row.get('code', '')} {row.get('name', '')} {row.get('change', '')}"
                            for i, row in enumerate(self.data['popularity'])])
        
        self.update_listbox(self.soaring_listbox,
                           [f"{i+1:3d}. {row.get('code', '')} {row.get('name', '')} {row.get('change', '')}"
                            for i, row in enumerate(self.data['soaring'])])
    
    def update_listbox(self, listbox, items):
        """更新列表框内容"""
        listbox.delete(0, tk.END)
        for item in items:
            listbox.insert(tk.END, item)
    
    def add_log(self, message, level="INFO"):
        """添加日志"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        level_colors = {
            'INFO': '#3498db',
            'SUCCESS': '#2ecc71',
            'WARNING': '#f39c12',
            'ERROR': '#e74c3c'
        }
        
        level_icons = {
            'INFO': 'ℹ️',
            'SUCCESS': '✅',
            'WARNING': '⚠️',
            'ERROR': '❌'
        }
        
        icon = level_icons.get(level, 'ℹ️')
        log_entry = f"[{timestamp}] {icon} {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
    
    def run_script_async(self, script_name, description):
        """异步运行脚本"""
        def run():
            try:
                self.progress.start()
                self.add_log(f"开始执行: {description}", "INFO")
                
                result = subprocess.run([
                    'python', script_name
                ], capture_output=True, text=True, encoding='utf-8')
                
                if result.returncode == 0:
                    self.add_log(f"{description} 完成", "SUCCESS")
                    self.root.after(0, self.refresh_data)
                else:
                    self.add_log(f"{description} 失败: {result.stderr}", "ERROR")
                    
            except Exception as e:
                self.add_log(f"{description} 执行出错: {e}", "ERROR")
            finally:
                self.progress.stop()
        
        threading.Thread(target=run, daemon=True).start()
    
    def start_collection(self):
        """开始采集"""
        self.run_script_async('全自动提取股票代码.py', '股票代码采集')
    
    def process_data(self):
        """处理数据"""
        self.run_script_async('从codes.txt读取股票代码.py', '数据处理')
    
    def generate_report(self):
        """生成报告"""
        self.run_script_async('数据分析报告.py', '报告生成')
    
    def refresh_data(self):
        """刷新数据"""
        self.load_data()
        self.add_log("数据已刷新", "SUCCESS")
    
    def open_html_interface(self):
        """打开HTML界面"""
        try:
            import webbrowser
            html_file = os.path.abspath('可视化界面.html')
            if os.path.exists(html_file):
                webbrowser.open(f'file:///{html_file}')
                self.add_log("HTML界面已在浏览器中打开", "SUCCESS")
            else:
                self.add_log("HTML界面文件不存在", "ERROR")
        except Exception as e:
            self.add_log(f"打开HTML界面失败: {e}", "ERROR")

def main():
    root = tk.Tk()
    app = SimpleStockVisualization(root)
    
    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass
    
    root.mainloop()

if __name__ == "__main__":
    main()

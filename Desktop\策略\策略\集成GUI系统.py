#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成GUI系统
股票数据采集系统的完整集成界面，所有结果都在同一界面显示
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import subprocess
import sys
import os
import threading
import webbrowser
from datetime import datetime
import csv
import json

class IntegratedStockGUI:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.create_widgets()
        self.load_initial_data()
        
    def setup_window(self):
        """设置窗口"""
        self.root.title("🚀 股票数据采集系统 - 集成控制台")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        self.root.configure(bg="#f0f0f0")
        
        # 窗口居中
        self.center_window()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 1200
        height = 800
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = tk.Frame(self.root, bg="#f0f0f0")
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部标题区域
        self.create_header(main_container)
        
        # 主要内容区域（左右分割）
        content_frame = tk.Frame(main_container, bg="#f0f0f0")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 左侧控制面板
        self.create_control_panel(content_frame)
        
        # 右侧结果显示区域
        self.create_result_area(content_frame)
        
        # 底部状态栏
        self.create_status_bar(main_container)
    
    def create_header(self, parent):
        """创建顶部标题区域"""
        header_frame = tk.Frame(parent, bg="#2c3e50", height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # 标题
        title_label = tk.Label(header_frame,
                              text="🚀 股票数据采集系统",
                              font=("Microsoft YaHei", 20, "bold"),
                              bg="#2c3e50",
                              fg="white")
        title_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # 版本信息
        version_label = tk.Label(header_frame,
                                text=f"v2.0 | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                                font=("Microsoft YaHei", 10),
                                bg="#2c3e50",
                                fg="#bdc3c7")
        version_label.pack(side=tk.RIGHT, padx=20, pady=20)
    
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        # 控制面板框架
        control_frame = tk.Frame(parent, bg="#ecf0f1", width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 控制面板标题
        control_title = tk.Label(control_frame,
                                text="📋 功能控制面板",
                                font=("Microsoft YaHei", 14, "bold"),
                                bg="#ecf0f1",
                                fg="#2c3e50")
        control_title.pack(pady=(15, 20))
        
        # 功能按钮
        self.create_function_buttons(control_frame)
        
        # 系统状态显示
        self.create_system_status(control_frame)
    
    def create_function_buttons(self, parent):
        """创建功能按钮"""
        buttons_data = [
            ("🔇 后台数据采集", self.run_background_collection, "#3498db"),
            ("📊 获取股票名称", self.get_stock_names, "#9b59b6"),
            ("📈 获取涨跌幅数据", self.get_price_changes, "#e67e22"),
            ("📄 生成分析报告", self.generate_report, "#27ae60"),
            ("🌐 启动数据服务器", self.start_server, "#f39c12"),
            ("🖥️ 打开可视化界面", self.open_web_interface, "#1abc9c"),
            ("📁 查看数据文件", self.view_data_files, "#34495e"),
            ("⚙️ 系统状态检查", self.check_system_status, "#95a5a6")
        ]
        
        for text, command, color in buttons_data:
            btn = tk.Button(parent,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=15,
                           pady=8,
                           cursor="hand2",
                           width=25)
            btn.pack(pady=5, padx=20, fill=tk.X)
    
    def create_system_status(self, parent):
        """创建系统状态显示"""
        status_frame = tk.LabelFrame(parent, 
                                    text="📊 系统状态",
                                    font=("Microsoft YaHei", 10, "bold"),
                                    bg="#ecf0f1",
                                    fg="#2c3e50")
        status_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        # Python版本
        tk.Label(status_frame, text="Python版本:", bg="#ecf0f1", font=("Microsoft YaHei", 9)).pack(anchor=tk.W, padx=10, pady=2)
        self.python_label = tk.Label(status_frame, text="检查中...", bg="#ecf0f1", fg="#27ae60", font=("Microsoft YaHei", 9))
        self.python_label.pack(anchor=tk.W, padx=20)
        
        # 依赖状态
        tk.Label(status_frame, text="依赖包:", bg="#ecf0f1", font=("Microsoft YaHei", 9)).pack(anchor=tk.W, padx=10, pady=2)
        self.deps_label = tk.Label(status_frame, text="检查中...", bg="#ecf0f1", fg="#27ae60", font=("Microsoft YaHei", 9))
        self.deps_label.pack(anchor=tk.W, padx=20)
        
        # 文件状态
        tk.Label(status_frame, text="核心文件:", bg="#ecf0f1", font=("Microsoft YaHei", 9)).pack(anchor=tk.W, padx=10, pady=2)
        self.files_label = tk.Label(status_frame, text="检查中...", bg="#ecf0f1", fg="#27ae60", font=("Microsoft YaHei", 9))
        self.files_label.pack(anchor=tk.W, padx=20, pady=(0, 10))
    
    def create_result_area(self, parent):
        """创建右侧结果显示区域"""
        # 结果区域框架
        result_frame = tk.Frame(parent, bg="#ffffff")
        result_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 结果区域标题
        result_title_frame = tk.Frame(result_frame, bg="#34495e", height=40)
        result_title_frame.pack(fill=tk.X)
        result_title_frame.pack_propagate(False)
        
        result_title = tk.Label(result_title_frame,
                               text="📊 操作结果与数据显示",
                               font=("Microsoft YaHei", 12, "bold"),
                               bg="#34495e",
                               fg="white")
        result_title.pack(side=tk.LEFT, padx=15, pady=10)
        
        # 清空按钮
        clear_btn = tk.Button(result_title_frame,
                             text="🗑️ 清空",
                             command=self.clear_results,
                             font=("Microsoft YaHei", 9),
                             bg="#e74c3c",
                             fg="white",
                             relief="raised",
                             bd=1,
                             padx=10,
                             pady=2,
                             cursor="hand2")
        clear_btn.pack(side=tk.RIGHT, padx=15, pady=8)
        
        # 标签页控件
        self.notebook = ttk.Notebook(result_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建各个标签页
        self.create_tabs()
    
    def create_tabs(self):
        """创建标签页"""
        # 操作日志标签页
        self.log_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.log_frame, text="📝 操作日志")
        
        self.log_text = scrolledtext.ScrolledText(self.log_frame,
                                                 font=("Consolas", 10),
                                                 wrap=tk.WORD,
                                                 bg="#f8f9fa",
                                                 fg="#2c3e50")
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 数据展示标签页
        self.data_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.data_frame, text="📊 数据展示")
        
        self.data_text = scrolledtext.ScrolledText(self.data_frame,
                                                  font=("Microsoft YaHei", 10),
                                                  wrap=tk.WORD,
                                                  bg="#f8f9fa",
                                                  fg="#2c3e50")
        self.data_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 系统信息标签页
        self.info_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.info_frame, text="⚙️ 系统信息")
        
        self.info_text = scrolledtext.ScrolledText(self.info_frame,
                                                  font=("Microsoft YaHei", 10),
                                                  wrap=tk.WORD,
                                                  bg="#f8f9fa",
                                                  fg="#2c3e50")
        self.info_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_status_bar(self, parent):
        """创建底部状态栏"""
        status_frame = tk.Frame(parent, bg="#bdc3c7", height=30)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        status_frame.pack_propagate(False)
        
        # 状态指示器
        self.status_label = tk.Label(status_frame,
                                    text="● 系统就绪",
                                    font=("Microsoft YaHei", 10, "bold"),
                                    bg="#bdc3c7",
                                    fg="#27ae60")
        self.status_label.pack(side=tk.LEFT, padx=15, pady=5)
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate', length=200)
        self.progress.pack(side=tk.LEFT, padx=20, pady=5)
        
        # 退出按钮
        exit_btn = tk.Button(status_frame,
                            text="🚪 退出",
                            command=self.exit_application,
                            font=("Microsoft YaHei", 9),
                            bg="#e74c3c",
                            fg="white",
                            relief="raised",
                            bd=1,
                            padx=15,
                            pady=2,
                            cursor="hand2")
        exit_btn.pack(side=tk.RIGHT, padx=15, pady=3)
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 根据级别设置颜色
        if level == "ERROR":
            self.log_text.tag_add("error", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("error", foreground="#e74c3c")
        elif level == "SUCCESS":
            self.log_text.tag_add("success", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("success", foreground="#27ae60")
        elif level == "WARNING":
            self.log_text.tag_add("warning", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("warning", foreground="#f39c12")
    
    def show_data(self, title, data):
        """在数据展示区域显示数据"""
        self.data_text.insert(tk.END, f"\n{'='*50}\n")
        self.data_text.insert(tk.END, f"📊 {title}\n")
        self.data_text.insert(tk.END, f"{'='*50}\n")
        self.data_text.insert(tk.END, f"{data}\n")
        self.data_text.see(tk.END)
        
        # 切换到数据展示标签页
        self.notebook.select(self.data_frame)
    
    def show_info(self, title, info):
        """在系统信息区域显示信息"""
        self.info_text.insert(tk.END, f"\n{'='*50}\n")
        self.info_text.insert(tk.END, f"⚙️ {title}\n")
        self.info_text.insert(tk.END, f"{'='*50}\n")
        self.info_text.insert(tk.END, f"{info}\n")
        self.info_text.see(tk.END)
        
        # 切换到系统信息标签页
        self.notebook.select(self.info_frame)
    
    def clear_results(self):
        """清空所有结果显示"""
        self.log_text.delete(1.0, tk.END)
        self.data_text.delete(1.0, tk.END)
        self.info_text.delete(1.0, tk.END)
        self.log_message("所有显示区域已清空", "INFO")
    
    def set_status(self, message, color="#27ae60"):
        """设置状态"""
        self.status_label.config(text=f"● {message}", fg=color)
        self.root.update()
    
    def start_progress(self):
        """开始进度条"""
        self.progress.start(10)
    
    def stop_progress(self):
        """停止进度条"""
        self.progress.stop()
    
    def load_initial_data(self):
        """加载初始数据"""
        self.log_message("欢迎使用股票数据采集系统！", "SUCCESS")
        self.log_message("系统初始化完成，请选择功能开始使用", "INFO")
        
        # 检查系统状态
        self.check_system_status_silent()
        
        # 加载现有数据
        self.load_existing_data()
    
    def check_system_status_silent(self):
        """静默检查系统状态"""
        # Python版本
        python_version = f"{sys.version.split()[0]}"
        self.python_label.config(text=python_version)
        
        # 检查依赖包
        required_modules = ["selenium", "requests", "matplotlib", "pandas", "numpy"]
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            self.deps_label.config(text=f"缺少 {len(missing_modules)} 个", fg="#e74c3c")
        else:
            self.deps_label.config(text="全部已安装", fg="#27ae60")
        
        # 检查核心文件
        core_files = [
            "后台采集.py", "股票名称获取工具.py", "股票涨跌幅获取工具.py",
            "智能数据分析报告.py", "数据服务器.py", "可视化界面_实时版.html"
        ]
        
        missing_files = [f for f in core_files if not os.path.exists(f)]
        
        if missing_files:
            self.files_label.config(text=f"缺少 {len(missing_files)} 个", fg="#e74c3c")
        else:
            self.files_label.config(text="全部存在", fg="#27ae60")
    
    def load_existing_data(self):
        """加载现有数据文件"""
        try:
            data_summary = "📊 当前数据文件状态:\n\n"
            
            # 检查CSV文件
            csv_files = [("popularity.csv", "人气榜"), ("soaring.csv", "飙升榜")]
            
            for filename, description in csv_files:
                if os.path.exists(filename):
                    try:
                        with open(filename, 'r', encoding='utf-8') as f:
                            reader = csv.reader(f)
                            rows = list(reader)
                            if len(rows) > 1:  # 有数据行
                                data_summary += f"✅ {description}数据: {len(rows)-1} 条记录\n"
                                
                                # 显示前5条数据
                                data_summary += f"   前5条数据预览:\n"
                                for i, row in enumerate(rows[1:6]):  # 跳过标题行
                                    if len(row) >= 2:
                                        data_summary += f"   {i+1}. {row[0]} {row[1]} {row[2] if len(row) > 2 else ''}\n"
                                data_summary += "\n"
                            else:
                                data_summary += f"⚠️ {description}数据: 文件为空\n"
                    except Exception as e:
                        data_summary += f"❌ {description}数据: 读取失败 ({str(e)})\n"
                else:
                    data_summary += f"❌ {description}数据: 文件不存在\n"
            
            # 检查其他文件
            other_files = [
                ("codes.txt", "股票代码库"),
                ("stock_names_cache.json", "股票名称缓存"),
                ("股票分析报告.html", "分析报告")
            ]
            
            for filename, description in other_files:
                if os.path.exists(filename):
                    size = os.path.getsize(filename)
                    if size < 1024:
                        size_str = f"{size} B"
                    elif size < 1024 * 1024:
                        size_str = f"{size/1024:.1f} KB"
                    else:
                        size_str = f"{size/(1024*1024):.1f} MB"
                    data_summary += f"✅ {description}: {size_str}\n"
                else:
                    data_summary += f"❌ {description}: 不存在\n"
            
            self.show_data("系统数据概览", data_summary)
            
        except Exception as e:
            self.log_message(f"加载数据时出错: {str(e)}", "ERROR")
    
    def run_script_with_output(self, script_name, description):
        """运行脚本并显示输出"""
        def run():
            self.set_status("运行中...", "#f39c12")
            self.start_progress()
            self.log_message(f"开始执行: {description}", "INFO")
            
            try:
                if not os.path.exists(script_name):
                    raise FileNotFoundError(f"脚本文件不存在: {script_name}")
                
                # 运行脚本并捕获输出
                process = subprocess.Popen([sys.executable, script_name],
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE,
                                         text=True,
                                         cwd=os.getcwd(),
                                         encoding='gbk',
                                         errors='ignore')
                
                # 实时读取输出
                output_lines = []
                while True:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break
                    if output:
                        output_lines.append(output.strip())
                        # 实时显示到日志
                        self.log_message(output.strip(), "INFO")
                
                # 获取错误输出
                stderr_output = process.stderr.read()
                
                if process.returncode == 0:
                    self.log_message(f"{description} 执行成功", "SUCCESS")
                    if output_lines:
                        self.show_data(f"{description} - 执行结果", "\n".join(output_lines))
                else:
                    self.log_message(f"{description} 执行失败", "ERROR")
                    if stderr_output:
                        self.show_data(f"{description} - 错误信息", stderr_output)
                
                # 重新加载数据
                self.load_existing_data()
                
            except Exception as e:
                error_msg = f"{description} 执行出错: {str(e)}"
                self.log_message(error_msg, "ERROR")
                self.show_data(f"{description} - 错误", str(e))
            
            finally:
                self.stop_progress()
                self.set_status("就绪", "#27ae60")
        
        # 在新线程中运行
        thread = threading.Thread(target=run, daemon=True)
        thread.start()
    
    # 功能按钮回调函数
    def run_background_collection(self):
        """后台数据采集"""
        self.run_script_with_output("后台采集.py", "后台数据采集")
    
    def get_stock_names(self):
        """获取股票名称"""
        self.run_script_with_output("股票名称获取工具.py", "股票名称获取")
    
    def get_price_changes(self):
        """获取涨跌幅数据"""
        self.run_script_with_output("股票涨跌幅获取工具.py", "涨跌幅数据获取")
    
    def generate_report(self):
        """生成分析报告"""
        def on_complete():
            # 询问是否打开报告
            if messagebox.askyesno("报告生成完成", "分析报告已生成！是否在浏览器中打开？"):
                self.open_html_report()
        
        self.run_script_with_output("智能数据分析报告.py", "智能分析报告生成")
        # 延迟询问打开报告
        self.root.after(3000, on_complete)
    
    def start_server(self):
        """启动数据服务器"""
        try:
            self.log_message("启动数据服务器...", "INFO")
            subprocess.Popen([sys.executable, "数据服务器.py"], cwd=os.getcwd())
            self.log_message("数据服务器已在后台启动", "SUCCESS")
            self.show_info("数据服务器", "服务器已启动\n访问地址: http://localhost:8080/可视化界面_实时版.html")
            
            # 询问是否打开界面
            if messagebox.askyesno("服务器启动", "数据服务器已启动！\n是否在浏览器中打开可视化界面？"):
                self.open_web_interface()
                
        except Exception as e:
            self.log_message(f"启动服务器失败: {str(e)}", "ERROR")
    
    def open_web_interface(self):
        """打开可视化界面"""
        try:
            url = "http://localhost:8080/可视化界面_实时版.html"
            webbrowser.open(url)
            self.log_message("可视化界面已在浏览器中打开", "SUCCESS")
        except Exception as e:
            self.log_message(f"打开界面失败: {str(e)}", "ERROR")
    
    def open_html_report(self):
        """打开HTML报告"""
        try:
            report_path = os.path.abspath("股票分析报告.html")
            webbrowser.open(f"file:///{report_path}")
            self.log_message("分析报告已在浏览器中打开", "SUCCESS")
        except Exception as e:
            self.log_message(f"打开报告失败: {str(e)}", "ERROR")
    
    def view_data_files(self):
        """查看数据文件"""
        self.log_message("刷新数据文件状态...", "INFO")
        self.load_existing_data()
    
    def check_system_status(self):
        """检查系统状态"""
        self.log_message("执行系统状态检查...", "INFO")
        
        # 更新状态显示
        self.check_system_status_silent()
        
        # 生成详细报告
        status_report = "⚙️ 系统状态详细报告\n\n"
        
        # Python信息
        status_report += f"🐍 Python版本: {sys.version}\n"
        status_report += f"📍 Python路径: {sys.executable}\n\n"
        
        # 依赖包检查
        required_modules = ["selenium", "requests", "matplotlib", "pandas", "numpy"]
        status_report += "📦 依赖包状态:\n"
        
        for module in required_modules:
            try:
                mod = __import__(module)
                version = getattr(mod, '__version__', '未知版本')
                status_report += f"  ✅ {module}: {version}\n"
            except ImportError:
                status_report += f"  ❌ {module}: 未安装\n"
        
        # 文件检查
        core_files = [
            "后台采集.py", "股票名称获取工具.py", "股票涨跌幅获取工具.py",
            "智能数据分析报告.py", "数据服务器.py", "可视化界面_实时版.html"
        ]
        
        status_report += "\n📁 核心文件状态:\n"
        for file in core_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                status_report += f"  ✅ {file}: {size/1024:.1f} KB\n"
            else:
                status_report += f"  ❌ {file}: 不存在\n"
        
        # 数据文件检查
        data_files = ["popularity.csv", "soaring.csv", "codes.txt", "stock_names_cache.json"]
        status_report += "\n📊 数据文件状态:\n"
        for file in data_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                mod_time = datetime.fromtimestamp(os.path.getmtime(file))
                status_report += f"  ✅ {file}: {size/1024:.1f} KB ({mod_time.strftime('%m-%d %H:%M')})\n"
            else:
                status_report += f"  ❌ {file}: 不存在\n"
        
        self.show_info("系统状态检查", status_report)
        self.log_message("系统状态检查完成", "SUCCESS")
    
    def exit_application(self):
        """退出应用程序"""
        if messagebox.askyesno("确认退出", "确定要退出股票数据采集系统吗？"):
            self.log_message("正在退出系统...", "INFO")
            self.root.quit()
            self.root.destroy()

def main():
    """主函数"""
    root = tk.Tk()
    app = IntegratedStockGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()

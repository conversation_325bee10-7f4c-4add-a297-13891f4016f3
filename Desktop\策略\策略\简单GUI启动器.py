#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单GUI启动器
股票数据采集系统的简化图形界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
import threading
import webbrowser
from datetime import datetime

class SimpleStockGUI:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """设置窗口"""
        self.root.title("🚀 股票数据采集系统")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 窗口居中
        self.center_window()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 800
        height = 600
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = tk.Frame(self.root, bg="#f0f0f0", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(main_frame, 
                              text="🚀 股票数据采集系统",
                              font=("Microsoft YaHei", 18, "bold"),
                              bg="#f0f0f0",
                              fg="#2c3e50")
        title_label.pack(pady=(0, 10))
        
        # 版本信息
        version_label = tk.Label(main_frame,
                                text=f"版本: v2.0 | 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                                font=("Microsoft YaHei", 10),
                                bg="#f0f0f0",
                                fg="#7f8c8d")
        version_label.pack(pady=(0, 30))
        
        # 按钮框架
        button_frame = tk.Frame(main_frame, bg="#f0f0f0")
        button_frame.pack(expand=True)
        
        # 创建按钮
        self.create_buttons(button_frame)
        
        # 状态栏
        self.create_status_bar(main_frame)
    
    def create_buttons(self, parent):
        """创建功能按钮"""
        buttons = [
            ("🔇 后台数据采集", self.run_background_collection, "#3498db"),
            ("📊 获取股票名称", self.get_stock_names, "#9b59b6"),
            ("📈 获取涨跌幅数据", self.get_price_changes, "#e67e22"),
            ("📄 生成分析报告", self.generate_report, "#27ae60"),
            ("🌐 启动数据服务器", self.start_server, "#f39c12"),
            ("🖥️ 打开可视化界面", self.open_web_interface, "#1abc9c"),
            ("📁 查看数据文件", self.view_data_files, "#34495e"),
            ("⚙️ 系统状态检查", self.check_system_status, "#95a5a6")
        ]
        
        # 创建按钮网格
        for i, (text, command, color) in enumerate(buttons):
            row = i // 2
            col = i % 2
            
            btn = tk.Button(parent,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 12, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=3,
                           padx=20,
                           pady=15,
                           cursor="hand2",
                           width=20,
                           height=2)
            btn.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        # 配置网格权重
        for i in range(2):
            parent.columnconfigure(i, weight=1)
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = tk.Frame(parent, bg="#f0f0f0")
        status_frame.pack(fill=tk.X, pady=(30, 0))
        
        # 状态标签
        self.status_label = tk.Label(status_frame,
                                    text="● 系统就绪",
                                    font=("Microsoft YaHei", 10, "bold"),
                                    bg="#f0f0f0",
                                    fg="#27ae60")
        self.status_label.pack(side=tk.LEFT)
        
        # 退出按钮
        exit_btn = tk.Button(status_frame,
                            text="🚪 退出系统",
                            command=self.exit_application,
                            font=("Microsoft YaHei", 10),
                            bg="#e74c3c",
                            fg="white",
                            relief="raised",
                            bd=2,
                            padx=15,
                            pady=5,
                            cursor="hand2")
        exit_btn.pack(side=tk.RIGHT)
    
    def set_status(self, message, color="#27ae60"):
        """设置状态"""
        self.status_label.config(text=f"● {message}", fg=color)
        self.root.update()
    
    def run_script_simple(self, script_name, description):
        """简单运行脚本"""
        def run():
            self.set_status("运行中...", "#f39c12")
            
            try:
                if not os.path.exists(script_name):
                    messagebox.showerror("错误", f"脚本文件不存在: {script_name}")
                    return
                
                # 简单启动脚本，不捕获输出
                subprocess.Popen([sys.executable, script_name], cwd=os.getcwd())
                
                self.set_status("执行完成", "#27ae60")
                messagebox.showinfo("成功", f"{description} 已启动")
                
            except Exception as e:
                self.set_status("执行失败", "#e74c3c")
                messagebox.showerror("错误", f"{description} 执行失败:\n{str(e)}")
        
        # 在新线程中运行
        thread = threading.Thread(target=run, daemon=True)
        thread.start()
    
    # 功能按钮回调函数
    def run_background_collection(self):
        """后台数据采集"""
        if messagebox.askyesno("确认", "开始后台数据采集？\n这将启动浏览器采集股票数据。"):
            self.run_script_simple("后台采集.py", "后台数据采集")
    
    def get_stock_names(self):
        """获取股票名称"""
        if messagebox.askyesno("确认", "开始获取股票名称？\n这将从网络获取真实的股票名称。"):
            self.run_script_simple("股票名称获取工具.py", "股票名称获取")
    
    def get_price_changes(self):
        """获取涨跌幅数据"""
        if messagebox.askyesno("确认", "开始获取涨跌幅数据？\n这将从网络获取实时涨跌幅。"):
            self.run_script_simple("股票涨跌幅获取工具.py", "涨跌幅数据获取")
    
    def generate_report(self):
        """生成分析报告"""
        if messagebox.askyesno("确认", "生成智能分析报告？\n这将分析数据并生成HTML报告。"):
            self.run_script_simple("智能数据分析报告.py", "智能分析报告")
            
            # 询问是否打开报告
            if messagebox.askyesno("报告生成", "报告生成完成！是否打开HTML报告？"):
                self.open_html_report()
    
    def start_server(self):
        """启动数据服务器"""
        try:
            self.set_status("启动服务器...", "#f39c12")
            subprocess.Popen([sys.executable, "数据服务器.py"], cwd=os.getcwd())
            self.set_status("服务器已启动", "#27ae60")
            
            if messagebox.askyesno("服务器启动", "数据服务器已启动！\n是否打开可视化界面？"):
                self.open_web_interface()
                
        except Exception as e:
            self.set_status("启动失败", "#e74c3c")
            messagebox.showerror("错误", f"启动服务器失败:\n{str(e)}")
    
    def open_web_interface(self):
        """打开可视化界面"""
        try:
            url = "http://localhost:8080/可视化界面_实时版.html"
            webbrowser.open(url)
            self.set_status("界面已打开", "#27ae60")
            messagebox.showinfo("成功", "可视化界面已在浏览器中打开")
        except Exception as e:
            messagebox.showerror("错误", f"打开界面失败:\n{str(e)}")
    
    def open_html_report(self):
        """打开HTML报告"""
        try:
            report_path = os.path.abspath("股票分析报告.html")
            webbrowser.open(f"file:///{report_path}")
            messagebox.showinfo("成功", "分析报告已在浏览器中打开")
        except Exception as e:
            messagebox.showerror("错误", f"打开报告失败:\n{str(e)}")
    
    def view_data_files(self):
        """查看数据文件"""
        data_files = [
            ("popularity.csv", "人气榜数据"),
            ("soaring.csv", "飙升榜数据"),
            ("codes.txt", "股票代码库"),
            ("stock_names_cache.json", "股票名称缓存"),
            ("股票分析报告.html", "分析报告")
        ]
        
        status_info = "📁 数据文件状态:\n" + "="*40 + "\n"
        
        for file_path, description in data_files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                if size < 1024:
                    size_str = f"{size} B"
                elif size < 1024 * 1024:
                    size_str = f"{size/1024:.1f} KB"
                else:
                    size_str = f"{size/(1024*1024):.1f} MB"
                
                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                status_info += f"✅ {description}: {size_str}\n    ({mod_time.strftime('%Y-%m-%d %H:%M')})\n"
            else:
                status_info += f"❌ {description}: 不存在\n"
        
        messagebox.showinfo("数据文件状态", status_info)
    
    def check_system_status(self):
        """检查系统状态"""
        self.set_status("检查中...", "#f39c12")
        
        # Python版本
        python_version = f"{sys.version.split()[0]}"
        
        # 检查依赖包
        required_modules = ["selenium", "requests", "matplotlib", "pandas", "numpy"]
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        # 检查核心文件
        core_files = [
            "后台采集.py", "股票名称获取工具.py", "股票涨跌幅获取工具.py",
            "智能数据分析报告.py", "数据服务器.py", "可视化界面_实时版.html"
        ]
        
        missing_files = [f for f in core_files if not os.path.exists(f)]
        
        # 生成状态报告
        status_report = f"⚙️ 系统状态检查报告\n{'='*40}\n"
        status_report += f"🐍 Python版本: {python_version}\n\n"
        
        if missing_modules:
            status_report += f"❌ 缺少依赖包: {', '.join(missing_modules)}\n"
        else:
            status_report += f"✅ 所有依赖包已安装\n"
        
        if missing_files:
            status_report += f"❌ 缺少核心文件: {len(missing_files)} 个\n"
            for file in missing_files:
                status_report += f"   - {file}\n"
        else:
            status_report += f"✅ 所有核心文件存在\n"
        
        status_report += f"\n📊 系统状态: "
        if not missing_modules and not missing_files:
            status_report += "正常 ✅"
            self.set_status("系统正常", "#27ae60")
        else:
            status_report += "异常 ❌"
            self.set_status("系统异常", "#e74c3c")
        
        messagebox.showinfo("系统状态", status_report)
    
    def exit_application(self):
        """退出应用程序"""
        if messagebox.askyesno("确认退出", "确定要退出股票数据采集系统吗？"):
            self.root.quit()
            self.root.destroy()

def main():
    """主函数"""
    root = tk.Tk()
    app = SimpleStockGUI(root)
    
    # 显示欢迎消息
    messagebox.showinfo("欢迎", "欢迎使用股票数据采集系统！\n\n点击按钮开始使用各项功能。")
    
    root.mainloop()

if __name__ == "__main__":
    main()

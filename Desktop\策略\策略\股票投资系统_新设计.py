#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票智能投资系统 - 新界面设计
左侧：快捷操作 + 系统信息
中间：顶部菜单 + 主显示区域
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import sys
import os
import threading
import json
import csv
from datetime import datetime
import time

class StockInvestmentSystemNew:
    def __init__(self, root):
        self.root = root
        self.current_module = "选股"  # 当前显示的模块
        self.setup_window()
        self.create_main_interface()
        self.load_initial_data()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("🚀 智股投")
        self.root.geometry("1600x1000")
        self.root.resizable(True, True)
        self.root.configure(bg="#f8f9fa")
        
        # 窗口居中
        self.center_window()
        
        # 设置样式
        self.setup_styles()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 1600
        height = 1000
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def setup_styles(self):
        """设置界面样式"""
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # 导航按钮样式
        self.style.configure("Nav.TButton",
                           font=("Microsoft YaHei", 12, "bold"),
                           padding=(20, 10))
        
        # 功能按钮样式
        self.style.configure("Action.TButton",
                           font=("Microsoft YaHei", 10, "bold"),
                           padding=(15, 8))
    
    def create_main_interface(self):
        """创建主界面"""
        # 主容器
        main_container = tk.Frame(self.root, bg="#f8f9fa")
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板（快捷操作 + 系统信息）
        self.create_left_panel(main_container)
        
        # 右侧区域（菜单 + 主显示区）
        self.create_right_area(main_container)

        # 底部状态栏
        self.create_status_bar(main_container)
    
    def create_left_panel(self, parent):
        """创建左侧面板"""
        left_frame = tk.Frame(parent, bg="#ecf0f1", width=280)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.pack_propagate(False)
        
        # 快捷操作区域
        self.create_quick_actions(left_frame)
        
        # 系统信息区域
        self.create_system_info(left_frame)
    
    def create_quick_actions(self, parent):
        """创建功能操作区域"""
        # 智股投标题
        title_frame = tk.Frame(parent, bg="#2c3e50", height=50)
        title_frame.pack(fill=tk.X, pady=(15, 20))
        title_frame.pack_propagate(False)

        tk.Label(title_frame,
                text="🚀 智股投",
                font=("Microsoft YaHei", 16, "bold"),
                bg="#2c3e50",
                fg="white").pack(expand=True)
        
        # 功能按钮 - 集成项目所有功能
        quick_buttons = [
            ("� 一键分析", self.run_comprehensive_stock_analysis, "#e74c3c"),
            ("� 热门股选股", self.run_hot_stocks, "#e67e22"),
            ("� 消息面选股", self.run_news_stocks, "#9b59b6"),
            ("🤖 AI智能分析", self.run_ai_analysis, "#8e44ad"),
            ("📊 生成报告", self.generate_report, "#27ae60"),
            ("🌐 Web可视化", self.start_web_visualization, "#16a085"),
            ("� 查看结果", self.show_all_results, "#34495e"),
            ("� 模拟交易", self.open_simulation_trading, "#f39c12"),
            ("🔧 系统设置", self.open_system_config, "#95a5a6")
        ]
        
        for text, command, color in quick_buttons:
            btn = tk.Button(parent,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=10,
                           pady=8,
                           cursor="hand2",
                           width=22)
            btn.pack(pady=6, padx=15, fill=tk.X)
    
    def create_system_info(self, parent):
        """创建系统信息区域"""
        # 分隔线
        separator = tk.Frame(parent, height=2, bg="#bdc3c7")
        separator.pack(fill=tk.X, padx=15, pady=20)
        
        # 系统信息标题
        tk.Label(parent,
                text="📊 系统信息",
                font=("Microsoft YaHei", 14, "bold"),
                bg="#ecf0f1",
                fg="#2c3e50").pack(pady=(0, 15))
        
        # 实时时间
        self.time_label = tk.Label(parent,
                                  text=f"🕐 {datetime.now().strftime('%H:%M:%S')}",
                                  font=("Microsoft YaHei", 12),
                                  bg="#ecf0f1",
                                  fg="#34495e")
        self.time_label.pack(pady=5)
        
        # 系统状态日志
        log_frame = tk.LabelFrame(parent,
                                text="📋 系统日志",
                                font=("Microsoft YaHei", 10, "bold"),
                                bg="#ecf0f1",
                                fg="#2c3e50")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame,
                                                height=12,
                                                width=30,
                                                font=("Consolas", 9),
                                                bg="#2c3e50",
                                                fg="#00ff00",
                                                insertbackground="white")
        self.log_text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)
        
        # 快速统计
        stats_frame = tk.LabelFrame(parent,
                                  text="📈 数据统计",
                                  font=("Microsoft YaHei", 10, "bold"),
                                  bg="#ecf0f1",
                                  fg="#2c3e50")
        stats_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        self.stats_labels = {}
        stats_items = [
            ("热门股票", "0"),
            ("消息股票", "0"),
            ("AI推荐", "0"),
            ("持仓股票", "0")
        ]
        
        for item, value in stats_items:
            frame = tk.Frame(stats_frame, bg="#ecf0f1")
            frame.pack(fill=tk.X, padx=10, pady=2)
            
            tk.Label(frame, text=f"{item}:", 
                    font=("Microsoft YaHei", 9),
                    bg="#ecf0f1", fg="#34495e").pack(side=tk.LEFT)
            
            label = tk.Label(frame, text=value,
                           font=("Microsoft YaHei", 9, "bold"),
                           bg="#ecf0f1", fg="#e74c3c")
            label.pack(side=tk.RIGHT)
            self.stats_labels[item] = label
    
    def create_right_area(self, parent):
        """创建右侧区域（菜单 + 主显示区）"""
        right_frame = tk.Frame(parent, bg="white", relief="solid", bd=1)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 顶部菜单栏
        self.create_top_menu(right_frame)
        
        # 主显示区域
        self.create_main_display(right_frame)
    
    def create_top_menu(self, parent):
        """创建顶部菜单栏"""
        menu_frame = tk.Frame(parent, bg="#34495e", height=60)
        menu_frame.pack(fill=tk.X)
        menu_frame.pack_propagate(False)
        
        # 菜单按钮
        menu_buttons = [
            ("📊 选股", "选股", "#3498db"),
            ("🔍 分析", "分析", "#e74c3c"),
            ("📋 结果", "结果", "#27ae60"),
            ("💰 交易", "交易", "#f39c12"),
            ("⚙️ 配置", "配置", "#9b59b6")
        ]
        
        # 菜单按钮容器（居中显示）
        menu_container = tk.Frame(menu_frame, bg="#34495e")
        menu_container.pack(expand=True)
        
        self.menu_buttons = {}
        for text, module, color in menu_buttons:
            btn = tk.Button(menu_container,
                           text=text,
                           command=lambda m=module: self.switch_module(m),
                           font=("Microsoft YaHei", 12, "bold"),
                           bg=color if module == self.current_module else "#34495e",
                           fg="white",
                           relief="flat",
                           padx=25,
                           pady=12,
                           cursor="hand2",
                           bd=0)
            btn.pack(side=tk.LEFT, padx=8, pady=10)
            self.menu_buttons[module] = btn
    
    def create_main_display(self, parent):
        """创建主显示区域"""
        # 显示区域容器
        self.display_frame = tk.Frame(parent, bg="white")
        self.display_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 内容显示区域（直接占满整个区域）
        self.content_frame = tk.Frame(self.display_frame, bg="white")
        self.content_frame.pack(fill=tk.BOTH, expand=True)

        # 初始化显示选股模块内容
        self.update_display_content()
    
    def create_status_bar(self, parent):
        """创建底部状态栏"""
        status_frame = tk.Frame(parent, bg="#34495e", height=30)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        status_frame.pack_propagate(False)
        
        # 状态信息
        self.status_label = tk.Label(status_frame,
                                    text="● 系统就绪",
                                    font=("Microsoft YaHei", 9),
                                    bg="#34495e",
                                    fg="#2ecc71")
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame,
                                      mode='indeterminate',
                                      length=200)
        self.progress.pack(side=tk.RIGHT, padx=10, pady=5)
    
    def switch_module(self, module):
        """切换功能模块"""
        self.current_module = module

        # 更新菜单按钮状态
        colors = {"选股": "#3498db", "分析": "#e74c3c", "结果": "#27ae60", "交易": "#f39c12", "配置": "#9b59b6"}
        for mod, btn in self.menu_buttons.items():
            if mod == module:
                btn.configure(bg=colors[mod])
            else:
                btn.configure(bg="#34495e")

        # 更新显示内容
        self.update_display_content()

        self.log_message(f"切换到{module}模块", "INFO")
    
    def update_display_content(self):
        """更新显示内容"""
        # 清空现有内容
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # 根据当前模块显示不同内容
        if self.current_module == "选股":
            self.create_stock_selection_display()
        elif self.current_module == "分析":
            self.create_analysis_display()
        elif self.current_module == "结果":
            self.create_results_display()
        elif self.current_module == "交易":
            self.create_trading_display()
        elif self.current_module == "配置":
            self.create_config_display()

    def create_stock_selection_display(self):
        """创建选股模块显示内容"""
        # 选股结果表格
        columns = ("排名", "股票代码", "股票名称", "当前价格", "涨跌幅", "推荐理由", "数据来源")
        self.stock_tree = ttk.Treeview(self.content_frame, columns=columns, show="headings", height=20)

        # 设置列标题和宽度
        column_widths = {"排名": 60, "股票代码": 100, "股票名称": 120, "当前价格": 100,
                        "涨跌幅": 100, "推荐理由": 200, "数据来源": 120}

        for col in columns:
            self.stock_tree.heading(col, text=col)
            self.stock_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)

        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(self.content_frame, orient="vertical", command=self.stock_tree.yview)
        scrollbar_h = ttk.Scrollbar(self.content_frame, orient="horizontal", command=self.stock_tree.xview)
        self.stock_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

        # 布局
        self.stock_tree.pack(side="left", fill="both", expand=True)
        scrollbar_v.pack(side="right", fill="y")
        scrollbar_h.pack(side="bottom", fill="x")

        # 加载现有数据
        self.load_existing_stock_data()

    def create_analysis_display(self):
        """创建分析模块显示内容"""
        # 创建上下分割的布局
        top_frame = tk.Frame(self.content_frame, bg="white", height=80)
        top_frame.pack(fill=tk.X, pady=(0, 10))
        top_frame.pack_propagate(False)

        bottom_frame = tk.Frame(self.content_frame, bg="white")
        bottom_frame.pack(fill=tk.BOTH, expand=True)

        # 上半部分：分析功能按钮
        analysis_buttons_frame = tk.Frame(top_frame, bg="white")
        analysis_buttons_frame.pack(expand=True)

        analysis_buttons = [
            ("🤖 AI智能分析", self.run_ai_analysis_detailed, "#8e44ad"),
            ("📊 技术指标分析", self.run_technical_analysis, "#3498db"),
            ("💭 情感分析", self.run_sentiment_analysis, "#e67e22"),
            ("⚖️ 风险评估", self.run_risk_assessment, "#e74c3c")
        ]

        for text, command, color in analysis_buttons:
            btn = tk.Button(analysis_buttons_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=15,
                           pady=8,
                           cursor="hand2",
                           width=15)
            btn.pack(side=tk.LEFT, padx=10)

        # 下半部分：分析结果显示
        self.analysis_text = scrolledtext.ScrolledText(bottom_frame,
                                                     height=20,
                                                     font=("Microsoft YaHei", 11),
                                                     bg="white",
                                                     fg="#2c3e50",
                                                     wrap=tk.WORD)
        self.analysis_text.pack(fill=tk.BOTH, expand=True)

        # 显示默认内容
        self.show_analysis_welcome_message()

    def create_results_display(self):
        """创建结果模块显示内容"""
        # 创建标签页
        notebook = ttk.Notebook(self.content_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # 推荐列表标签页
        recommendations_frame = tk.Frame(notebook, bg="white")
        notebook.add(recommendations_frame, text="📈 推荐列表")

        # 分析报告标签页
        report_frame = tk.Frame(notebook, bg="white")
        notebook.add(report_frame, text="📊 分析报告")

        # 投资建议标签页
        advice_frame = tk.Frame(notebook, bg="white")
        notebook.add(advice_frame, text="🎯 投资建议")

        # 在推荐列表中创建表格
        rec_columns = ("等级", "排名", "股票代码", "股票名称", "AI得分", "风险等级", "推荐理由")
        self.rec_tree = ttk.Treeview(recommendations_frame, columns=rec_columns, show="headings", height=18)

        for col in rec_columns:
            self.rec_tree.heading(col, text=col)
            self.rec_tree.column(col, width=120, anchor=tk.CENTER)

        rec_scrollbar = ttk.Scrollbar(recommendations_frame, orient="vertical", command=self.rec_tree.yview)
        self.rec_tree.configure(yscrollcommand=rec_scrollbar.set)

        self.rec_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        rec_scrollbar.pack(side="right", fill="y", pady=10)

        # 在分析报告中添加文本显示
        self.report_text = scrolledtext.ScrolledText(report_frame,
                                                   font=("Microsoft YaHei", 10),
                                                   bg="white",
                                                   fg="#2c3e50",
                                                   wrap=tk.WORD)
        self.report_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 在投资建议中添加内容
        advice_text = scrolledtext.ScrolledText(advice_frame,
                                              font=("Microsoft YaHei", 10),
                                              bg="white",
                                              fg="#2c3e50",
                                              wrap=tk.WORD)
        advice_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 初始化结果数据
        self.load_results_data()

    def create_trading_display(self):
        """创建交易模块显示内容"""
        # 创建上下分割的布局
        top_frame = tk.Frame(self.content_frame, bg="white", height=200)
        top_frame.pack(fill=tk.X, pady=(0, 10))
        top_frame.pack_propagate(False)

        bottom_frame = tk.Frame(self.content_frame, bg="white")
        bottom_frame.pack(fill=tk.BOTH, expand=True)

        # 上半部分：交易操作面板
        trading_panel = tk.LabelFrame(top_frame,
                                    text="💹 交易操作",
                                    font=("Microsoft YaHei", 12, "bold"),
                                    bg="white",
                                    fg="#2c3e50")
        trading_panel.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 交易输入区域
        input_frame = tk.Frame(trading_panel, bg="white")
        input_frame.pack(fill=tk.X, padx=20, pady=15)

        # 股票代码
        tk.Label(input_frame, text="股票代码:", font=("Microsoft YaHei", 10), bg="white").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.stock_code_entry = tk.Entry(input_frame, font=("Microsoft YaHei", 10), width=12)
        self.stock_code_entry.grid(row=0, column=1, padx=(0, 20))

        # 交易数量
        tk.Label(input_frame, text="数量:", font=("Microsoft YaHei", 10), bg="white").grid(row=0, column=2, sticky="w", padx=(0, 10))
        self.quantity_entry = tk.Entry(input_frame, font=("Microsoft YaHei", 10), width=12)
        self.quantity_entry.grid(row=0, column=3, padx=(0, 20))

        # 交易价格
        tk.Label(input_frame, text="价格:", font=("Microsoft YaHei", 10), bg="white").grid(row=0, column=4, sticky="w", padx=(0, 10))
        self.price_entry = tk.Entry(input_frame, font=("Microsoft YaHei", 10), width=12)
        self.price_entry.grid(row=0, column=5, padx=(0, 20))

        # 交易按钮
        buy_btn = tk.Button(input_frame, text="🛒 买入", bg="#27ae60", fg="white",
                           font=("Microsoft YaHei", 10, "bold"), padx=15, pady=5)
        buy_btn.grid(row=0, column=6, padx=5)

        sell_btn = tk.Button(input_frame, text="💸 卖出", bg="#e74c3c", fg="white",
                            font=("Microsoft YaHei", 10, "bold"), padx=15, pady=5)
        sell_btn.grid(row=0, column=7, padx=5)

        # 下半部分：持仓信息
        position_panel = tk.LabelFrame(bottom_frame,
                                     text="📊 持仓信息",
                                     font=("Microsoft YaHei", 12, "bold"),
                                     bg="white",
                                     fg="#2c3e50")
        position_panel.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # 持仓表格
        position_columns = ("股票代码", "股票名称", "持仓数量", "成本价", "当前价", "盈亏金额", "盈亏比例")
        self.position_tree = ttk.Treeview(position_panel, columns=position_columns, show="headings", height=12)

        for col in position_columns:
            self.position_tree.heading(col, text=col)
            self.position_tree.column(col, width=120, anchor=tk.CENTER)

        position_scrollbar = ttk.Scrollbar(position_panel, orient="vertical", command=self.position_tree.yview)
        self.position_tree.configure(yscrollcommand=position_scrollbar.set)

        self.position_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        position_scrollbar.pack(side="right", fill="y", pady=10)

    def create_config_display(self):
        """创建配置模块显示内容"""
        # 配置选项卡
        config_notebook = ttk.Notebook(self.content_frame)
        config_notebook.pack(fill=tk.BOTH, expand=True)

        # 系统设置标签页
        system_frame = tk.Frame(config_notebook, bg="white")
        config_notebook.add(system_frame, text="🔧 系统设置")

        # 数据源配置标签页
        data_frame = tk.Frame(config_notebook, bg="white")
        config_notebook.add(data_frame, text="📊 数据源配置")

        # 交易配置标签页
        trading_config_frame = tk.Frame(config_notebook, bg="white")
        config_notebook.add(trading_config_frame, text="💰 交易配置")

        # 系统设置内容
        self.create_system_settings(system_frame)

        # 数据源配置内容
        self.create_data_settings(data_frame)

        # 交易配置内容
        self.create_trading_settings(trading_config_frame)

    def create_system_settings(self, parent):
        """创建系统设置"""
        settings_frame = tk.Frame(parent, bg="white")
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        settings_items = [
            ("自动刷新间隔", "30", "秒"),
            ("数据保存路径", "./data/", ""),
            ("日志保存天数", "30", "天"),
            ("界面主题", "默认", ""),
            ("最大显示股票数", "50", "只")
        ]

        for i, (label, default_value, unit) in enumerate(settings_items):
            frame = tk.Frame(settings_frame, bg="white")
            frame.pack(fill=tk.X, pady=8)

            tk.Label(frame, text=f"{label}:",
                    font=("Microsoft YaHei", 11),
                    bg="white", width=15, anchor="w").pack(side=tk.LEFT)

            entry = tk.Entry(frame, font=("Microsoft YaHei", 10), width=20)
            entry.insert(0, default_value)
            entry.pack(side=tk.LEFT, padx=(10, 5))

            if unit:
                tk.Label(frame, text=unit,
                        font=("Microsoft YaHei", 10),
                        bg="white", fg="#7f8c8d").pack(side=tk.LEFT)

        # 保存按钮
        save_btn = tk.Button(settings_frame, text="💾 保存设置",
                           bg="#3498db", fg="white",
                           font=("Microsoft YaHei", 10, "bold"),
                           padx=20, pady=8)
        save_btn.pack(pady=20)

    def create_data_settings(self, parent):
        """创建数据源配置"""
        data_frame = tk.Frame(parent, bg="white")
        data_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        tk.Label(data_frame, text="📊 数据源配置",
                font=("Microsoft YaHei", 14, "bold"),
                bg="white", fg="#2c3e50").pack(pady=(0, 20))

        data_sources = [
            ("东方财富人气榜", "启用", "#27ae60"),
            ("东方财富飙升榜", "启用", "#27ae60"),
            ("股吧热门话题", "启用", "#27ae60"),
            ("QMT历史数据", "启用", "#27ae60"),
            ("实时行情数据", "启用", "#27ae60")
        ]

        for source, status, color in data_sources:
            frame = tk.Frame(data_frame, bg="white")
            frame.pack(fill=tk.X, pady=5)

            tk.Label(frame, text=source,
                    font=("Microsoft YaHei", 11),
                    bg="white", width=20, anchor="w").pack(side=tk.LEFT)

            status_label = tk.Label(frame, text=status,
                                  font=("Microsoft YaHei", 10, "bold"),
                                  bg=color, fg="white",
                                  padx=10, pady=2)
            status_label.pack(side=tk.LEFT, padx=10)

            test_btn = tk.Button(frame, text="测试连接",
                               font=("Microsoft YaHei", 9),
                               bg="#95a5a6", fg="white",
                               padx=10, pady=2)
            test_btn.pack(side=tk.RIGHT)

    def create_trading_settings(self, parent):
        """创建交易配置"""
        trading_frame = tk.Frame(parent, bg="white")
        trading_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        tk.Label(trading_frame, text="💰 交易配置",
                font=("Microsoft YaHei", 14, "bold"),
                bg="white", fg="#2c3e50").pack(pady=(0, 20))

        trading_items = [
            ("默认交易数量", "100", "股"),
            ("风险控制比例", "10", "%"),
            ("止损比例", "5", "%"),
            ("止盈比例", "15", "%"),
            ("最大持仓数量", "10", "只")
        ]

        for label, default_value, unit in trading_items:
            frame = tk.Frame(trading_frame, bg="white")
            frame.pack(fill=tk.X, pady=8)

            tk.Label(frame, text=f"{label}:",
                    font=("Microsoft YaHei", 11),
                    bg="white", width=15, anchor="w").pack(side=tk.LEFT)

            entry = tk.Entry(frame, font=("Microsoft YaHei", 10), width=15)
            entry.insert(0, default_value)
            entry.pack(side=tk.LEFT, padx=(10, 5))

            tk.Label(frame, text=unit,
                    font=("Microsoft YaHei", 10),
                    bg="white", fg="#7f8c8d").pack(side=tk.LEFT)

    # ==================== 功能实现方法 ====================

    def load_initial_data(self):
        """加载初始数据"""
        self.log_message("系统启动完成", "SUCCESS")
        self.start_time_update()

    def start_time_update(self):
        """开始时间更新"""
        def update_time():
            current_time = datetime.now().strftime('%H:%M:%S')
            self.time_label.configure(text=f"🕐 {current_time}")
            self.root.after(1000, update_time)

        update_time()

    def log_message(self, message, level="INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        color_map = {
            "INFO": "#00ff00",
            "SUCCESS": "#00ff00",
            "WARNING": "#ffff00",
            "ERROR": "#ff0000"
        }

        # 安全检查：确保log_text存在
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.log_text.see(tk.END)

        # 更新状态栏（安全检查）
        if hasattr(self, 'status_label'):
            status_icons = {
                "INFO": "●",
                "SUCCESS": "✓",
                "WARNING": "⚠",
                "ERROR": "✗"
            }
            self.status_label.configure(text=f"{status_icons[level]} {message}")

        # 如果界面还没完全初始化，打印到控制台
        if not hasattr(self, 'log_text'):
            print(f"[{timestamp}] {message}")

    def load_existing_stock_data(self):
        """加载现有股票数据"""
        try:
            # 尝试加载各种数据文件
            self.load_hot_stocks_data()
            self.load_news_stocks_data()
            self.load_ai_stocks_data()
        except Exception as e:
            self.log_message(f"加载数据失败: {str(e)}", "WARNING")

    def load_hot_stocks_data(self):
        """加载热门股数据"""
        try:
            if os.path.exists('popularity.csv'):
                with open('popularity.csv', 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    count = 0
                    for i, row in enumerate(reader, 1):
                        if i <= 20:  # 只显示前20只
                            self.stock_tree.insert("", "end", values=(
                                i,
                                row.get('code', ''),
                                row.get('name', ''),
                                row.get('price', ''),
                                row.get('change', ''),
                                "热门股推荐",
                                "人气榜"
                            ))
                            count += 1

                self.stats_labels["热门股票"].configure(text=str(count))
                self.log_message(f"加载热门股数据: {count}只", "SUCCESS")

        except Exception as e:
            self.log_message(f"加载热门股数据失败: {str(e)}", "ERROR")

    def load_news_stocks_data(self):
        """加载消息面股票数据"""
        try:
            if os.path.exists('消息面选股结果.json'):
                with open('消息面选股结果.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    recommendations = data.get('recommendations', [])

                    for i, stock in enumerate(recommendations[:10], 1):  # 显示前10只
                        self.stock_tree.insert("", "end", values=(
                            f"M{i}",
                            stock.get('code', ''),
                            stock.get('name', ''),
                            "实时获取",
                            "实时获取",
                            stock.get('reason', ''),
                            "消息面"
                        ))

                self.stats_labels["消息股票"].configure(text=str(len(recommendations)))
                self.log_message(f"加载消息面数据: {len(recommendations)}只", "SUCCESS")

        except Exception as e:
            self.log_message(f"加载消息面数据失败: {str(e)}", "ERROR")

    def load_ai_stocks_data(self):
        """加载AI选股数据"""
        try:
            if os.path.exists('AI智能选股分析结果.json'):
                with open('AI智能选股分析结果.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    final_rec = data.get('final_recommendations', {})

                    # 显示强烈推荐的股票
                    strong_buy = final_rec.get('strong_buy', [])
                    for i, stock in enumerate(strong_buy[:5], 1):
                        self.stock_tree.insert("", "end", values=(
                            f"AI{i}",
                            stock.get('code', ''),
                            stock.get('name', ''),
                            "实时获取",
                            "实时获取",
                            stock.get('ai_reason', ''),
                            "AI推荐"
                        ))

                total_ai = len(strong_buy) + len(final_rec.get('buy', []))
                self.stats_labels["AI推荐"].configure(text=str(total_ai))
                self.log_message(f"加载AI推荐数据: {total_ai}只", "SUCCESS")

        except Exception as e:
            self.log_message(f"加载AI数据失败: {str(e)}", "ERROR")

    def load_technical_stocks_data(self):
        """加载技术面选股数据"""
        try:
            # 检查技术面选股结果文件
            technical_files = ['technical_stocks.csv', 'selected_stocks.csv', 'stock_analysis.csv']

            for filename in technical_files:
                if os.path.exists(filename):
                    with open(filename, 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        count = 0
                        for i, row in enumerate(reader, 1):
                            if i <= 15:  # 显示前15只
                                self.stock_tree.insert("", "end", values=(
                                    f"T{i}",
                                    row.get('code', row.get('股票代码', '')),
                                    row.get('name', row.get('股票名称', '')),
                                    row.get('price', row.get('当前价格', '实时获取')),
                                    row.get('change', row.get('涨跌幅', '实时获取')),
                                    row.get('reason', row.get('推荐理由', '技术面推荐')),
                                    "技术面"
                                ))
                                count += 1

                    self.log_message(f"加载技术面数据: {count}只", "SUCCESS")
                    break
            else:
                self.log_message("未找到技术面选股结果文件", "WARNING")

        except Exception as e:
            self.log_message(f"加载技术面数据失败: {str(e)}", "ERROR")

    # ==================== 快捷按钮功能实现 ====================

    def run_hot_stocks(self):
        """运行热门股选股"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在爬取热门股数据...", "INFO")

                # 清空现有选股数据
                if hasattr(self, 'stock_tree'):
                    for item in self.stock_tree.get_children():
                        self.stock_tree.delete(item)

                # 运行后台采集
                result = subprocess.run([sys.executable, "后台采集.py"],
                                      cwd=os.getcwd(), capture_output=True, text=True)

                if result.returncode == 0:
                    self.log_message("热门股数据采集完成", "SUCCESS")
                    self.load_hot_stocks_data()
                    # 切换到选股模块显示结果
                    if self.current_module != "选股":
                        self.switch_module("选股")
                else:
                    self.log_message("热门股数据采集失败", "ERROR")

            except Exception as e:
                self.log_message(f"热门股选股失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def run_news_stocks(self):
        """运行消息面选股"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在进行消息面选股...", "INFO")

                # 清空现有选股数据
                if hasattr(self, 'stock_tree'):
                    for item in self.stock_tree.get_children():
                        self.stock_tree.delete(item)

                # 获取脚本文件的绝对路径
                script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "消息面智能选股.py")
                
                # 运行消息面选股
                result = subprocess.run([sys.executable, script_path],
                                      cwd=os.path.dirname(script_path), capture_output=True, text=True)

                if result.returncode == 0:
                    self.log_message("消息面选股完成", "SUCCESS")
                    self.load_news_stocks_data()
                    # 切换到选股模块显示结果
                    if self.current_module != "选股":
                        self.switch_module("选股")
                else:
                    self.log_message("消息面选股失败", "ERROR")

            except Exception as e:
                self.log_message(f"消息面选股失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def run_technical_stocks(self):
        """运行技术面选股"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在进行技术面选股...", "INFO")

                # 检查是否有技术面选股脚本
                if os.path.exists("智能选股系统.py"):
                    result = subprocess.run([sys.executable, "智能选股系统.py"],
                                          cwd=os.getcwd(), capture_output=True, text=True)

                    if result.returncode == 0:
                        self.log_message("技术面选股完成", "SUCCESS")
                        # 切换到选股模块显示结果
                        if self.current_module != "选股":
                            self.switch_module("选股")
                        self.load_technical_stocks_data()
                    else:
                        self.log_message("技术面选股失败", "ERROR")
                else:
                    self.log_message("技术面选股功能开发中...", "WARNING")
                    messagebox.showinfo("提示", "技术面选股功能正在开发中，敬请期待！\n\n将基于QMT历史数据进行技术指标分析")

            except Exception as e:
                self.log_message(f"技术面选股失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def run_ai_analysis(self):
        """运行AI综合分析"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在进行AI智能分析...", "INFO")

                # 检查前置条件
                required_files = [
                    ('popularity.csv', '人气榜数据'),
                    ('消息面选股结果.json', '消息面选股结果')
                ]

                missing_files = []
                for filename, description in required_files:
                    if not os.path.exists(filename):
                        missing_files.append(f"• {description} ({filename})")

                if missing_files:
                    error_msg = "缺少必要的数据文件:\n\n" + "\n".join(missing_files) + "\n\n请先运行相应的选股功能"
                    self.log_message("AI分析失败: 缺少数据文件", "ERROR")
                    messagebox.showerror("数据文件缺失", error_msg)
                    return

                # 运行AI分析
                result = subprocess.run([sys.executable, "AI智能选股分析.py"],
                                      cwd=os.getcwd(), capture_output=True, text=True)

                if result.returncode == 0:
                    self.log_message("AI智能分析完成", "SUCCESS")
                    self.load_ai_stocks_data()

                    # 切换到结果模块显示分析结果
                    if self.current_module != "结果":
                        self.switch_module("结果")

                    # 加载AI分析结果到结果模块
                    self.load_ai_analysis_results()

                else:
                    self.log_message("AI智能分析失败", "ERROR")

            except Exception as e:
                self.log_message(f"AI分析失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def generate_report(self):
        """生成分析报告"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在生成分析报告...", "INFO")

                # 检查是否有AI分析结果
                if os.path.exists('AI智能选股分析结果.json'):
                    # 生成AI选股报告
                    result = subprocess.run([sys.executable, "AI选股报告生成器.py"],
                                          cwd=os.getcwd(), capture_output=True, text=True)

                    if result.returncode == 0:
                        self.log_message("AI选股报告生成完成", "SUCCESS")

                # 检查是否有消息面结果
                if os.path.exists('消息面选股结果.json'):
                    # 生成消息面报告
                    result = subprocess.run([sys.executable, "消息面选股报告生成器.py"],
                                          cwd=os.getcwd(), capture_output=True, text=True)

                    if result.returncode == 0:
                        self.log_message("消息面选股报告生成完成", "SUCCESS")

                # 切换到结果模块
                if self.current_module != "结果":
                    self.switch_module("结果")

                self.log_message("所有报告生成完成", "SUCCESS")

                # 询问是否打开报告
                if messagebox.askyesno("打开报告", "报告生成完成！是否打开HTML报告查看？"):
                    self.open_web_reports()

            except Exception as e:
                self.log_message(f"生成报告失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def open_simulation_trading(self):
        """打开模拟交易"""
        self.log_message("切换到交易模块", "INFO")
        if self.current_module != "交易":
            self.switch_module("交易")

        # 显示模拟交易说明
        messagebox.showinfo("模拟交易",
                           "模拟交易功能\n\n"
                           "• 使用虚拟资金进行交易练习\n"
                           "• 验证投资策略效果\n"
                           "• 无真实资金风险\n"
                           "• 基于实时行情数据\n\n"
                           "请在交易模块中进行操作")

    def load_ai_analysis_results(self):
        """加载AI分析结果到结果模块"""
        try:
            if hasattr(self, 'rec_tree') and os.path.exists('AI智能选股分析结果.json'):
                # 清空现有数据
                for item in self.rec_tree.get_children():
                    self.rec_tree.delete(item)

                with open('AI智能选股分析结果.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    final_rec = data.get('final_recommendations', {})

                    # 添加强烈推荐
                    for i, stock in enumerate(final_rec.get('strong_buy', []), 1):
                        self.rec_tree.insert("", "end", values=(
                            "🔥 强烈推荐",
                            i,
                            stock.get('code', ''),
                            stock.get('name', ''),
                            f"{stock.get('ai_score', 0):.2f}",
                            stock.get('risk_level', ''),
                            stock.get('ai_reason', '')
                        ))

                    # 添加一般推荐
                    for i, stock in enumerate(final_rec.get('buy', []), 1):
                        self.rec_tree.insert("", "end", values=(
                            "⭐ 建议买入",
                            i,
                            stock.get('code', ''),
                            stock.get('name', ''),
                            f"{stock.get('ai_score', 0):.2f}",
                            stock.get('risk_level', ''),
                            stock.get('ai_reason', '')
                        ))

                    # 添加可以考虑
                    for i, stock in enumerate(final_rec.get('consider', []), 1):
                        self.rec_tree.insert("", "end", values=(
                            "👀 可以考虑",
                            i,
                            stock.get('code', ''),
                            stock.get('name', ''),
                            f"{stock.get('ai_score', 0):.2f}",
                            stock.get('risk_level', ''),
                            stock.get('ai_reason', '')
                        ))

                self.log_message("AI分析结果加载完成", "SUCCESS")

        except Exception as e:
            self.log_message(f"加载AI分析结果失败: {str(e)}", "ERROR")

    def open_web_reports(self):
        """打开Web报告"""
        try:
            import webbrowser

            # 打开AI选股报告
            if os.path.exists("AI智能选股分析报告.html"):
                webbrowser.open(f"file:///{os.path.abspath('AI智能选股分析报告.html')}")
                self.log_message("已打开AI选股分析报告", "INFO")

            # 打开消息面选股报告
            if os.path.exists("消息面选股分析报告.html"):
                webbrowser.open(f"file:///{os.path.abspath('消息面选股分析报告.html')}")
                self.log_message("已打开消息面选股报告", "INFO")

        except Exception as e:
            self.log_message(f"打开Web报告失败: {str(e)}", "ERROR")

    # ==================== 新增功能方法 ====================

    def start_web_visualization(self):
        """启动Web可视化"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在启动Web可视化服务...", "INFO")

                # 检查可视化主程序
                if os.path.exists("可视化主程序.py"):
                    # 在后台启动可视化程序
                    subprocess.Popen([sys.executable, "可视化主程序.py"],
                                   cwd=os.getcwd())

                    self.log_message("可视化程序已启动", "SUCCESS")

                elif os.path.exists("web_visualization.py"):
                    # 在后台启动Web服务
                    subprocess.Popen([sys.executable, "web_visualization.py"],
                                   cwd=os.getcwd())

                    self.log_message("Web可视化服务已启动", "SUCCESS")

                    # 等待服务启动
                    time.sleep(3)

                    # 打开浏览器
                    import webbrowser
                    webbrowser.open("http://localhost:8080")
                    self.log_message("已打开Web可视化页面", "INFO")

                else:
                    # 直接打开HTML报告
                    self.log_message("启动HTML报告查看...", "INFO")
                    self.open_web_reports()

            except Exception as e:
                self.log_message(f"启动Web可视化失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def show_all_results(self):
        """显示所有结果"""
        self.log_message("切换到结果模块", "INFO")
        if self.current_module != "结果":
            self.switch_module("结果")

        # 加载所有可用的结果数据
        self.load_all_available_results()

        messagebox.showinfo("结果查看",
                           "结果模块功能\n\n"
                           "• 📈 推荐列表 - 查看所有推荐股票\n"
                           "• 📊 分析报告 - 详细分析报告\n"
                           "• 🎯 投资建议 - AI投资建议\n"
                           "• 📱 实时监控 - 实时价格监控\n\n"
                           "请在结果模块中查看详细信息")

    def open_system_config(self):
        """打开系统配置"""
        self.log_message("切换到配置模块", "INFO")
        if self.current_module != "配置":
            self.switch_module("配置")

        messagebox.showinfo("系统配置",
                           "系统配置功能\n\n"
                           "• 🔧 系统设置 - 基本参数配置\n"
                           "• 📊 数据源配置 - 数据源管理\n"
                           "• 💰 交易配置 - 交易参数设置\n"
                           "• 📋 日志管理 - 系统日志配置\n\n"
                           "请在配置模块中进行设置")

    def load_all_available_results(self):
        """加载所有可用的结果数据"""
        try:
            # 检查并加载各种结果文件
            result_files = {
                'popularity.csv': '热门股数据',
                'soaring.csv': '飙升股数据',
                '消息面选股结果.json': '消息面选股结果',
                'AI智能选股分析结果.json': 'AI分析结果',
                '消息面推荐股票.csv': '消息面推荐',
                'AI最终购买建议.csv': 'AI购买建议'
            }

            available_files = []
            for filename, description in result_files.items():
                if os.path.exists(filename):
                    available_files.append(f"✅ {description}")
                else:
                    available_files.append(f"❌ {description}")

            # 在分析文本中显示可用文件
            if hasattr(self, 'analysis_text'):
                self.analysis_text.configure(state=tk.NORMAL)
                self.analysis_text.delete(1.0, tk.END)

                content = "📋 系统数据文件状态\n\n"
                content += "\n".join(available_files)
                content += "\n\n💡 使用说明:\n"
                content += "• ✅ 表示文件存在且可用\n"
                content += "• ❌ 表示文件不存在，需要先运行相应功能\n"
                content += "• 点击左侧按钮执行相应的选股或分析功能\n"
                content += "• 生成的结果会自动显示在对应模块中\n"

                self.analysis_text.insert(tk.END, content)
                self.analysis_text.configure(state=tk.DISABLED)

            self.log_message("结果数据状态检查完成", "SUCCESS")

        except Exception as e:
            self.log_message(f"加载结果数据失败: {str(e)}", "ERROR")

    def run_comprehensive_stock_analysis(self):
        """运行综合股票分析"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("开始综合股票分析流程...", "INFO")

                # 1. 先运行热门股选股
                self.log_message("第1步: 热门股数据采集", "INFO")
                result1 = subprocess.run([sys.executable, "后台采集.py"],
                                       cwd=os.getcwd(), capture_output=True, text=True)

                if result1.returncode == 0:
                    self.log_message("✅ 热门股数据采集完成", "SUCCESS")
                else:
                    self.log_message("❌ 热门股数据采集失败", "ERROR")
                    return

                # 2. 运行消息面选股
                self.log_message("第2步: 消息面选股分析", "INFO")
                script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "消息面智能选股.py")
                result2 = subprocess.run([sys.executable, script_path],
                                       cwd=os.path.dirname(script_path), capture_output=True, text=True)

                if result2.returncode == 0:
                    self.log_message("✅ 消息面选股完成", "SUCCESS")
                else:
                    self.log_message("❌ 消息面选股失败", "ERROR")
                    return

                # 3. 运行AI智能分析
                self.log_message("第3步: AI智能分析", "INFO")
                result3 = subprocess.run([sys.executable, "AI智能选股分析.py"],
                                       cwd=os.getcwd(), capture_output=True, text=True)

                if result3.returncode == 0:
                    self.log_message("✅ AI智能分析完成", "SUCCESS")
                else:
                    self.log_message("❌ AI智能分析失败", "ERROR")
                    return

                # 4. 生成报告
                self.log_message("第4步: 生成分析报告", "INFO")

                # 生成AI报告
                subprocess.run([sys.executable, "AI选股报告生成器.py"],
                             cwd=os.getcwd(), capture_output=True, text=True)

                # 生成消息面报告
                subprocess.run([sys.executable, "消息面选股报告生成器.py"],
                             cwd=os.getcwd(), capture_output=True, text=True)

                self.log_message("✅ 分析报告生成完成", "SUCCESS")

                # 5. 加载结果到界面
                self.log_message("第5步: 加载结果到界面", "INFO")
                self.load_existing_stock_data()
                self.load_ai_analysis_results()

                self.log_message("🎉 综合股票分析流程完成!", "SUCCESS")

                # 切换到结果模块显示
                if self.current_module != "结果":
                    self.switch_module("结果")

                # 询问是否打开报告
                if messagebox.askyesno("分析完成",
                                     "综合股票分析已完成！\n\n"
                                     "✅ 热门股数据采集\n"
                                     "✅ 消息面选股分析\n"
                                     "✅ AI智能分析\n"
                                     "✅ 分析报告生成\n\n"
                                     "是否打开HTML报告查看详细结果？"):
                    self.open_web_reports()

            except Exception as e:
                self.log_message(f"综合分析失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    # ==================== 分析模块功能实现 ====================

    def show_analysis_welcome_message(self):
        """显示分析模块欢迎信息"""
        welcome_text = """
🔍 智能分析模块

欢迎使用AI智能分析功能！

📊 可用分析功能：
• 🤖 AI智能分析 - 综合多维度数据进行智能评分
• 📊 技术指标分析 - 基于历史数据进行技术分析
• 💭 情感分析 - 分析市场情绪和话题热度
• ⚖️ 风险评估 - 评估投资风险等级

💡 使用方法：
1. 先在选股模块获取股票数据
2. 点击上方按钮进行相应分析
3. 分析结果将在下方显示

📋 分析结果包含：
• 详细的分析报告
• 投资建议和风险提示
• 数据来源和分析依据

🎯 提示：建议先运行"一键分析"获取完整数据后再进行详细分析
        """

        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(tk.END, welcome_text)

    def run_ai_analysis_detailed(self):
        """运行详细AI分析"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在进行详细AI分析...", "INFO")

                # 更新分析文本
                self.analysis_text.delete(1.0, tk.END)
                self.analysis_text.insert(tk.END, "🤖 正在进行AI智能分析...\n\n")
                self.analysis_text.update()

                # 检查数据文件
                required_files = ['popularity.csv', '消息面选股结果.json']
                missing_files = [f for f in required_files if not os.path.exists(f)]

                if missing_files:
                    error_text = f"❌ 缺少必要数据文件:\n"
                    for f in missing_files:
                        error_text += f"  • {f}\n"
                    error_text += "\n💡 请先运行相应的选股功能获取数据"

                    self.analysis_text.insert(tk.END, error_text)
                    self.log_message("AI分析失败: 缺少数据文件", "ERROR")
                    return

                # 运行AI分析
                result = subprocess.run([sys.executable, "AI智能选股分析.py"],
                                      cwd=os.getcwd(), capture_output=True, text=True)

                if result.returncode == 0:
                    self.log_message("AI智能分析完成", "SUCCESS")
                    self.display_ai_analysis_results()
                else:
                    self.analysis_text.insert(tk.END, f"❌ AI分析失败\n错误信息: {result.stderr}")
                    self.log_message("AI智能分析失败", "ERROR")

            except Exception as e:
                self.analysis_text.insert(tk.END, f"❌ AI分析异常: {str(e)}")
                self.log_message(f"AI分析异常: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def run_technical_analysis(self):
        """运行技术指标分析"""
        self.analysis_text.delete(1.0, tk.END)

        analysis_text = """
📊 技术指标分析

正在分析技术指标...

📈 分析维度：
• 趋势指标：MA、MACD、布林带
• 震荡指标：RSI、KDJ、威廉指标
• 成交量指标：OBV、成交量比率
• 动量指标：ROC、MTM

💡 技术分析基于QMT历史数据，包含：
• K线形态识别
• 支撑阻力位分析
• 买卖信号判断
• 风险控制建议

⚠️ 注意：技术分析仅供参考，需结合基本面分析
        """

        self.analysis_text.insert(tk.END, analysis_text)
        self.log_message("技术指标分析功能展示", "INFO")

    def run_sentiment_analysis(self):
        """运行情感分析"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在进行情感分析...", "INFO")

                self.analysis_text.delete(1.0, tk.END)
                self.analysis_text.insert(tk.END, "💭 正在分析市场情绪...\n\n")
                self.analysis_text.update()

                # 检查消息面数据
                if os.path.exists('消息面选股结果.json'):
                    with open('消息面选股结果.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    hot_topics = data.get('hot_topics', [])
                    recommendations = data.get('recommendations', [])

                    # 分析情感分布
                    sentiment_analysis = self.analyze_sentiment_distribution(hot_topics, recommendations)

                    self.analysis_text.delete(1.0, tk.END)
                    self.analysis_text.insert(tk.END, sentiment_analysis)

                    self.log_message("情感分析完成", "SUCCESS")
                else:
                    self.analysis_text.insert(tk.END, "❌ 未找到消息面数据\n请先运行消息面选股功能")
                    self.log_message("情感分析失败: 缺少数据", "ERROR")

            except Exception as e:
                self.analysis_text.insert(tk.END, f"❌ 情感分析失败: {str(e)}")
                self.log_message(f"情感分析失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def run_risk_assessment(self):
        """运行风险评估"""
        def run_in_thread():
            try:
                self.progress.start()
                self.log_message("正在进行风险评估...", "INFO")

                self.analysis_text.delete(1.0, tk.END)
                self.analysis_text.insert(tk.END, "⚖️ 正在评估投资风险...\n\n")
                self.analysis_text.update()

                # 检查AI分析结果
                if os.path.exists('AI智能选股分析结果.json'):
                    with open('AI智能选股分析结果.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    risk_analysis = self.analyze_investment_risks(data)

                    self.analysis_text.delete(1.0, tk.END)
                    self.analysis_text.insert(tk.END, risk_analysis)

                    self.log_message("风险评估完成", "SUCCESS")
                else:
                    self.analysis_text.insert(tk.END, "❌ 未找到AI分析数据\n请先运行AI智能分析功能")
                    self.log_message("风险评估失败: 缺少数据", "ERROR")

            except Exception as e:
                self.analysis_text.insert(tk.END, f"❌ 风险评估失败: {str(e)}")
                self.log_message(f"风险评估失败: {str(e)}", "ERROR")
            finally:
                self.progress.stop()

        threading.Thread(target=run_in_thread, daemon=True).start()

    def display_ai_analysis_results(self):
        """显示AI分析结果"""
        try:
            if os.path.exists('AI智能选股分析结果.json'):
                with open('AI智能选股分析结果.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)

                final_rec = data.get('final_recommendations', {})
                data_sources = data.get('data_sources', {})

                analysis_text = f"""
🤖 AI智能分析结果

📊 数据概览：
• 热门股数据: {data_sources.get('hot_stocks_count', 0)} 只
• 消息面数据: {data_sources.get('news_stocks_count', 0)} 只
• 合并分析: {data_sources.get('combined_count', 0)} 只
• 高分股票: {final_rec.get('high_score_count', 0)} 只

🎯 AI推荐分布：
• 🔥 强烈建议买入: {len(final_rec.get('strong_buy', []))} 只
• ⭐ 建议买入: {len(final_rec.get('buy', []))} 只
• 👀 可以考虑: {len(final_rec.get('consider', []))} 只

💡 AI分析方法：
• 综合评分算法: 热门股得分(40%) + 消息面得分(35%) + 数据源加分(25%)
• 风险评估: 基于AI得分和数据完整性的5级风险评估
• 投资建议: 85+强烈推荐, 75+建议买入, 65+可以考虑
• 双重验证: 同时出现在热门股和消息面的股票获得额外加分

📋 详细结果请查看"结果"模块
                """

                self.analysis_text.delete(1.0, tk.END)
                self.analysis_text.insert(tk.END, analysis_text)

        except Exception as e:
            self.analysis_text.insert(tk.END, f"❌ 显示AI分析结果失败: {str(e)}")

    def analyze_sentiment_distribution(self, hot_topics, recommendations):
        """分析情感分布"""
        try:
            positive_count = 0
            negative_count = 0
            neutral_count = 0

            # 分析推荐股票的情感分布
            for rec in recommendations:
                sentiment_score = rec.get('sentiment_score', 0)
                if sentiment_score > 0.5:
                    positive_count += 1
                elif sentiment_score < -0.5:
                    negative_count += 1
                else:
                    neutral_count += 1

            total = len(recommendations)

            analysis_text = f"""
💭 市场情感分析结果

📊 情感分布统计：
• 😊 积极情感: {positive_count} 只 ({positive_count/total*100:.1f}%)
• 😐 中性情感: {neutral_count} 只 ({neutral_count/total*100:.1f}%)
• 😟 消极情感: {negative_count} 只 ({negative_count/total*100:.1f}%)

📈 话题热度分析：
• 总话题数: {len(hot_topics)} 个
• 平均热度: {sum([t.get('热度', 0) for t in hot_topics])/len(hot_topics):.1f}
• 高热度话题: {len([t for t in hot_topics if t.get('热度', 0) > 100])} 个

💡 市场情绪判断：
"""

            if positive_count > negative_count * 1.5:
                analysis_text += "🟢 市场情绪偏向乐观，投资者信心较强"
            elif negative_count > positive_count * 1.5:
                analysis_text += "🔴 市场情绪偏向悲观，建议谨慎投资"
            else:
                analysis_text += "🟡 市场情绪相对平衡，可适度参与"

            return analysis_text

        except Exception as e:
            return f"❌ 情感分析处理失败: {str(e)}"

    def analyze_investment_risks(self, data):
        """分析投资风险"""
        try:
            final_rec = data.get('final_recommendations', {})
            complete_analysis = data.get('complete_analysis', [])

            # 风险等级统计
            risk_stats = {}
            for stock in complete_analysis:
                risk_level = stock.get('risk_level', '未知风险')
                risk_stats[risk_level] = risk_stats.get(risk_level, 0) + 1

            # 高分股票风险分析
            high_score_stocks = [s for s in complete_analysis if s.get('ai_score', 0) >= 70]
            low_risk_high_score = [s for s in high_score_stocks if '低风险' in s.get('risk_level', '')]

            analysis_text = f"""
⚖️ 投资风险评估报告

📊 整体风险分布：
"""

            for risk_level, count in risk_stats.items():
                percentage = count / len(complete_analysis) * 100
                analysis_text += f"• {risk_level}: {count} 只 ({percentage:.1f}%)\n"

            analysis_text += f"""

🎯 高分股票风险分析：
• 高分股票总数: {len(high_score_stocks)} 只
• 低风险高分股票: {len(low_risk_high_score)} 只
• 风险收益比: {len(low_risk_high_score)/len(high_score_stocks)*100:.1f}%

💡 风险控制建议：
• 🟢 优先选择低风险高分股票
• 🟡 中等风险股票适量配置
• 🔴 高风险股票谨慎参与
• 📊 建议分散投资，控制单只股票仓位

⚠️ 风险提示：
• 股市有风险，投资需谨慎
• 本分析仅供参考，不构成投资建议
• 请根据个人风险承受能力做决策
• 建议结合更多信息进行综合判断
            """

            return analysis_text

        except Exception as e:
            return f"❌ 风险分析处理失败: {str(e)}"

    def load_results_data(self):
        """加载结果数据到结果模块"""
        try:
            # 加载推荐列表数据
            if hasattr(self, 'rec_tree'):
                self.load_ai_analysis_results()

            # 加载分析报告
            if hasattr(self, 'report_text'):
                self.load_analysis_report()

        except Exception as e:
            self.log_message(f"加载结果数据失败: {str(e)}", "ERROR")

    def load_analysis_report(self):
        """加载分析报告内容"""
        try:
            report_content = """
📊 智股投分析报告

🎯 系统概述：
智股投是一个综合性的股票投资分析系统，集成了多种选股策略和AI智能分析功能。

📈 主要功能：
• 🔥 热门股选股 - 基于东方财富人气榜和飙升榜
• 📰 消息面选股 - 基于股吧热门话题的情感分析
• 🤖 AI智能分析 - 多维度综合评分和风险评估
• 📊 可视化报告 - 生成HTML格式的详细报告

💡 分析方法：
1. 数据采集：从东方财富等平台获取实时数据
2. 情感分析：对股吧话题进行自然语言处理
3. AI评分：使用机器学习算法进行综合评分
4. 风险评估：基于多个维度评估投资风险

📋 使用建议：
• 建议使用"一键分析"功能获取完整分析结果
• 结合多个维度的分析结果做投资决策
• 注意风险控制，分散投资
• 定期更新数据，跟踪市场变化

⚠️ 免责声明：
本系统提供的分析结果仅供参考，不构成投资建议。
股市有风险，投资需谨慎。请根据个人情况做出投资决策。
            """

            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(tk.END, report_content)

        except Exception as e:
            self.log_message(f"加载分析报告失败: {str(e)}", "ERROR")

def main():
    """主函数"""
    root = tk.Tk()
    app = StockInvestmentSystemNew(root)
    root.mainloop()

if __name__ == "__main__":
    main()

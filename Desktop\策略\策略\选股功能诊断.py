#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选股功能诊断工具
诊断GUI中选股功能失败的原因
"""

import os
import sys
import subprocess
import traceback
from datetime import datetime

def diagnose_selection_issue():
    """诊断选股功能问题"""
    print("🔧 选股功能诊断工具")
    print("=" * 50)
    
    # 1. 检查当前工作目录
    print(f"📁 当前工作目录: {os.getcwd()}")
    
    # 2. 检查必要文件
    print(f"\n📋 检查必要文件:")
    required_files = [
        '智能选股系统.py',
        '选股报告生成器.py', 
        '行业板块分类.py',
        'popularity.csv',
        'soaring.csv'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"  ✅ {file}: {size/1024:.1f} KB")
        else:
            print(f"  ❌ {file}: 不存在")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ 缺少文件: {', '.join(missing_files)}")
        return False
    
    # 3. 测试智能选股系统
    print(f"\n🎯 测试智能选股系统:")
    try:
        print("  正在运行智能选股系统...")
        result = subprocess.run(
            [sys.executable, '智能选股系统.py'], 
            capture_output=True, 
            text=True, 
            encoding='utf-8',
            timeout=60
        )
        
        print(f"  返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("  ✅ 智能选股系统运行成功")
            
            # 检查输出文件
            if os.path.exists('选股结果.json'):
                size = os.path.getsize('选股结果.json')
                print(f"  ✅ 选股结果.json: {size/1024:.1f} KB")
            else:
                print("  ❌ 选股结果.json 未生成")
                
        else:
            print("  ❌ 智能选股系统运行失败")
            if result.stdout:
                print(f"  标准输出: {result.stdout[:500]}")
            if result.stderr:
                print(f"  错误输出: {result.stderr[:500]}")
                
    except subprocess.TimeoutExpired:
        print("  ⏰ 智能选股系统运行超时")
    except Exception as e:
        print(f"  ❌ 运行智能选股系统时出错: {str(e)}")
        traceback.print_exc()
    
    # 4. 测试选股报告生成器
    print(f"\n📋 测试选股报告生成器:")
    try:
        print("  正在运行选股报告生成器...")
        result = subprocess.run(
            [sys.executable, '选股报告生成器.py'], 
            capture_output=True, 
            text=True, 
            encoding='utf-8',
            timeout=30
        )
        
        print(f"  返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("  ✅ 选股报告生成器运行成功")
            
            if os.path.exists('智能选股分析报告.html'):
                size = os.path.getsize('智能选股分析报告.html')
                print(f"  ✅ 智能选股分析报告.html: {size/1024:.1f} KB")
            else:
                print("  ❌ HTML报告未生成")
                
        else:
            print("  ❌ 选股报告生成器运行失败")
            if result.stdout:
                print(f"  标准输出: {result.stdout[:500]}")
            if result.stderr:
                print(f"  错误输出: {result.stderr[:500]}")
                
    except subprocess.TimeoutExpired:
        print("  ⏰ 选股报告生成器运行超时")
    except Exception as e:
        print(f"  ❌ 运行选股报告生成器时出错: {str(e)}")
        traceback.print_exc()
    
    # 5. 模拟GUI调用
    print(f"\n🖥️ 模拟GUI调用:")
    try:
        print("  模拟GUI中的选股流程...")
        
        # 检查数据文件（模拟GUI检查）
        if not os.path.exists('popularity.csv') or not os.path.exists('soaring.csv'):
            print("  ❌ 数据文件检查失败")
            return False
        
        print("  ✅ 数据文件检查通过")
        
        # 模拟subprocess调用（GUI中的方式）
        result = subprocess.run(
            [sys.executable, '智能选股系统.py'], 
            capture_output=True, 
            text=True, 
            encoding='utf-8'
        )
        
        if result.returncode == 0:
            print("  ✅ 模拟GUI调用成功")
            
            # 模拟报告生成
            report_result = subprocess.run(
                [sys.executable, '选股报告生成器.py'], 
                capture_output=True, 
                text=True, 
                encoding='utf-8'
            )
            
            if report_result.returncode == 0:
                print("  ✅ 模拟报告生成成功")
            else:
                print("  ❌ 模拟报告生成失败")
                print(f"  错误: {report_result.stderr[:200]}")
        else:
            print("  ❌ 模拟GUI调用失败")
            print(f"  错误: {result.stderr[:200]}")
            
    except Exception as e:
        print(f"  ❌ 模拟GUI调用时出错: {str(e)}")
        traceback.print_exc()
    
    # 6. 检查Python环境
    print(f"\n🐍 Python环境检查:")
    print(f"  Python版本: {sys.version}")
    print(f"  Python路径: {sys.executable}")
    
    # 检查必要模块
    required_modules = ['csv', 'json', 'os', 'subprocess', 'datetime']
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}: 可用")
        except ImportError:
            print(f"  ❌ {module}: 不可用")
    
    return True

def create_simple_test():
    """创建简单的选股测试"""
    print(f"\n🧪 创建简单选股测试:")
    
    test_code = '''
import subprocess
import sys
import os

print("开始简单选股测试...")

# 检查文件
if os.path.exists('popularity.csv') and os.path.exists('soaring.csv'):
    print("数据文件存在")
    
    # 运行选股
    try:
        result = subprocess.run([sys.executable, '智能选股系统.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("选股成功")
        else:
            print(f"选股失败: {result.stderr}")
            
    except Exception as e:
        print(f"选股出错: {str(e)}")
else:
    print("数据文件不存在")
'''
    
    try:
        with open('简单选股测试.py', 'w', encoding='utf-8') as f:
            f.write(test_code)
        
        print("  ✅ 简单选股测试文件已创建")
        
        # 运行简单测试
        result = subprocess.run([sys.executable, '简单选股测试.py'], 
                              capture_output=True, text=True, encoding='utf-8')
        
        print(f"  测试结果: {result.stdout}")
        if result.stderr:
            print(f"  测试错误: {result.stderr}")
            
    except Exception as e:
        print(f"  ❌ 创建简单测试失败: {str(e)}")

def main():
    """主函数"""
    print("🔧 选股功能诊断工具")
    print("诊断GUI中选股功能失败的原因")
    print("=" * 60)
    
    try:
        success = diagnose_selection_issue()
        
        if success:
            create_simple_test()
            
            print(f"\n📊 诊断总结:")
            print(f"  📅 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"  🎯 如果所有测试都通过，问题可能在GUI界面的异常处理")
            print(f"  💡 建议检查GUI日志或添加更详细的错误信息")
            
            print(f"\n🔧 可能的解决方案:")
            print(f"  1. 检查GUI界面的错误日志")
            print(f"  2. 确保GUI运行时的工作目录正确")
            print(f"  3. 检查GUI中的异常处理逻辑")
            print(f"  4. 尝试在GUI中添加更详细的调试信息")
        else:
            print(f"\n❌ 诊断发现问题，请根据上述信息修复")
            
    except Exception as e:
        print(f"\n❌ 诊断过程出错: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()

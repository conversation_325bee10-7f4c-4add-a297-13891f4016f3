#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终GUI系统
股票数据采集系统的完整集成界面，所有操作和结果都在同一界面
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import subprocess
import sys
import os
import threading
import webbrowser
from datetime import datetime
import csv

class FinalStockGUI:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.create_widgets()
        self.load_initial_data()
        
    def setup_window(self):
        """设置窗口"""
        self.root.title("🚀 股票数据采集系统 - 集成控制台")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        self.root.configure(bg="#f0f0f0")
        
        # 窗口居中
        self.center_window()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 1200
        height = 800
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = tk.Frame(self.root, bg="#f0f0f0")
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部标题
        self.create_header(main_container)
        
        # 主要内容区域
        content_frame = tk.Frame(main_container, bg="#f0f0f0")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 左侧控制面板
        self.create_control_panel(content_frame)
        
        # 右侧结果显示区域
        self.create_result_area(content_frame)
        
        # 底部状态栏
        self.create_status_bar(main_container)
    
    def create_header(self, parent):
        """创建顶部标题区域"""
        header_frame = tk.Frame(parent, bg="#2c3e50", height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame,
                              text="🚀 股票数据采集系统 - 集成控制台",
                              font=("Microsoft YaHei", 18, "bold"),
                              bg="#2c3e50",
                              fg="white")
        title_label.pack(side=tk.LEFT, padx=20, pady=15)
        
        version_label = tk.Label(header_frame,
                                text=f"v2.0 | {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                                font=("Microsoft YaHei", 10),
                                bg="#2c3e50",
                                fg="#bdc3c7")
        version_label.pack(side=tk.RIGHT, padx=20, pady=15)
    
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_frame = tk.Frame(parent, bg="#ecf0f1", width=280)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 控制面板标题
        tk.Label(control_frame,
                text="📋 功能控制面板",
                font=("Microsoft YaHei", 14, "bold"),
                bg="#ecf0f1",
                fg="#2c3e50").pack(pady=(15, 20))
        
        # 功能按钮
        buttons = [
            ("🔇 后台数据采集", self.run_background_collection, "#3498db"),
            ("📊 获取股票名称", self.get_stock_names, "#9b59b6"),
            ("📈 获取涨跌幅数据", self.get_price_changes, "#e67e22"),
            ("📄 生成分析报告", self.generate_report, "#27ae60"),
            ("🌐 启动数据服务器", self.start_server, "#f39c12"),
            ("🖥️ 打开可视化界面", self.open_web_interface, "#1abc9c"),
            ("📁 刷新数据显示", self.refresh_data, "#34495e"),
            ("⚙️ 系统状态检查", self.check_system_status, "#95a5a6")
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(control_frame,
                           text=text,
                           command=command,
                           font=("Microsoft YaHei", 10, "bold"),
                           bg=color,
                           fg="white",
                           relief="raised",
                           bd=2,
                           padx=15,
                           pady=8,
                           cursor="hand2",
                           width=22)
            btn.pack(pady=5, padx=15, fill=tk.X)
        
        # 系统状态显示
        self.create_system_status(control_frame)
    
    def create_system_status(self, parent):
        """创建系统状态显示"""
        status_frame = tk.LabelFrame(parent, 
                                    text="📊 系统状态",
                                    font=("Microsoft YaHei", 10, "bold"),
                                    bg="#ecf0f1",
                                    fg="#2c3e50")
        status_frame.pack(fill=tk.X, padx=15, pady=(20, 10))
        
        # 状态标签
        tk.Label(status_frame, text="Python:", bg="#ecf0f1", font=("Microsoft YaHei", 9)).pack(anchor=tk.W, padx=10, pady=2)
        self.python_label = tk.Label(status_frame, text="检查中...", bg="#ecf0f1", fg="#27ae60", font=("Microsoft YaHei", 9))
        self.python_label.pack(anchor=tk.W, padx=20)
        
        tk.Label(status_frame, text="依赖包:", bg="#ecf0f1", font=("Microsoft YaHei", 9)).pack(anchor=tk.W, padx=10, pady=2)
        self.deps_label = tk.Label(status_frame, text="检查中...", bg="#ecf0f1", fg="#27ae60", font=("Microsoft YaHei", 9))
        self.deps_label.pack(anchor=tk.W, padx=20)
        
        tk.Label(status_frame, text="核心文件:", bg="#ecf0f1", font=("Microsoft YaHei", 9)).pack(anchor=tk.W, padx=10, pady=2)
        self.files_label = tk.Label(status_frame, text="检查中...", bg="#ecf0f1", fg="#27ae60", font=("Microsoft YaHei", 9))
        self.files_label.pack(anchor=tk.W, padx=20, pady=(0, 10))
    
    def create_result_area(self, parent):
        """创建右侧结果显示区域"""
        result_frame = tk.Frame(parent, bg="#ffffff")
        result_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 结果区域标题
        result_title_frame = tk.Frame(result_frame, bg="#34495e", height=40)
        result_title_frame.pack(fill=tk.X)
        result_title_frame.pack_propagate(False)
        
        tk.Label(result_title_frame,
                text="📊 操作结果与数据显示",
                font=("Microsoft YaHei", 12, "bold"),
                bg="#34495e",
                fg="white").pack(side=tk.LEFT, padx=15, pady=10)
        
        # 清空按钮
        tk.Button(result_title_frame,
                 text="🗑️ 清空",
                 command=self.clear_results,
                 font=("Microsoft YaHei", 9),
                 bg="#e74c3c",
                 fg="white",
                 relief="raised",
                 bd=1,
                 padx=10,
                 pady=2,
                 cursor="hand2").pack(side=tk.RIGHT, padx=15, pady=8)
        
        # 标签页控件
        self.notebook = ttk.Notebook(result_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建标签页
        self.create_tabs()
    
    def create_tabs(self):
        """创建标签页"""
        # 数据展示标签页
        self.data_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.data_frame, text="📊 股票数据")
        
        self.data_text = scrolledtext.ScrolledText(self.data_frame,
                                                  font=("Microsoft YaHei", 10),
                                                  wrap=tk.WORD,
                                                  bg="#f8f9fa",
                                                  fg="#2c3e50")
        self.data_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 操作日志标签页
        self.log_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.log_frame, text="📝 操作日志")
        
        self.log_text = scrolledtext.ScrolledText(self.log_frame,
                                                 font=("Consolas", 10),
                                                 wrap=tk.WORD,
                                                 bg="#f8f9fa",
                                                 fg="#2c3e50")
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 系统信息标签页
        self.info_frame = tk.Frame(self.notebook, bg="white")
        self.notebook.add(self.info_frame, text="⚙️ 系统信息")
        
        self.info_text = scrolledtext.ScrolledText(self.info_frame,
                                                  font=("Microsoft YaHei", 10),
                                                  wrap=tk.WORD,
                                                  bg="#f8f9fa",
                                                  fg="#2c3e50")
        self.info_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_status_bar(self, parent):
        """创建底部状态栏"""
        status_frame = tk.Frame(parent, bg="#bdc3c7", height=30)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(status_frame,
                                    text="● 系统就绪",
                                    font=("Microsoft YaHei", 10, "bold"),
                                    bg="#bdc3c7",
                                    fg="#27ae60")
        self.status_label.pack(side=tk.LEFT, padx=15, pady=5)
        
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate', length=200)
        self.progress.pack(side=tk.LEFT, padx=20, pady=5)
        
        tk.Button(status_frame,
                 text="🚪 退出",
                 command=self.exit_application,
                 font=("Microsoft YaHei", 9),
                 bg="#e74c3c",
                 fg="white",
                 relief="raised",
                 bd=1,
                 padx=15,
                 pady=2,
                 cursor="hand2").pack(side=tk.RIGHT, padx=15, pady=3)
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 根据级别设置颜色
        if level == "ERROR":
            self.log_text.tag_add("error", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("error", foreground="#e74c3c")
        elif level == "SUCCESS":
            self.log_text.tag_add("success", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("success", foreground="#27ae60")
    
    def show_data(self, title, data):
        """显示数据"""
        self.data_text.insert(tk.END, f"\n{'='*60}\n")
        self.data_text.insert(tk.END, f"📊 {title} - {datetime.now().strftime('%H:%M:%S')}\n")
        self.data_text.insert(tk.END, f"{'='*60}\n")
        self.data_text.insert(tk.END, f"{data}\n")
        self.data_text.see(tk.END)
        self.notebook.select(self.data_frame)
    
    def show_info(self, title, info):
        """显示系统信息"""
        self.info_text.insert(tk.END, f"\n{'='*60}\n")
        self.info_text.insert(tk.END, f"⚙️ {title} - {datetime.now().strftime('%H:%M:%S')}\n")
        self.info_text.insert(tk.END, f"{'='*60}\n")
        self.info_text.insert(tk.END, f"{info}\n")
        self.info_text.see(tk.END)
        self.notebook.select(self.info_frame)
    
    def clear_results(self):
        """清空所有结果显示"""
        self.log_text.delete(1.0, tk.END)
        self.data_text.delete(1.0, tk.END)
        self.info_text.delete(1.0, tk.END)
        self.log_message("所有显示区域已清空", "INFO")
    
    def set_status(self, message, color="#27ae60"):
        """设置状态"""
        self.status_label.config(text=f"● {message}", fg=color)
        self.root.update()
    
    def start_progress(self):
        """开始进度条"""
        self.progress.start(10)
    
    def stop_progress(self):
        """停止进度条"""
        self.progress.stop()

    def get_file_size(self, filename):
        """获取文件大小"""
        try:
            size = os.path.getsize(filename)
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size/1024:.1f} KB"
            else:
                return f"{size/(1024*1024):.1f} MB"
        except:
            return "未知"
    
    def load_initial_data(self):
        """加载初始数据"""
        self.log_message("欢迎使用股票数据采集系统！", "SUCCESS")
        self.log_message("系统初始化完成，请选择功能开始使用", "INFO")
        
        # 检查系统状态
        self.check_system_status_silent()
        
        # 加载现有数据
        self.load_existing_data()
    
    def check_system_status_silent(self):
        """静默检查系统状态"""
        # Python版本
        python_version = f"{sys.version.split()[0]}"
        self.python_label.config(text=python_version)
        
        # 检查依赖包
        required_modules = ["selenium", "requests", "matplotlib", "pandas", "numpy"]
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            self.deps_label.config(text=f"缺少 {len(missing_modules)} 个", fg="#e74c3c")
        else:
            self.deps_label.config(text="全部已安装", fg="#27ae60")
        
        # 检查核心文件
        core_files = [
            "后台采集.py", "股票名称获取工具.py", "股票涨跌幅获取工具.py",
            "智能数据分析报告.py", "数据服务器.py", "可视化界面_实时版.html"
        ]
        
        missing_files = [f for f in core_files if not os.path.exists(f)]
        
        if missing_files:
            self.files_label.config(text=f"缺少 {len(missing_files)} 个", fg="#e74c3c")
        else:
            self.files_label.config(text="全部存在", fg="#27ae60")
    
    def load_existing_data(self):
        """加载现有数据文件"""
        try:
            data_summary = "📊 当前股票数据概览\n\n"
            
            # 检查CSV文件并显示数据
            csv_files = [("popularity.csv", "人气榜"), ("soaring.csv", "飙升榜")]
            
            for filename, description in csv_files:
                if os.path.exists(filename):
                    try:
                        with open(filename, 'r', encoding='utf-8') as f:
                            reader = csv.reader(f)
                            rows = list(reader)
                            if len(rows) > 1:  # 有数据行
                                data_summary += f"✅ {description}数据: {len(rows)-1} 条记录\n"
                                data_summary += f"   最新更新: {datetime.fromtimestamp(os.path.getmtime(filename)).strftime('%Y-%m-%d %H:%M')}\n"
                                
                                # 显示前10条数据
                                data_summary += f"   前10条数据:\n"
                                for i, row in enumerate(rows[1:11]):  # 跳过标题行，显示前10条
                                    if len(row) >= 2:
                                        code = row[0] if len(row) > 0 else ""
                                        name = row[1] if len(row) > 1 else ""
                                        change = row[2] if len(row) > 2 else ""
                                        data_summary += f"   {i+1:2d}. {code} {name} {change}\n"
                                data_summary += "\n"
                            else:
                                data_summary += f"⚠️ {description}数据: 文件为空\n\n"
                    except Exception as e:
                        data_summary += f"❌ {description}数据: 读取失败 ({str(e)})\n\n"
                else:
                    data_summary += f"❌ {description}数据: 文件不存在\n\n"
            
            # 检查其他文件
            other_files = [
                ("codes.txt", "股票代码库"),
                ("stock_names_cache.json", "股票名称缓存"),
                ("股票分析报告.html", "分析报告")
            ]
            
            data_summary += "📁 其他数据文件:\n"
            for filename, description in other_files:
                if os.path.exists(filename):
                    size = os.path.getsize(filename)
                    if size < 1024:
                        size_str = f"{size} B"
                    elif size < 1024 * 1024:
                        size_str = f"{size/1024:.1f} KB"
                    else:
                        size_str = f"{size/(1024*1024):.1f} MB"
                    mod_time = datetime.fromtimestamp(os.path.getmtime(filename))
                    data_summary += f"✅ {description}: {size_str} ({mod_time.strftime('%m-%d %H:%M')})\n"
                else:
                    data_summary += f"❌ {description}: 不存在\n"
            
            self.show_data("系统数据概览", data_summary)
            
        except Exception as e:
            self.log_message(f"加载数据时出错: {str(e)}", "ERROR")
    
    def run_script_simple(self, script_name, description):
        """简单运行脚本"""
        def run():
            self.set_status("运行中...", "#f39c12")
            self.start_progress()
            self.log_message(f"开始执行: {description}", "INFO")
            
            try:
                if not os.path.exists(script_name):
                    raise FileNotFoundError(f"脚本文件不存在: {script_name}")
                
                # 简单启动脚本
                process = subprocess.Popen([sys.executable, script_name], cwd=os.getcwd())
                
                self.log_message(f"{description} 已启动", "SUCCESS")
                
                # 等待一段时间后重新加载数据
                self.root.after(5000, self.load_existing_data)
                
            except Exception as e:
                error_msg = f"{description} 执行出错: {str(e)}"
                self.log_message(error_msg, "ERROR")
            
            finally:
                self.stop_progress()
                self.set_status("就绪", "#27ae60")
        
        thread = threading.Thread(target=run, daemon=True)
        thread.start()
    
    # 功能按钮回调函数
    def run_background_collection(self):
        """后台数据采集"""
        self.run_script_simple("后台采集.py", "后台数据采集")
    
    def get_stock_names(self):
        """获取股票名称"""
        self.run_script_simple("股票名称获取工具.py", "股票名称获取")
    
    def get_price_changes(self):
        """获取涨跌幅数据"""
        self.run_script_simple("股票涨跌幅获取工具.py", "涨跌幅数据获取")
    
    def generate_report(self):
        """生成分析报告"""
        def run_and_ask():
            self.set_status("生成报告中...", "#f39c12")
            self.start_progress()
            self.log_message("开始生成智能分析报告", "INFO")

            try:
                if not os.path.exists("智能数据分析报告.py"):
                    raise FileNotFoundError("智能数据分析报告.py 文件不存在")

                # 运行分析报告脚本
                process = subprocess.Popen([sys.executable, "智能数据分析报告.py"],
                                         cwd=os.getcwd())

                self.log_message("智能分析报告脚本已启动", "SUCCESS")

                # 等待脚本完成
                process.wait()

                if process.returncode == 0:
                    self.log_message("智能分析报告生成完成", "SUCCESS")

                    # 检查报告文件是否生成
                    if os.path.exists("股票分析报告.html"):
                        self.show_data("分析报告生成完成",
                                     f"✅ HTML报告已生成: 股票分析报告.html\n"
                                     f"📊 文件大小: {self.get_file_size('股票分析报告.html')}\n"
                                     f"🕒 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                                     f"报告包含以下内容:\n"
                                     f"• 市场分布分析\n"
                                     f"• 涨跌幅统计\n"
                                     f"• 表现排名\n"
                                     f"• 榜单重叠分析\n"
                                     f"• 可视化图表")

                        # 立即询问是否打开报告
                        if messagebox.askyesno("报告生成完成",
                                             "✅ 智能分析报告生成完成！\n\n"
                                             "📄 文件: 股票分析报告.html\n"
                                             "📊 包含完整的数据分析和图表\n\n"
                                             "是否立即在浏览器中打开查看？"):
                            self.open_html_report()
                    else:
                        self.log_message("报告文件未找到", "ERROR")
                        messagebox.showerror("错误", "报告生成失败：未找到输出文件")
                else:
                    self.log_message("报告生成失败", "ERROR")
                    messagebox.showerror("错误", "报告生成失败：脚本执行出错")

            except Exception as e:
                error_msg = f"生成报告时出错: {str(e)}"
                self.log_message(error_msg, "ERROR")
                messagebox.showerror("错误", f"生成报告失败:\n{str(e)}")

            finally:
                self.stop_progress()
                self.set_status("就绪", "#27ae60")

        # 在新线程中运行
        thread = threading.Thread(target=run_and_ask, daemon=True)
        thread.start()
    
    def start_server(self):
        """启动数据服务器"""
        try:
            self.log_message("启动数据服务器...", "INFO")
            subprocess.Popen([sys.executable, "数据服务器.py"], cwd=os.getcwd())
            self.log_message("数据服务器已在后台启动", "SUCCESS")
            self.show_info("数据服务器", "服务器已启动\n访问地址: http://localhost:8080/可视化界面_实时版.html\n\n服务器在后台运行，您可以通过浏览器访问可视化界面。")
            
            if messagebox.askyesno("服务器启动", "数据服务器已启动！\n是否在浏览器中打开可视化界面？"):
                self.open_web_interface()
                
        except Exception as e:
            self.log_message(f"启动服务器失败: {str(e)}", "ERROR")
    
    def open_web_interface(self):
        """打开可视化界面"""
        try:
            url = "http://localhost:8080/可视化界面_实时版.html"
            webbrowser.open(url)
            self.log_message("可视化界面已在浏览器中打开", "SUCCESS")
        except Exception as e:
            self.log_message(f"打开界面失败: {str(e)}", "ERROR")
    
    def open_html_report(self):
        """打开HTML报告"""
        try:
            report_file = "股票分析报告.html"

            # 检查文件是否存在
            if not os.path.exists(report_file):
                self.log_message("报告文件不存在，请先生成报告", "ERROR")
                messagebox.showerror("文件不存在",
                                   f"找不到报告文件: {report_file}\n\n"
                                   f"请先点击'生成分析报告'按钮生成报告。")
                return

            # 获取绝对路径并转换为正确的URL格式
            report_path = os.path.abspath(report_file)

            # Windows路径处理
            if os.name == 'nt':  # Windows系统
                report_url = f"file:///{report_path.replace(os.sep, '/')}"
            else:
                report_url = f"file://{report_path}"

            # 打开浏览器
            webbrowser.open(report_url)

            self.log_message(f"分析报告已在浏览器中打开: {report_file}", "SUCCESS")
            self.show_data("报告已打开",
                         f"📄 报告文件: {report_file}\n"
                         f"📁 完整路径: {report_path}\n"
                         f"🌐 浏览器URL: {report_url}\n"
                         f"🕒 打开时间: {datetime.now().strftime('%H:%M:%S')}")

        except Exception as e:
            error_msg = f"打开报告失败: {str(e)}"
            self.log_message(error_msg, "ERROR")
            messagebox.showerror("打开失败", f"无法打开HTML报告:\n{str(e)}\n\n请检查浏览器设置或手动打开文件。")
    
    def refresh_data(self):
        """刷新数据显示"""
        self.log_message("刷新数据显示...", "INFO")
        self.load_existing_data()
        self.log_message("数据显示已刷新", "SUCCESS")
    
    def check_system_status(self):
        """检查系统状态"""
        self.log_message("执行系统状态检查...", "INFO")
        
        # 更新状态显示
        self.check_system_status_silent()
        
        # 生成详细报告
        status_report = "⚙️ 系统状态详细报告\n\n"
        
        # Python信息
        status_report += f"🐍 Python版本: {sys.version.split()[0]}\n"
        status_report += f"📍 Python路径: {sys.executable}\n\n"
        
        # 依赖包检查
        required_modules = ["selenium", "requests", "matplotlib", "pandas", "numpy"]
        status_report += "📦 依赖包状态:\n"
        
        for module in required_modules:
            try:
                mod = __import__(module)
                version = getattr(mod, '__version__', '未知版本')
                status_report += f"  ✅ {module}: {version}\n"
            except ImportError:
                status_report += f"  ❌ {module}: 未安装\n"
        
        # 文件检查
        core_files = [
            "后台采集.py", "股票名称获取工具.py", "股票涨跌幅获取工具.py",
            "智能数据分析报告.py", "数据服务器.py", "可视化界面_实时版.html"
        ]
        
        status_report += "\n📁 核心文件状态:\n"
        for file in core_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                status_report += f"  ✅ {file}: {size/1024:.1f} KB\n"
            else:
                status_report += f"  ❌ {file}: 不存在\n"
        
        self.show_info("系统状态检查", status_report)
        self.log_message("系统状态检查完成", "SUCCESS")
    
    def exit_application(self):
        """退出应用程序"""
        if messagebox.askyesno("确认退出", "确定要退出股票数据采集系统吗？"):
            self.log_message("正在退出系统...", "INFO")
            self.root.quit()
            self.root.destroy()

def main():
    """主函数"""
    root = tk.Tk()
    app = FinalStockGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()

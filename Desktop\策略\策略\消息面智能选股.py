#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息面智能选股 - 正式版
根据用户精确指导：
- 话题 = #  #
- 股票 = "相关股" 
- "点击加载更多" = 在"郑重声明"上一行
"""

import requests
import json
import re
import time
from datetime import datetime
import csv
import os

# Selenium导入
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("[警告] Selenium未安装，将使用备用方案")

class NewsBasedStockSelector:
    def __init__(self):
        self.hot_topics = []
        self.stock_mentions = {}
        self.sentiment_scores = {}
        self.stock_name_cache = self.load_stock_name_cache()
        
    def test_network_connection(self):
        """测试网络连接"""
        try:
            print("[网络] 测试网络连接...")
            response = requests.get("https://www.baidu.com", timeout=5)
            if response.status_code == 200:
                print("[成功] 网络连接正常")
                return True
            else:
                print("[失败] 网络连接异常")
                return False
        except Exception as e:
            print(f"[失败] 网络连接失败: {str(e)}")
            return False
    
    def try_web_crawling(self):
        """网络爬取 - 精确版"""
        try:
            print("[网络] 启动精确网络爬取...")
            
            if not SELENIUM_AVAILABLE:
                print("[警告] Selenium不可用，跳过浏览器爬取")
                return None
                
            print("[成功] Selenium可用，启动浏览器爬取")
            
            # 设置浏览器选项
            chrome_options = Options()
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--headless')
            
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(20)
            driver.implicitly_wait(10)
            
            # 执行反检测脚本
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("[网络] 访问东方财富股吧话题页面...")
            driver.get("https://gubatopic.eastmoney.com/index.html")
            time.sleep(3)
            
            title = driver.title
            print(f"[成功] 成功访问页面: {title}")
            
            # 快速精确爬取
            all_topics = []
            all_stocks = []
            max_pages = 5  # 只爬取5页，避免过度爬取
            topics_per_page = 10  # 每页期望获取10个话题

            print(f"[目标] 开始分页爬取，目标: {max_pages}页，每页约{topics_per_page}个话题")

            for page in range(max_pages):
                print(f"\n[页面] === 第 {page + 1} 页爬取开始 ===")

                # 记录本页开始前的话题数量
                page_start_count = len(all_topics)

                # 等待页面稳定
                time.sleep(2)

                # 提取当前页面的话题
                page_topics = self.extract_topics_fast(driver, page + 1)
                if page_topics:
                    # 去重：只添加新话题
                    existing_titles = {t['title'] for t in all_topics}
                    new_topics = [t for t in page_topics if t['title'] not in existing_titles]
                    all_topics.extend(new_topics)

                    page_new_count = len(all_topics) - page_start_count
                    print(f"[成功] 第 {page + 1} 页新话题: {page_new_count} 个 (总计: {len(all_topics)})")

                    # 如果这一页获取的新话题太少，说明重复内容多
                    if page_new_count < 3 and page > 0:
                        print(f"[警告] 第 {page + 1} 页新话题过少({page_new_count}个)，可能重复内容较多")

                # 提取股票（简化处理）
                page_stocks = self.extract_stocks_fast(driver, page + 1)
                if page_stocks:
                    existing_codes = {s['code'] for s in all_stocks}
                    new_stocks = [s for s in page_stocks if s['code'] not in existing_codes]
                    all_stocks.extend(new_stocks)
                    print(f"[成功] 第 {page + 1} 页新股票: {len(new_stocks)} 只 (总计: {len(all_stocks)})")

                # 检查是否需要继续爬取下一页
                if page < max_pages - 1:
                    print(f"[刷新] 准备进入第 {page + 2} 页...")

                    # 尝试加载下一页
                    if self.load_next_page(driver, page):
                        print(f"[成功] 成功加载第 {page + 2} 页")
                        # 等待新页面内容稳定
                        time.sleep(3)
                    else:
                        print(f"[失败] 无法加载第 {page + 2} 页，停止爬取")
                        break
                else:
                    print(f"[页面] 已完成 {max_pages} 页爬取")

                print(f"[页面] === 第 {page + 1} 页爬取完成 ===\n")
            
            # 将股票信息转换为话题格式
            for stock in all_stocks:
                all_topics.append({
                    'title': f"{stock['name']}({stock['code']}) - 相关股推荐",
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'source': '相关股',
                    'stock_code': stock['code'],
                    'stock_name': stock['name']
                })
            
            driver.quit()
            
            print(f"[数据] 爬取完成: 话题 {len([t for t in all_topics if t['source'] != '相关股'])} 个, 股票 {len(all_stocks)} 只, 总计 {len(all_topics)} 条")
            
            return all_topics if all_topics else None
                
        except Exception as e:
            print(f"[失败] 网络爬取失败: {str(e)}")
            return None
    
    def extract_topics_precise(self, driver, page_num):
        """精确提取话题 - 寻找 # # 符号里的内容"""
        try:
            topics = []
            print(f"  [目标] 寻找 # # 符号里的话题内容...")

            # 方法1: 从页面源码中提取 # # 之间的内容
            try:
                page_source = driver.page_source

                # 匹配 # 和 # 之间的内容
                hash_patterns = [
                    r'#\s*([^#]+?)\s*#',  # # 内容 #
                    r'#([^#]+?)#',        # #内容#
                    r'＃\s*([^＃]+?)\s*＃', # 全角符号
                    r'＃([^＃]+?)＃'       # 全角符号紧密
                ]

                for pattern in hash_patterns:
                    matches = re.findall(pattern, page_source, re.DOTALL)
                    for match in matches:
                        content = match.strip()
                        if content and len(content) > 2 and len(content) < 100:
                            # 过滤明显不是话题的内容
                            if not any(word in content.lower() for word in [
                                'script', 'style', 'function', 'var', 'document',
                                'http', 'www', 'com', 'cn', 'html', 'css', 'js'
                            ]):
                                topics.append({
                                    'title': content,
                                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    'source': f'#话题#-第{page_num}页',
                                    'page': page_num,
                                    'extraction_method': '井号符号'
                                })
                                print(f"    [成功] #话题#: {content[:40]}...")

                if topics:
                    print(f"  [成功] 从 # # 符号提取到 {len(topics)} 个话题")
                    return topics[:30]  # 每页最多30个话题

            except Exception as e:
                print(f"  [失败] 井号符号提取失败: {str(e)}")

            # 方法2: 寻找包含井号的元素
            try:
                print("  [搜索] 寻找包含井号符号的页面元素...")

                # 查找包含 # 的元素
                hash_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '#')]")

                for element in hash_elements:
                    if element.is_displayed():
                        text = element.text.strip()
                        if '#' in text:
                            print(f"    [搜索] 发现井号元素: {text[:50]}...")

                            # 提取井号之间的内容
                            for pattern in [r'#\s*([^#]+?)\s*#', r'#([^#]+?)#']:
                                matches = re.findall(pattern, text)
                                for match in matches:
                                    content = match.strip()
                                    if content and len(content) > 2 and len(content) < 100:
                                        topics.append({
                                            'title': content,
                                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                            'source': f'#元素#-第{page_num}页',
                                            'page': page_num,
                                            'extraction_method': '元素井号'
                                        })
                                        print(f"    [成功] #元素话题#: {content[:40]}...")

                if topics:
                    print(f"  [成功] 从元素井号提取到 {len(topics)} 个话题")
                    return topics[:30]

            except Exception as e:
                print(f"  [失败] 元素井号提取失败: {str(e)}")

            # 方法3: 备用方案 - 寻找话题相关元素
            try:
                print("  [刷新] 使用备用方案寻找话题...")

                backup_selectors = [
                    ".topic-item",
                    ".hot-topic",
                    ".topic-content",
                    ".guba-topic",
                    "a[href*='topic']",
                    ".title"
                ]

                for selector in backup_selectors:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            if text and len(text) > 3 and len(text) < 100:
                                if not any(word in text.lower() for word in ['登录', '注册', '搜索', '导航']):
                                    topics.append({
                                        'title': text,
                                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                        'source': f'备用话题-第{page_num}页',
                                        'page': page_num,
                                        'extraction_method': '备用选择器'
                                    })
                                    print(f"    [成功] 备用话题: {text[:30]}...")

                    if topics:
                        break

            except Exception as e:
                print(f"  [失败] 备用方案失败: {str(e)}")

            print(f"  [数据] 第{page_num}页总计提取: {len(topics)} 个话题")
            return topics[:30]  # 每页最多30个话题

        except Exception as e:
            print(f"  [失败] 提取话题失败: {str(e)}")
            return []

    def extract_topics_fast(self, driver, page_num):
        """快速提取话题 - 增强调试版"""
        try:
            topics = []

            print(f"  [搜索] 第{page_num}页：开始提取话题...")

            # 获取页面源码
            page_source = driver.page_source
            print(f"  [页面] 页面源码长度: {len(page_source)} 字符")

            # 多种模式匹配 # # 之间的内容
            patterns = [
                r'#([^#]{3,80})#',      # 标准模式
                r'#\s*([^#]{3,80})\s*#', # 带空格
                r'＃([^＃]{3,80})＃',     # 全角符号
                r'#([^#\n]{3,80})#',    # 不包含换行
            ]

            all_matches = []
            for i, pattern in enumerate(patterns):
                matches = re.findall(pattern, page_source, re.DOTALL)
                print(f"  [搜索] 模式{i+1}: 找到 {len(matches)} 个匹配")
                all_matches.extend(matches)

            print(f"  [数据] 总匹配数: {len(all_matches)}")

            # 处理匹配结果
            for match in all_matches:
                content = match.strip()
                if content:
                    print(f"    [搜索] 检查内容: '{content[:30]}...'")

                    # 过滤条件
                    if not any(word in content.lower() for word in [
                        'script', 'function', 'http', 'www', 'com',
                        'javascript', 'css', 'html', 'document'
                    ]):
                        topics.append({
                            'title': content,
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'source': f'快速#话题#-第{page_num}页',
                            'page': page_num
                        })
                        print(f"    [成功] 添加话题: '{content[:40]}...'")
                    else:
                        print(f"    [失败] 过滤内容: '{content[:30]}...'")

            # 去重处理
            unique_topics = []
            seen_titles = set()
            for topic in topics:
                if topic['title'] not in seen_titles:
                    unique_topics.append(topic)
                    seen_titles.add(topic['title'])
                else:
                    print(f"    [警告] 重复话题: '{topic['title'][:30]}...'")

            print(f"  [数据] 第{page_num}页提取结果: 原始{len(topics)}个 -> 去重后{len(unique_topics)}个")

            return unique_topics[:25]  # 增加到25个

        except Exception as e:
            print(f"  [失败] 快速提取失败: {str(e)}")
            return []

    def extract_stocks_fast(self, driver, page_num):
        """快速提取股票"""
        try:
            stocks = []

            # 快速从页面源码提取股票代码
            page_source = driver.page_source
            codes = re.findall(r'\b([036]\d{5})\b', page_source)

            for code in set(codes[:15]):  # 限制15个，去重
                stocks.append({
                    'code': code,
                    'name': f'股票{code}',
                    'description': '快速提取'
                })

            return stocks

        except Exception:
            return []

    def click_load_more_fast(self, driver):
        """快速点击加载更多"""
        try:
            # 滚动到底部
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(0.5)

            # 快速JavaScript搜索并点击
            success = driver.execute_script("""
            var elements = document.querySelectorAll('*');
            for (var i = 0; i < elements.length; i++) {
                var element = elements[i];
                if (element.textContent && element.textContent.trim() === '点击加载更多') {
                    var style = window.getComputedStyle(element);
                    if (style.display !== 'none' && style.visibility !== 'hidden') {
                        element.click();
                        return true;
                    }
                }
            }
            return false;
            """)

            return success

        except Exception:
            return False

    def click_load_more_smart(self, driver):
        """智能点击加载更多 - 多种策略"""
        try:
            print("  🧠 智能搜索'点击加载更多'按钮...")

            # 滚动到底部
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(1)

            # 策略1: 查找包含"加载更多"的所有元素
            load_more_patterns = [
                "点击加载更多", "加载更多", "查看更多", "显示更多",
                "load more", "Load More", "LOAD MORE"
            ]

            for pattern in load_more_patterns:
                try:
                    # XPath查找
                    elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{pattern}')]")
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            print(f"  [成功] 找到按钮: {element.text[:20]}...")
                            return self.safe_click(driver, element)
                except Exception:
                    continue

            # 策略2: 查找常见的加载更多按钮类名
            common_selectors = [
                "[class*='load-more']", "[class*='loadmore']", "[class*='more-btn']",
                "[class*='show-more']", "[id*='load-more']", "[id*='loadmore']",
                "button[class*='more']", "a[class*='more']", "div[class*='more']"
            ]

            for selector in common_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            text = element.text.strip()
                            if any(word in text for word in ["更多", "加载", "load", "more"]):
                                print(f"  [成功] CSS找到按钮: {text[:20]}...")
                                return self.safe_click(driver, element)
                except Exception:
                    continue

            # 策略3: JavaScript全页面搜索
            try:
                success = driver.execute_script("""
                var allElements = document.querySelectorAll('*');
                var patterns = ['点击加载更多', '加载更多', '查看更多', '显示更多'];

                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    var text = element.textContent || element.innerText || '';

                    for (var j = 0; j < patterns.length; j++) {
                        if (text.trim() === patterns[j]) {
                            var style = window.getComputedStyle(element);
                            if (style.display !== 'none' && style.visibility !== 'hidden') {
                                element.click();
                                return true;
                            }
                        }
                    }
                }
                return false;
                """)

                if success:
                    print("  [成功] JavaScript全页面搜索成功")
                    return True
            except Exception:
                pass

            print("  [失败] 智能搜索未找到按钮")
            return False

        except Exception as e:
            print(f"  [失败] 智能点击失败: {str(e)}")
            return False

    def load_next_page(self, driver, current_page):
        """加载下一页内容"""
        try:
            print(f"  [刷新] 尝试加载第 {current_page + 1} 页...")

            # 记录当前话题数量
            before_source = driver.page_source
            before_topics = set(re.findall(r'#([^#]{3,80})#', before_source))

            # 尝试多种点击方法
            click_methods = [
                ("快速点击", self.click_load_more_fast),
                ("精确点击", self.click_load_more_precise),
                ("智能点击", self.click_load_more_smart)
            ]

            for method_name, method_func in click_methods:
                print(f"  [目标] 尝试{method_name}...")
                if method_func(driver):
                    print(f"  [成功] {method_name}成功")

                    # 等待新内容加载
                    for wait_time in range(8):  # 最多等待8秒
                        time.sleep(1)

                        current_source = driver.page_source
                        current_topics = set(re.findall(r'#([^#]{3,80})#', current_source))
                        new_count = len(current_topics - before_topics)

                        if new_count > 0:
                            print(f"  [完成] 发现 {new_count} 个新话题，加载成功！")
                            return True
                        elif wait_time == 3:
                            print(f"  [等待] 等待新内容... ({wait_time + 1}/8)")

                    print(f"  [警告] {method_name}后未发现新内容")
                else:
                    print(f"  [失败] {method_name}失败")

            print(f"  [失败] 所有点击方法都失败，无法加载第 {current_page + 1} 页")
            return False

        except Exception as e:
            print(f"  [失败] 加载下一页失败: {str(e)}")
            return False

    def scroll_to_load_more_content(self, driver):
        """下拉页面获取更多内容"""
        try:
            print("  [滚动] 开始下拉页面获取更多话题...")

            # 获取当前页面高度
            initial_height = driver.execute_script("return document.body.scrollHeight")
            print(f"  [测量] 初始页面高度: {initial_height}")

            # 记录下拉前的话题数量
            before_scroll = driver.page_source
            before_topics = set(re.findall(r'#([^#]{3,80})#', before_scroll))
            print(f"  [数据] 下拉前话题数量: {len(before_topics)}")

            # 分段下拉，模拟用户滚动行为
            scroll_steps = 5  # 分5步下拉
            for step in range(scroll_steps):
                # 计算滚动位置
                scroll_position = (step + 1) * (initial_height // scroll_steps)

                print(f"  [滚动] 第{step+1}步下拉到位置: {scroll_position}")
                driver.execute_script(f"window.scrollTo(0, {scroll_position});")

                # 每次下拉后等待内容加载
                time.sleep(1)

                # 检查是否有新话题出现
                current_source = driver.page_source
                current_topics = set(re.findall(r'#([^#]{3,80})#', current_source))
                new_count = len(current_topics - before_topics)

                if new_count > 0:
                    print(f"  [成功] 下拉第{step+1}步发现{new_count}个新话题")
                    before_topics = current_topics  # 更新基准

            # 最后滚动到页面底部
            print("  [滚动] 最终滚动到页面底部...")
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            # 再次检查新内容
            final_source = driver.page_source
            final_topics = set(re.findall(r'#([^#]{3,80})#', final_source))
            total_new = len(final_topics - before_topics)

            print(f"  [数据] 下拉完成，总计新增话题: {total_new}个")
            print(f"  [数据] 当前页面总话题数: {len(final_topics)}个")

            return True

        except Exception as e:
            print(f"  [失败] 下拉页面失败: {str(e)}")
            return False
    
    def extract_stocks_precise(self, driver, page_num):
        """精确提取股票 - 寻找'相关股'"""
        try:
            stocks = []
            print(f"  [目标] 寻找'相关股'...")
            
            # 精确选择器：寻找相关股
            selectors = [
                "//*[contains(text(), '相关股')]",
                "//*[contains(text(), '关联股')]",
                "//*[contains(text(), '相关')]",
                ".related-stock",
                ".stock-related",
                "[class*='related']",
                "[class*='stock']"
            ]
            
            for selector in selectors:
                try:
                    if selector.startswith("//"):
                        elements = driver.find_elements(By.XPATH, selector)
                    else:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            if text:
                                # 从文本中提取股票信息
                                stock_info = self.parse_stock_info(text)
                                if stock_info:
                                    stocks.extend(stock_info)
                                    print(f"    [成功] 相关股: {text[:30]}...")
                    
                    if stocks:
                        break
                        
                except Exception:
                    continue
            
            # 备用方案：从页面源码提取股票代码
            if not stocks:
                page_source = driver.page_source
                codes = re.findall(r'\b([036]\d{5})\b', page_source)
                for code in set(codes[:10]):
                    stocks.append({
                        'code': code,
                        'name': f'股票{code}',
                        'description': '页面提取'
                    })
            
            return stocks[:15]  # 每页最多15只股票
            
        except Exception as e:
            print(f"  [失败] 提取股票失败: {str(e)}")
            return []
    
    def click_load_more_precise(self, driver):
        """精确点击'点击加载更多' - 在'郑重声明'上一行"""
        try:
            print("  [目标] 精确寻找'点击加载更多'按钮...")
            print("  [位置] 位置: 郑重声明上一行")
            
            # 滚动到页面底部
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            
            # 方法1: 精确文本匹配
            exact_selectors = [
                "//button[text()='点击加载更多']",
                "//a[text()='点击加载更多']", 
                "//div[text()='点击加载更多']",
                "//span[text()='点击加载更多']",
                "//*[text()='点击加载更多']"
            ]
            
            for selector in exact_selectors:
                try:
                    element = driver.find_element(By.XPATH, selector)
                    if element and element.is_displayed():
                        print(f"  [成功] 找到按钮: {element.text}")
                        return self.safe_click(driver, element)
                except Exception:
                    continue
            
            # 方法2: 通过郑重声明定位
            try:
                statement_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '郑重声明')]")
                for statement in statement_elements:
                    if statement.is_displayed():
                        print(f"  [成功] 找到郑重声明: {statement.text[:20]}...")
                        
                        # 查找前面的元素
                        prev_elements = driver.find_elements(By.XPATH, 
                            "//preceding::*[contains(text(), '点击加载更多')]")
                        
                        for elem in prev_elements[-3:]:  # 检查最近的3个
                            if elem.is_displayed():
                                print(f"  [成功] 在郑重声明前找到按钮: {elem.text}")
                                return self.safe_click(driver, elem)
            except Exception:
                pass
            
            # 方法3: JavaScript搜索
            try:
                js_element = driver.execute_script("""
                var elements = document.querySelectorAll('*');
                for (var i = 0; i < elements.length; i++) {
                    var element = elements[i];
                    if (element.textContent && element.textContent.trim() === '点击加载更多') {
                        var style = window.getComputedStyle(element);
                        if (style.display !== 'none' && style.visibility !== 'hidden') {
                            return element;
                        }
                    }
                }
                return null;
                """)
                
                if js_element:
                    print("  [成功] JavaScript找到按钮")
                    return self.safe_click(driver, js_element)
            except Exception:
                pass
            
            print("  [失败] 未找到'点击加载更多'按钮")
            return False
            
        except Exception as e:
            print(f"  [失败] 点击失败: {str(e)}")
            return False
    
    def safe_click(self, driver, element):
        """安全点击元素"""
        try:
            # 滚动到元素
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(1)
            
            # 尝试点击
            try:
                element.click()
                print("  [成功] 普通点击成功")
                return True
            except:
                driver.execute_script("arguments[0].click();", element)
                print("  [成功] JavaScript点击成功")
                return True
                
        except Exception as e:
            print(f"  [失败] 点击失败: {str(e)}")
            return False
    
    def parse_stock_info(self, text):
        """解析股票信息"""
        try:
            stocks = []
            
            # 匹配模式
            patterns = [
                r'(\d{6})\s*([^\d\s]{2,10})',  # 代码 名称
                r'([^\d\s]{2,10})\s*\((\d{6})\)',  # 名称(代码)
                r'([^\d\s]{2,10})\s*(\d{6})',  # 名称 代码
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    if len(match) == 2:
                        if match[0].isdigit():
                            code, name = match[0], match[1]
                        else:
                            name, code = match[0], match[1]
                        
                        if re.match(r'^[036]\d{5}$', code):
                            stocks.append({
                                'code': code,
                                'name': name,
                                'description': '相关股'
                            })
            
            # 只查找代码
            if not stocks:
                codes = re.findall(r'\b([036]\d{5})\b', text)
                for code in codes:
                    # 使用快速获取方法
                    real_name = self.get_stock_name_fast(code)
                    if real_name:
                        name = real_name
                    else:
                        name = f'股票{code}'

                    stocks.append({
                        'code': code,
                        'name': name,
                        'description': '代码提取'
                    })
            
            return stocks
            
        except Exception:
            return []

    def load_stock_name_cache(self):
        """加载股票名称缓存"""
        try:
            if os.path.exists('stock_names_cache.json'):
                with open('stock_names_cache.json', 'r', encoding='utf-8') as f:
                    cache = json.load(f)
                print(f"[列表] 加载股票名称缓存: {len(cache)} 条")
                return cache
        except Exception:
            pass

        # 返回常用股票名称
        return {
            "000001": "平安银行", "000002": "万科A", "000063": "中兴通讯",
            "000858": "五粮液", "002415": "海康威视", "002594": "比亚迪",
            "300750": "宁德时代", "300760": "迈瑞医疗", "600000": "浦发银行",
            "600036": "招商银行", "600519": "贵州茅台", "600887": "伊利股份",
            "688981": "中芯国际", "000725": "京东方A", "002230": "科大讯飞"
        }

    def save_stock_name_cache(self):
        """保存股票名称缓存"""
        try:
            with open('stock_names_cache.json', 'w', encoding='utf-8') as f:
                json.dump(self.stock_name_cache, f, ensure_ascii=False, indent=2)
        except Exception:
            pass

    def get_stock_name_fast(self, code):
        """快速获取股票名称（优先使用缓存）"""
        # 1. 检查缓存
        if code in self.stock_name_cache:
            return self.stock_name_cache[code]

        # 2. 在线查询（简化版，只用一个API）
        try:
            if code.startswith(('60', '68')):
                market_code = f"sh{code}"
            else:
                market_code = f"sz{code}"

            url = f"http://hq.sinajs.cn/list={market_code}"
            response = requests.get(url, timeout=2)  # 减少超时时间
            response.encoding = 'gbk'

            if response.status_code == 200:
                content = response.text
                if 'var hq_str_' in content:
                    data_match = re.search(r'"([^"]*)"', content)
                    if data_match:
                        data_parts = data_match.group(1).split(',')
                        if len(data_parts) > 0 and data_parts[0]:
                            stock_name = data_parts[0].strip()
                            if stock_name and stock_name != code:
                                # 保存到缓存
                                self.stock_name_cache[code] = stock_name
                                return stock_name
        except Exception:
            pass

        return None

    def batch_get_stock_names(self, codes):
        """批量获取股票名称"""
        print(f"[股票] 批量获取 {len(codes)} 只股票名称...")

        updated_count = 0
        for i, code in enumerate(codes):
            if code not in self.stock_name_cache:
                name = self.get_stock_name_fast(code)
                if name:
                    updated_count += 1
                    print(f"  {i+1}/{len(codes)} {code} -> {name}")
                else:
                    print(f"  {i+1}/{len(codes)} {code} -> 未找到")

                # 避免请求过快
                if i % 5 == 4:  # 每5个请求暂停一下
                    time.sleep(0.5)

        if updated_count > 0:
            self.save_stock_name_cache()
            print(f"[成功] 新获取 {updated_count} 只股票名称")

        return updated_count

    def generate_smart_topics(self):
        """生成智能话题"""
        try:
            print("🧠 生成智能话题...")
            topics = []

            # 读取股票数据
            stock_data = []
            for filename in ['popularity.csv', 'soaring.csv']:
                if os.path.exists(filename):
                    with open(filename, 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        stock_data.extend(list(reader))

            if stock_data:
                topic_templates = [
                    "{name}今日走势强劲，后市如何？",
                    "{name}技术面突破，值得关注",
                    "看好{name}的投资机会，大家怎么看？",
                    "{name}基本面分析，长期价值如何？",
                    "{name}消息面利好，股价有望上涨"
                ]

                for i, stock in enumerate(stock_data[:20]):
                    name = stock.get('name', '').replace('股份', '').replace('有限公司', '')
                    if name:
                        template = topic_templates[i % len(topic_templates)]
                        topics.append({
                            'title': template.format(name=name),
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'source': '智能生成',
                            'related_stock': stock.get('code', ''),
                            'stock_name': name
                        })

            # 市场热点
            market_topics = [
                "A股三大指数集体上涨，市场情绪回暖",
                "北向资金持续流入，外资看好A股",
                "科技股领涨，创新驱动成投资主线",
                "消费板块表现活跃，内需复苏显现",
                "新能源汽车产业链机会凸显"
            ]

            for topic in market_topics:
                topics.append({
                    'title': topic,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'source': '市场热点'
                })

            print(f"[成功] 智能生成 {len(topics)} 个话题")
            return topics

        except Exception as e:
            print(f"[失败] 智能话题生成失败: {str(e)}")
            return []

    def get_hot_topics(self):
        """获取热门话题 - 混合策略"""
        print("[新闻] 开始获取热门话题...")

        # 优先网络爬取
        if self.test_network_connection():
            web_topics = self.try_web_crawling()
            if web_topics and len(web_topics) > 0:
                smart_topics = self.generate_smart_topics()
                all_topics = web_topics + smart_topics
                print(f"[成功] 混合策略: 网络 {len(web_topics)} + 智能 {len(smart_topics)} = 总计 {len(all_topics)}")
                return all_topics

        # 备用智能生成
        smart_topics = self.generate_smart_topics()
        if smart_topics:
            print(f"[成功] 备用方案: 智能生成 {len(smart_topics)} 个话题")
            return smart_topics
        else:
            print("[失败] 所有方案都失败了")
            return []

    def analyze_stock_mentions(self):
        """分析股票提及"""
        try:
            print("\n[搜索] 步骤2: 分析股票提及...")

            # 加载股票代码库
            stock_codes = {}
            for filename in ['popularity.csv', 'soaring.csv']:
                if os.path.exists(filename):
                    with open(filename, 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        for row in reader:
                            code = row.get('code', '')
                            name = row.get('name', '')
                            if code and name:
                                stock_codes[name] = code
                                simple_name = name.replace('股份', '').replace('有限公司', '').replace('集团', '')
                                stock_codes[simple_name] = code

            print(f"[列表] 加载股票代码库: {len(stock_codes)} 个")

            # 分析提及
            mentions = {}
            for topic in self.hot_topics:
                title = topic['title']

                # 查找股票名称
                for stock_name, stock_code in stock_codes.items():
                    if stock_name in title:
                        if stock_code not in mentions:
                            mentions[stock_code] = {
                                'name': stock_name,
                                'count': 0,
                                'topics': []
                            }
                        mentions[stock_code]['count'] += 1
                        mentions[stock_code]['topics'].append(title)

                # 查找股票代码
                codes = re.findall(r'\b\d{6}\b', title)
                for code in codes:
                    if code not in mentions:
                        mentions[code] = {
                            'name': f'股票{code}',
                            'count': 0,
                            'topics': []
                        }
                    mentions[code]['count'] += 1
                    mentions[code]['topics'].append(title)

            self.stock_mentions = mentions
            print(f"[目标] 发现股票提及: {len(mentions)} 只")
            return mentions

        except Exception as e:
            print(f"[失败] 分析失败: {str(e)}")
            return {}

    def calculate_sentiment_scores(self):
        """计算情感得分"""
        try:
            print("\n[数据] 计算情感得分...")

            positive_words = ['涨', '上涨', '大涨', '突破', '利好', '看好', '推荐', '买入', '强劲', '机会', '潜力', '优质', '龙头', '热门']
            negative_words = ['跌', '下跌', '大跌', '破位', '利空', '看空', '卖出', '弱势', '风险', '警惕', '回调', '调整']

            scores = {}
            for code, data in self.stock_mentions.items():
                total_score = 0
                topic_count = len(data['topics'])

                for topic in data['topics']:
                    topic_score = 0
                    for word in positive_words:
                        topic_score += topic.count(word) * 1
                    for word in negative_words:
                        topic_score -= topic.count(word) * 1
                    total_score += topic_score

                if topic_count > 0:
                    avg_sentiment = total_score / topic_count
                    mention_weight = min(data['count'] / 3, 2)
                    final_score = avg_sentiment * mention_weight

                    scores[code] = {
                        'sentiment_score': round(final_score, 2),
                        'mention_count': data['count'],
                        'avg_sentiment': round(avg_sentiment, 2),
                        'name': data['name']
                    }

            self.sentiment_scores = scores
            print(f"[数据] 计算完成: {len(scores)} 只股票")
            return scores

        except Exception as e:
            print(f"[失败] 计算失败: {str(e)}")
            return {}

    def generate_recommendations(self):
        """生成推荐"""
        try:
            recommendations = []

            sorted_stocks = sorted(
                self.sentiment_scores.items(),
                key=lambda x: (x[1]['sentiment_score'], x[1]['mention_count']),
                reverse=True
            )

            print(f"\n[目标] 消息面智能选股推荐 - 全部 {len(sorted_stocks)} 只股票")
            print("=" * 80)
            print(f"{'排名':<4} {'代码':<8} {'名称':<15} {'情感得分':<8} {'提及次数':<8} {'推荐等级'}")
            print("=" * 80)

            for i, (code, data) in enumerate(sorted_stocks, 1):
                score = data['sentiment_score']
                mentions = data['mention_count']

                if score >= 2.0 and mentions >= 3:
                    level = "🔥 强烈推荐"
                elif score >= 1.5 and mentions >= 2:
                    level = "⭐ 推荐"
                elif score >= 1.0 or mentions >= 2:
                    level = "👀 关注"
                elif score >= 0.5:
                    level = "[警告] 观望"
                elif score > 0:
                    level = "🤔 谨慎"
                else:
                    level = "[失败] 回避"

                recommendation = {
                    'rank': i,
                    'code': code,
                    'name': data['name'],
                    'sentiment_score': score,
                    'mention_count': mentions,
                    'recommend_level': level,
                    'reason': f"情感得分{score}，提及{mentions}次"
                }

                recommendations.append(recommendation)

                print(f"{i:2d}.  {code:<8} {data['name']:<15} "
                      f"{score:6.2f}   {mentions:4d}      {level}")

            print(f"\n[提示] 共发现 {len(recommendations)} 只股票，已全部显示")
            return recommendations

        except Exception as e:
            print(f"[失败] 生成推荐失败: {str(e)}")
            return []

    def save_results(self, recommendations):
        """保存结果"""
        try:
            # JSON格式
            result_data = {
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_topics': len(self.hot_topics),
                'mentioned_stocks': len(self.stock_mentions),
                'recommendations': recommendations,
                'hot_topics': self.hot_topics,  # 保存所有话题，不限制数量
                'method': '消息面智能选股-正式版'
            }

            with open('消息面选股结果.json', 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

            # CSV格式
            with open('消息面推荐股票.csv', 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['排名', '股票代码', '股票名称', '情感得分', '提及次数', '推荐等级', '推荐理由'])

                for rec in recommendations:
                    writer.writerow([
                        rec['rank'], rec['code'], rec['name'],
                        rec['sentiment_score'], rec['mention_count'],
                        rec['recommend_level'], rec['reason']
                    ])

            print(f"\n[成功] 结果已保存:")
            print(f"  [页面] 消息面选股结果.json")
            print(f"  [数据] 消息面推荐股票.csv")

        except Exception as e:
            print(f"[失败] 保存失败: {str(e)}")

    def run_analysis(self):
        """运行完整分析"""
        try:
            print("消息面智能选股系统 - 正式版")
            print("精确定位: 收藏同行=话题, 相关股=股票, 点击加载更多=郑重声明上方")
            print("=" * 80)

            # 1. 获取话题
            print("\n[步骤1] 获取热门话题...")
            topics = self.get_hot_topics()
            if not topics:
                print("[失败] 未能获取话题")
                return False

            self.hot_topics = topics

            # 2. 分析股票
            mentions = self.analyze_stock_mentions()
            if not mentions:
                print("[失败] 未发现股票")
                return False

            # 3. 批量获取股票名称
            print("\n[股票] 步骤3: 批量获取股票名称...")
            all_codes = list(self.stock_mentions.keys())
            if all_codes:
                self.batch_get_stock_names(all_codes)

                # 更新股票名称
                for code in all_codes:
                    if code in self.stock_name_cache:
                        # 更新sentiment_scores中的名称
                        if code in self.sentiment_scores:
                            self.sentiment_scores[code]['name'] = self.stock_name_cache[code]

            # 4. 计算得分
            scores = self.calculate_sentiment_scores()

            # 5. 生成推荐
            print("\n[目标] 步骤5: 生成选股推荐...")
            recommendations = self.generate_recommendations()

            # 6. 保存结果
            if recommendations:
                print("\n[保存] 步骤6: 保存分析结果...")
                self.save_results(recommendations)

                print(f"\n[完成] 消息面智能选股完成！")
                print(f"[数据] 推荐股票: {len(recommendations)} 只")
                print(f"[提示] 建议结合技术面分析进行投资决策")
                return True
            else:
                print("[失败] 未生成推荐")
                return False

        except Exception as e:
            print(f"[失败] 分析失败: {str(e)}")
            return False

def main():
    """主函数"""
    selector = NewsBasedStockSelector()
    success = selector.run_analysis()

    if success:
        print(f"\n[成功] 消息面智能选股成功完成")
    else:
        print(f"\n[失败] 消息面智能选股失败")

if __name__ == "__main__":
    main()

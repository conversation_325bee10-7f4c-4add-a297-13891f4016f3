#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文图表生成器
专门处理matplotlib中文显示问题
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
import os

def setup_chinese_font():
    """设置中文字体"""
    try:
        # 方法1: 尝试使用系统字体
        font_paths = [
            'C:/Windows/Fonts/msyh.ttc',  # Microsoft YaHei
            'C:/Windows/Fonts/simhei.ttf',  # SimHei
            'C:/Windows/Fonts/simsun.ttc',  # SimSun
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                prop = fm.FontProperties(fname=font_path)
                plt.rcParams['font.family'] = prop.get_name()
                print(f"✅ 成功设置中文字体: {prop.get_name()}")
                return prop
        
        # 方法2: 使用系统字体名称
        chinese_fonts = ['Microsoft YaHei', 'SimHei', 'KaiTi', 'FangSong']
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        
        for font_name in chinese_fonts:
            if font_name in available_fonts:
                plt.rcParams['font.sans-serif'] = [font_name]
                plt.rcParams['axes.unicode_minus'] = False
                print(f"✅ 成功设置中文字体: {font_name}")
                return fm.FontProperties(family=font_name)
        
        print("⚠️ 未找到合适的中文字体，使用默认字体")
        return None
        
    except Exception as e:
        print(f"❌ 字体设置失败: {str(e)}")
        return None

def create_sector_chart_with_chinese(popularity_sectors, soaring_sectors):
    """创建带中文的板块分布图"""
    font_prop = setup_chinese_font()
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 人气榜板块分布
    if popularity_sectors:
        sectors = list(popularity_sectors.keys())
        counts = list(popularity_sectors.values())
        colors = plt.cm.Set3(np.linspace(0, 1, len(sectors)))
        
        bars1 = ax1.bar(sectors, counts, color=colors)
        
        if font_prop:
            ax1.set_title('人气榜板块分布', fontsize=14, fontweight='bold', fontproperties=font_prop)
            ax1.set_xlabel('板块', fontproperties=font_prop)
            ax1.set_ylabel('股票数量', fontproperties=font_prop)
            
            # 设置刻度标签
            ax1.set_xticklabels(sectors, fontproperties=font_prop, rotation=45)
        else:
            ax1.set_title('Popularity Sector Distribution', fontsize=14, fontweight='bold')
            ax1.set_xlabel('Sector')
            ax1.set_ylabel('Stock Count')
            ax1.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, count in zip(bars1, counts):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{count}', ha='center', va='bottom')
    
    # 飙升榜板块分布
    if soaring_sectors:
        sectors = list(soaring_sectors.keys())
        counts = list(soaring_sectors.values())
        colors = plt.cm.Set3(np.linspace(0, 1, len(sectors)))
        
        bars2 = ax2.bar(sectors, counts, color=colors)
        
        if font_prop:
            ax2.set_title('飙升榜板块分布', fontsize=14, fontweight='bold', fontproperties=font_prop)
            ax2.set_xlabel('板块', fontproperties=font_prop)
            ax2.set_ylabel('股票数量', fontproperties=font_prop)
            
            # 设置刻度标签
            ax2.set_xticklabels(sectors, fontproperties=font_prop, rotation=45)
        else:
            ax2.set_title('Soaring Sector Distribution', fontsize=14, fontweight='bold')
            ax2.set_xlabel('Sector')
            ax2.set_ylabel('Stock Count')
            ax2.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, count in zip(bars2, counts):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{count}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 确保charts目录存在
    os.makedirs('charts', exist_ok=True)
    
    plt.savefig('charts/sector_distribution.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()
    print("✅ 板块分布图已保存: charts/sector_distribution.png")

def create_volatility_chart_with_chinese(popularity_volatility, soaring_volatility):
    """创建带中文的波动性分析图"""
    font_prop = setup_chinese_font()
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 人气榜波动性分布
    if popularity_volatility:
        categories = list(popularity_volatility.keys())
        counts = list(popularity_volatility.values())
        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4']
        
        wedges, texts, autotexts = ax1.pie(counts, labels=categories, colors=colors[:len(categories)], 
                                          autopct='%1.1f%%', startangle=90)
        
        if font_prop:
            ax1.set_title('人气榜波动性分布', fontsize=14, fontweight='bold', fontproperties=font_prop)
            # 设置标签字体
            for text in texts:
                text.set_fontproperties(font_prop)
        else:
            ax1.set_title('Popularity Volatility Distribution', fontsize=14, fontweight='bold')
    
    # 飙升榜波动性分布
    if soaring_volatility:
        categories = list(soaring_volatility.keys())
        counts = list(soaring_volatility.values())
        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4']
        
        wedges, texts, autotexts = ax2.pie(counts, labels=categories, colors=colors[:len(categories)], 
                                          autopct='%1.1f%%', startangle=90)
        
        if font_prop:
            ax2.set_title('飙升榜波动性分布', fontsize=14, fontweight='bold', fontproperties=font_prop)
            # 设置标签字体
            for text in texts:
                text.set_fontproperties(font_prop)
        else:
            ax2.set_title('Soaring Volatility Distribution', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    
    # 确保charts目录存在
    os.makedirs('charts', exist_ok=True)
    
    plt.savefig('charts/volatility_analysis.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    print("✅ 波动性分析图已保存: charts/volatility_analysis.png")

def create_sentiment_chart_with_chinese(sentiment_data):
    """创建带中文的市场情绪图"""
    font_prop = setup_chinese_font()
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 情绪分布饼图
    if font_prop:
        labels = ['上涨', '下跌', '平盘']
    else:
        labels = ['Rising', 'Falling', 'Flat']
    
    sizes = [sentiment_data['positive_count'], 
            sentiment_data['negative_count'], 
            sentiment_data['neutral_count']]
    colors = ['#2ecc71', '#e74c3c', '#95a5a6']
    
    wedges, texts, autotexts = ax1.pie(sizes, labels=labels, colors=colors, 
                                      autopct='%1.1f%%', startangle=90)
    
    if font_prop:
        ax1.set_title('市场情绪分布', fontsize=14, fontweight='bold', fontproperties=font_prop)
        for text in texts:
            text.set_fontproperties(font_prop)
    else:
        ax1.set_title('Market Sentiment Distribution', fontsize=14, fontweight='bold')
    
    # 强弱势股对比
    if font_prop:
        categories = ['强势股 (≥3%)', '弱势股 (≤-3%)', '其他']
    else:
        categories = ['Strong (≥3%)', 'Weak (≤-3%)', 'Others']
    
    counts = [sentiment_data['strong_stocks'], 
             sentiment_data['weak_stocks'],
             sentiment_data['positive_count'] + sentiment_data['negative_count'] + 
             sentiment_data['neutral_count'] - sentiment_data['strong_stocks'] - 
             sentiment_data['weak_stocks']]
    colors = ['#27ae60', '#c0392b', '#7f8c8d']
    
    bars = ax2.bar(categories, counts, color=colors)
    
    if font_prop:
        ax2.set_title('强弱势股分布', fontsize=14, fontweight='bold', fontproperties=font_prop)
        ax2.set_ylabel('股票数量', fontproperties=font_prop)
        ax2.set_xticklabels(categories, fontproperties=font_prop, rotation=15)
    else:
        ax2.set_title('Strong vs Weak Stock Distribution', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Stock Count')
        ax2.tick_params(axis='x', rotation=15)
    
    # 添加数值标签
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{count}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 确保charts目录存在
    os.makedirs('charts', exist_ok=True)
    
    plt.savefig('charts/market_sentiment.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    print("✅ 市场情绪图已保存: charts/market_sentiment.png")

def create_risk_radar_chart_with_chinese(risk_data):
    """创建带中文的风险评估雷达图"""
    font_prop = setup_chinese_font()
    
    fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))
    
    # 风险指标
    if font_prop:
        categories = ['整体波动率', '下行波动率', 'VaR风险', '极端上涨风险', '极端下跌风险']
    else:
        categories = ['Overall Vol', 'Downside Vol', 'VaR Risk', 'Extreme Up', 'Extreme Down']
    
    # 标准化数值 (0-10分)
    values = [
        min(risk_data['volatility'] * 2, 10),  # 波动率 * 2
        min(risk_data['downside_volatility'] * 2, 10),  # 下行波动率 * 2
        min(abs(risk_data['var_95']) * 2, 10),  # VaR绝对值 * 2
        min(risk_data['extreme_positive'] / 5, 10),  # 极端上涨股票数 / 5
        min(risk_data['extreme_negative'] / 5, 10)   # 极端下跌股票数 / 5
    ]
    
    # 角度
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    values += values[:1]  # 闭合图形
    angles += angles[:1]
    
    ax.plot(angles, values, 'o-', linewidth=2, color='#e74c3c')
    ax.fill(angles, values, alpha=0.25, color='#e74c3c')
    ax.set_xticks(angles[:-1])
    
    if font_prop:
        ax.set_xticklabels(categories, fontproperties=font_prop)
        ax.set_title('风险评估雷达图', fontsize=16, fontweight='bold', 
                    fontproperties=font_prop, pad=20)
    else:
        ax.set_xticklabels(categories)
        ax.set_title('Risk Assessment Radar Chart', fontsize=16, fontweight='bold', pad=20)
    
    ax.set_ylim(0, 10)
    ax.grid(True)
    
    plt.tight_layout()
    
    # 确保charts目录存在
    os.makedirs('charts', exist_ok=True)
    
    plt.savefig('charts/risk_assessment.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    print("✅ 风险评估雷达图已保存: charts/risk_assessment.png")

if __name__ == "__main__":
    # 测试字体设置
    setup_chinese_font()
    print("中文图表生成器测试完成")

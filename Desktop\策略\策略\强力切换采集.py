#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强力切换采集脚本
使用更强力的方法切换标签页并采集数据
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
import time
import csv
import re

def setup_driver():
    """配置并启动Chrome浏览器"""
    print("🚀 启动浏览器...")
    
    chrome_options = Options()
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--log-level=3')
    chrome_options.add_argument('--silent')
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    return driver

def force_click_tab(driver, tab_name):
    """强力点击标签页"""
    print(f"💪 强力点击 {tab_name} 标签页...")
    
    # 记录点击前的页面内容
    before_content = driver.page_source[:2000]
    
    # 尝试多种强力方法
    methods = [
        ("键盘导航", lambda: keyboard_navigate_to_tab(driver, tab_name)),
        ("坐标点击", lambda: coordinate_click_tab(driver, tab_name)),
        ("JavaScript强制点击", lambda: js_force_click_tab(driver, tab_name)),
        ("模拟用户行为", lambda: simulate_user_click_tab(driver, tab_name))
    ]
    
    for method_name, method_func in methods:
        try:
            print(f"  尝试{method_name}...")
            success = method_func()
            
            if success:
                time.sleep(5)  # 等待页面响应
                
                # 验证是否真正切换
                after_content = driver.page_source[:2000]
                if after_content != before_content:
                    print(f"  ✅ {method_name}成功，页面内容已改变")
                    return True
                else:
                    print(f"  ⚠️ {method_name}执行了但页面内容未改变")
            else:
                print(f"  ❌ {method_name}执行失败")
                
        except Exception as e:
            print(f"  ❌ {method_name}出错: {str(e)[:50]}")
            continue
    
    return False

def keyboard_navigate_to_tab(driver, tab_name):
    """使用键盘导航到标签页"""
    try:
        # 按Tab键导航到标签页区域
        body = driver.find_element(By.TAG_NAME, "body")
        for _ in range(10):
            body.send_keys(Keys.TAB)
            time.sleep(0.2)
            
            # 检查当前焦点元素是否包含目标文本
            try:
                active_element = driver.switch_to.active_element
                if tab_name in active_element.text:
                    active_element.send_keys(Keys.ENTER)
                    return True
            except:
                continue
                
        return False
    except:
        return False

def coordinate_click_tab(driver, tab_name):
    """使用坐标点击标签页"""
    try:
        # 查找包含目标文本的元素
        elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{tab_name}')]")
        
        for element in elements:
            if element.is_displayed():
                # 获取元素位置和大小
                location = element.location
                size = element.size
                
                # 计算点击坐标（元素中心）
                x = location['x'] + size['width'] // 2
                y = location['y'] + size['height'] // 2
                
                # 使用ActionChains进行坐标点击
                ActionChains(driver).move_by_offset(x, y).click().perform()
                
                # 重置鼠标位置
                ActionChains(driver).move_by_offset(-x, -y).perform()
                
                return True
                
        return False
    except:
        return False

def js_force_click_tab(driver, tab_name):
    """使用JavaScript强制点击"""
    try:
        # 使用JavaScript查找并点击元素
        js_code = f"""
        var elements = document.querySelectorAll('*');
        for (var i = 0; i < elements.length; i++) {{
            var element = elements[i];
            if (element.textContent && element.textContent.includes('{tab_name}')) {{
                // 尝试多种点击方式
                try {{
                    element.click();
                    return true;
                }} catch(e1) {{
                    try {{
                        var event = new MouseEvent('click', {{
                            view: window,
                            bubbles: true,
                            cancelable: true
                        }});
                        element.dispatchEvent(event);
                        return true;
                    }} catch(e2) {{
                        try {{
                            element.focus();
                            var enterEvent = new KeyboardEvent('keydown', {{
                                key: 'Enter',
                                code: 'Enter',
                                keyCode: 13
                            }});
                            element.dispatchEvent(enterEvent);
                            return true;
                        }} catch(e3) {{
                            continue;
                        }}
                    }}
                }}
            }}
        }}
        return false;
        """
        
        result = driver.execute_script(js_code)
        return result
        
    except:
        return False

def simulate_user_click_tab(driver, tab_name):
    """模拟真实用户点击行为"""
    try:
        elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{tab_name}')]")
        
        for element in elements:
            if element.is_displayed() and element.is_enabled():
                # 模拟真实用户行为：先移动到元素，然后点击
                actions = ActionChains(driver)
                
                # 移动到元素
                actions.move_to_element(element)
                time.sleep(0.5)
                
                # 点击并保持一小段时间
                actions.click_and_hold(element)
                time.sleep(0.1)
                actions.release(element)
                
                # 执行动作
                actions.perform()
                
                return True
                
        return False
    except:
        return False

def extract_stock_data_improved(driver, target_count=100):
    """改进的数据提取方法"""
    print(f"📊 改进数据提取，目标 {target_count} 条...")
    
    # 等待页面稳定
    time.sleep(3)
    
    # 直接从页面源码提取，使用更精确的正则表达式
    page_source = driver.page_source
    
    # 查找股票数据的多种模式
    patterns = [
        # 模式1: 标准的6位股票代码
        r'(\d{6})',
    ]
    
    all_codes = set()
    
    for pattern in patterns:
        matches = re.findall(pattern, page_source)
        for code in matches:
            if len(code) == 6 and code.startswith(('00', '30', '60')):
                all_codes.add(code)
    
    # 转换为列表并限制数量
    stock_codes = list(all_codes)[:target_count]
    
    # 构造数据格式
    data = []
    for i, code in enumerate(stock_codes):
        data.append([code, f"股票{i+1}", ""])
        
    print(f"✅ 提取到 {len(data)} 条股票代码")
    return data

def save_data(data, filename, tab_name):
    """保存数据到CSV文件"""
    if data:
        with open(filename, "w", newline="", encoding="utf-8") as f:
            writer = csv.writer(f)
            writer.writerow(["code", "name", "change"])
            writer.writerows(data)
        print(f"✅ {filename} 已保存，共 {len(data)} 条数据")
        return True
    else:
        print(f"❌ {tab_name} 未提取到有效数据")
        return False

def main():
    """主函数"""
    driver = setup_driver()
    
    try:
        # 访问网站
        print("🌐 访问东方财富VIP网站...")
        driver.get("https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock")
        print("⏳ 页面加载中...")
        time.sleep(25)  # 充分等待页面加载
        
        # 第一步：采集当前页面数据（假设是人气榜）
        print("\n" + "="*60)
        print("📊 第一步：采集当前页面数据（人气榜）")
        print("="*60)
        
        popularity_data = extract_stock_data_improved(driver, 100)
        save_data(popularity_data, "popularity.csv", "人气榜")
        
        # 第二步：强力切换到飙升榜
        print("\n" + "="*60)
        print("🚀 第二步：强力切换到飙升榜")
        print("="*60)
        
        switch_success = force_click_tab(driver, "飙升榜")
        
        if switch_success:
            print("✅ 成功切换到飙升榜")
            
            # 等待页面稳定
            time.sleep(5)
            
            # 采集飙升榜数据
            soaring_data = extract_stock_data_improved(driver, 100)
            save_data(soaring_data, "soaring.csv", "飙升榜")
            
            # 验证数据差异
            if popularity_data and soaring_data:
                pop_codes = set(item[0] for item in popularity_data)
                soar_codes = set(item[0] for item in soaring_data)
                common_codes = pop_codes & soar_codes
                
                print(f"\n📊 数据验证:")
                print(f"  人气榜股票数: {len(pop_codes)}")
                print(f"  飙升榜股票数: {len(soar_codes)}")
                print(f"  重复股票数: {len(common_codes)}")
                print(f"  数据差异度: {(len(pop_codes | soar_codes) - len(common_codes)) / len(pop_codes | soar_codes) * 100:.1f}%")
                
                if len(common_codes) < len(pop_codes) * 0.8:  # 如果重复度小于80%
                    print("✅ 两个榜单数据存在明显差异，切换成功")
                else:
                    print("⚠️ 两个榜单数据相似度较高，可能切换不完全")
        else:
            print("❌ 无法切换到飙升榜")
        
        print("\n🎉 采集任务完成！")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n🔚 正在关闭浏览器...")
        driver.quit()
        print("✅ 浏览器已关闭")

if __name__ == "__main__":
    main()

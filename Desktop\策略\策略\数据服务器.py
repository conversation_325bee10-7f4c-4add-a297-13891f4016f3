#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据服务器
为Web可视化提供数据API服务
"""

import json
import csv
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs, unquote
import threading
import time

class DataServer(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path

            # API路由处理
            if path.startswith('/api/'):
                # 设置CORS头
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                self.end_headers()

                if path == '/api/stocks':
                    self.handle_stocks_api()
                elif path == '/api/selection':
                    self.handle_selection_api()
                elif path == '/api/status':
                    self.handle_status_api()
                else:
                    self.send_error_response("API endpoint not found")
            else:
                # 静态文件处理
                self.handle_static_file(path)

        except Exception as e:
            self.send_error_response(str(e))
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def handle_stocks_api(self):
        """处理股票数据API"""
        try:
            stocks_data = self.load_stocks_data()
            response = {
                'success': True,
                'data': stocks_data,
                'timestamp': time.time()
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        except Exception as e:
            self.send_error_response(f"Failed to load stocks data: {str(e)}")
    
    def handle_selection_api(self):
        """处理选股结果API"""
        try:
            selection_data = self.load_selection_data()
            response = {
                'success': True,
                'data': selection_data,
                'timestamp': time.time()
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        except Exception as e:
            self.send_error_response(f"Failed to load selection data: {str(e)}")
    
    def handle_status_api(self):
        """处理系统状态API"""
        try:
            status_data = self.get_system_status()
            response = {
                'success': True,
                'data': status_data,
                'timestamp': time.time()
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        except Exception as e:
            self.send_error_response(f"Failed to get system status: {str(e)}")
    
    def load_stocks_data(self):
        """加载股票数据"""
        stocks_data = {
            'popularity': [],
            'soaring': []
        }
        
        # 加载人气榜数据
        if os.path.exists('popularity.csv'):
            with open('popularity.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                stocks_data['popularity'] = list(reader)
        
        # 加载飙升榜数据
        if os.path.exists('soaring.csv'):
            with open('soaring.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                stocks_data['soaring'] = list(reader)
        
        return stocks_data
    
    def load_selection_data(self):
        """加载选股数据"""
        if not os.path.exists('选股结果.json'):
            return None
        
        with open('选股结果.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def get_system_status(self):
        """获取系统状态"""
        status = {
            'data_files': {},
            'reports': {},
            'last_update': None
        }
        
        # 检查数据文件
        data_files = ['popularity.csv', 'soaring.csv', '选股结果.json']
        for file in data_files:
            if os.path.exists(file):
                stat = os.stat(file)
                status['data_files'][file] = {
                    'exists': True,
                    'size': stat.st_size,
                    'modified': stat.st_mtime
                }
                
                # 更新最后更新时间
                if status['last_update'] is None or stat.st_mtime > status['last_update']:
                    status['last_update'] = stat.st_mtime
            else:
                status['data_files'][file] = {'exists': False}
        
        # 检查报告文件
        report_files = ['智能选股分析报告.html', '股票行业整合报告.html']
        for file in report_files:
            if os.path.exists(file):
                stat = os.stat(file)
                status['reports'][file] = {
                    'exists': True,
                    'size': stat.st_size,
                    'modified': stat.st_mtime
                }
            else:
                status['reports'][file] = {'exists': False}
        
        return status
    
    def handle_static_file(self, path):
        """处理静态文件请求"""
        try:
            # 移除开头的斜杠
            if path.startswith('/'):
                path = path[1:]

            # 如果是根路径，直接返回可视化界面内容
            if path == '' or path == '/':
                self.serve_visualization_page()
                return

            # URL解码（处理中文文件名）
            try:
                decoded_path = unquote(path, encoding='utf-8')
                # 如果解码后的路径对应的文件存在，使用解码后的路径
                if os.path.exists(decoded_path):
                    path = decoded_path
                # 否则尝试原路径
                elif not os.path.exists(path):
                    # 如果都不存在，尝试一些常见的文件名映射
                    if 'E5%8F%AF%E8%A7%86%E5%8C%96' in path:  # 检测到是可视化界面的编码
                        path = '可视化界面_实时版.html'
            except:
                # 解码失败，使用原路径
                pass

            # 检查文件是否存在
            if not os.path.exists(path):
                self.send_response(404)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(f"<h1>404 - 文件不存在</h1><p>找不到文件: {path}</p>".encode('utf-8'))
                return

            # 根据文件扩展名设置Content-Type
            if path.endswith('.html'):
                content_type = 'text/html; charset=utf-8'
            elif path.endswith('.css'):
                content_type = 'text/css; charset=utf-8'
            elif path.endswith('.js'):
                content_type = 'application/javascript; charset=utf-8'
            elif path.endswith('.json'):
                content_type = 'application/json; charset=utf-8'
            else:
                content_type = 'text/plain; charset=utf-8'

            # 读取并发送文件
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()

            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            error_html = f"<h1>500 - 服务器错误</h1><p>错误信息: {str(e)}</p>"
            self.wfile.write(error_html.encode('utf-8'))

    def serve_visualization_page(self):
        """直接提供可视化界面内容"""
        try:
            # 优先尝试读取index.html
            html_files = ['index.html', '可视化界面_实时版.html']

            for html_file in html_files:
                if os.path.exists(html_file):
                    with open(html_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    self.send_response(200)
                    self.send_header('Content-type', 'text/html; charset=utf-8')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()
                    self.wfile.write(content.encode('utf-8'))
                    return

            # 如果都不存在，返回备用页面
                # 如果文件不存在，返回简单的重定向页面
                redirect_html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 股票数据服务器</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 50px;
            text-align: center;
            color: white;
        }}
        .container {{
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            max-width: 600px;
            margin: 0 auto;
        }}
        h1 {{ font-size: 2.5em; margin-bottom: 20px; }}
        .status {{ font-size: 1.2em; margin: 20px 0; }}
        .api-list {{ text-align: left; margin: 30px 0; }}
        .api-item {{
            background: rgba(255,255,255,0.2);
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
        }}
        a {{ color: #fff; text-decoration: none; }}
        a:hover {{ text-decoration: underline; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 股票数据服务器</h1>
        <div class="status">✅ 服务器运行正常</div>

        <div class="api-list">
            <h3>📊 可用的API端点:</h3>
            <div class="api-item">
                <a href="/api/status">🔍 /api/status</a> - 系统状态
            </div>
            <div class="api-item">
                <a href="/api/stocks">📈 /api/stocks</a> - 股票数据
            </div>
            <div class="api-item">
                <a href="/api/selection">🎯 /api/selection</a> - 选股结果
            </div>
        </div>

        <p>💡 提示: 可视化界面文件未找到，请确保 '可视化界面_实时版.html' 文件存在</p>
    </div>
</body>
</html>
                """

                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(redirect_html.encode('utf-8'))

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            error_html = f"<h1>500 - 服务器错误</h1><p>错误信息: {str(e)}</p>"
            self.wfile.write(error_html.encode('utf-8'))

    def send_error_response(self, message):
        """发送错误响应"""
        response = {
            'success': False,
            'error': message,
            'timestamp': time.time()
        }
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def log_message(self, format, *args):
        """重写日志方法，减少输出"""
        pass

class DataServerManager:
    def __init__(self, port=8080):
        self.port = port
        self.server = None
        self.server_thread = None
        self.running = False
    
    def start_server(self):
        """启动数据服务器"""
        if self.running:
            print(f"数据服务器已在端口 {self.port} 运行")
            return True
        
        try:
            self.server = HTTPServer(('localhost', self.port), DataServer)
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            self.running = True
            
            print(f"✅ 数据服务器已启动")
            print(f"🌐 服务地址: http://localhost:{self.port}")
            print(f"📊 API端点:")
            print(f"  • http://localhost:{self.port}/api/stocks - 股票数据")
            print(f"  • http://localhost:{self.port}/api/selection - 选股结果")
            print(f"  • http://localhost:{self.port}/api/status - 系统状态")
            
            return True
            
        except Exception as e:
            print(f"❌ 启动数据服务器失败: {str(e)}")
            return False
    
    def stop_server(self):
        """停止数据服务器"""
        if not self.running:
            print("数据服务器未运行")
            return
        
        try:
            if self.server:
                self.server.shutdown()
                self.server.server_close()
            
            self.running = False
            print("✅ 数据服务器已停止")
            
        except Exception as e:
            print(f"❌ 停止数据服务器失败: {str(e)}")
    
    def is_running(self):
        """检查服务器是否运行"""
        return self.running
    
    def get_server_info(self):
        """获取服务器信息"""
        return {
            'running': self.running,
            'port': self.port,
            'url': f"http://localhost:{self.port}" if self.running else None
        }

# 全局服务器实例
_server_manager = None

def start_data_server(port=8080):
    """启动数据服务器"""
    global _server_manager
    
    if _server_manager is None:
        _server_manager = DataServerManager(port)
    
    return _server_manager.start_server()

def stop_data_server():
    """停止数据服务器"""
    global _server_manager
    
    if _server_manager:
        _server_manager.stop_server()

def get_server_status():
    """获取服务器状态"""
    global _server_manager
    
    if _server_manager:
        return _server_manager.get_server_info()
    else:
        return {'running': False, 'port': None, 'url': None}

def main():
    """主函数 - 用于独立运行"""
    print("🌐 股票数据服务器")
    print("=" * 30)
    
    server_manager = DataServerManager(8080)
    
    try:
        if server_manager.start_server():
            print("\n💡 服务器运行中...")
            print("按 Ctrl+C 停止服务器")
            
            # 保持服务器运行
            while True:
                time.sleep(1)
                
    except KeyboardInterrupt:
        print("\n👋 收到停止信号")
        server_manager.stop_server()
        print("🏁 服务器已关闭")

if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 股票数据实时可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .status-online {
            background: #27ae60;
            color: white;
        }
        
        .status-offline {
            background: #e74c3c;
            color: white;
        }
        
        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .content {
            padding: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(116, 185, 255, 0.3);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .data-section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .stock-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
            transition: all 0.3s ease;
        }
        
        .stock-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .stock-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .stock-code {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stock-change {
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .change-positive {
            background: #e74c3c;
            color: white;
        }
        
        .change-negative {
            background: #27ae60;
            color: white;
        }
        
        .stock-name {
            font-size: 1.1em;
            color: #34495e;
            margin-bottom: 10px;
        }
        
        .stock-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.9em;
            color: #7f8c8d;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #7f8c8d;
        }
        
        .error {
            background: #e74c3c;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .last-update {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .btn {
                text-align: center;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .data-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 股票数据实时可视化</h1>
            <p>实时监控热门股票动态</p>
            <div id="status" class="status-indicator status-offline">离线</div>
        </div>
        
        <div class="controls">
            <div>
                <button class="btn btn-primary" onclick="refreshData()">🔄 刷新数据</button>
                <button class="btn btn-success" onclick="toggleAutoRefresh()">⏰ 自动刷新</button>
                <button class="btn btn-warning" onclick="showSelection()">🎯 查看选股</button>
            </div>
            <div>
                <span>刷新间隔: </span>
                <select id="refreshInterval" onchange="updateRefreshInterval()">
                    <option value="30">30秒</option>
                    <option value="60" selected>1分钟</option>
                    <option value="300">5分钟</option>
                    <option value="600">10分钟</option>
                </select>
            </div>
        </div>
        
        <div class="content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalStocks">--</div>
                    <div class="stat-label">总股票数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="popularityCount">--</div>
                    <div class="stat-label">人气榜</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="soaringCount">--</div>
                    <div class="stat-label">飙升榜</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="risingCount">--</div>
                    <div class="stat-label">上涨股票</div>
                </div>
            </div>
            
            <div class="data-section">
                <h2 class="section-title">🔥 人气榜股票</h2>
                <div id="popularityData" class="data-grid">
                    <div class="loading">正在加载数据...</div>
                </div>
            </div>
            
            <div class="data-section">
                <h2 class="section-title">🚀 飙升榜股票</h2>
                <div id="soaringData" class="data-grid">
                    <div class="loading">正在加载数据...</div>
                </div>
            </div>
            
            <div class="last-update">
                <span>最后更新: </span>
                <span id="lastUpdate">--</span>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshTimer = null;
        let isAutoRefresh = false;
        
        // API基础URL
        const API_BASE = 'http://localhost:8080/api';
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            updateStatus();
        });
        
        // 刷新数据
        async function refreshData() {
            try {
                updateStatus('loading');
                
                // 获取股票数据
                const response = await fetch(`${API_BASE}/stocks`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    displayData(data.data);
                    updateStats(data.data);
                    updateStatus('online');
                    updateLastUpdate();
                } else {
                    throw new Error(data.error || '数据获取失败');
                }
                
            } catch (error) {
                console.error('数据获取失败:', error);
                showError('数据获取失败: ' + error.message);
                updateStatus('offline');
            }
        }
        
        // 显示数据
        function displayData(data) {
            displayStockList('popularityData', data.popularity || [], '人气榜');
            displayStockList('soaringData', data.soaring || [], '飙升榜');
        }
        
        // 显示股票列表
        function displayStockList(containerId, stocks, type) {
            const container = document.getElementById(containerId);
            
            if (!stocks || stocks.length === 0) {
                container.innerHTML = `<div class="loading">暂无${type}数据</div>`;
                return;
            }
            
            const html = stocks.map(stock => {
                const change = stock.change || '0%';
                const changeClass = change.startsWith('+') ? 'change-positive' : 
                                  change.startsWith('-') ? 'change-negative' : '';
                
                return `
                    <div class="stock-card">
                        <div class="stock-header">
                            <div class="stock-code">${stock.code || 'N/A'}</div>
                            <div class="stock-change ${changeClass}">${change}</div>
                        </div>
                        <div class="stock-name">${stock.name || '未知股票'}</div>
                        <div class="stock-details">
                            <span>💰 ¥${stock.price || '--'}</span>
                            <span>📊 ${type}</span>
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = html;
        }
        
        // 更新统计数据
        function updateStats(data) {
            const popularity = data.popularity || [];
            const soaring = data.soaring || [];
            const total = popularity.length + soaring.length;
            
            // 计算上涨股票数
            const risingCount = [...popularity, ...soaring].filter(stock => {
                const change = stock.change || '0%';
                return change.startsWith('+');
            }).length;
            
            document.getElementById('totalStocks').textContent = total;
            document.getElementById('popularityCount').textContent = popularity.length;
            document.getElementById('soaringCount').textContent = soaring.length;
            document.getElementById('risingCount').textContent = risingCount;
        }
        
        // 更新状态
        function updateStatus(status = 'checking') {
            const statusEl = document.getElementById('status');
            
            switch (status) {
                case 'online':
                    statusEl.textContent = '在线';
                    statusEl.className = 'status-indicator status-online';
                    break;
                case 'offline':
                    statusEl.textContent = '离线';
                    statusEl.className = 'status-indicator status-offline';
                    break;
                case 'loading':
                    statusEl.textContent = '加载中...';
                    statusEl.className = 'status-indicator status-offline';
                    break;
                default:
                    statusEl.textContent = '检查中...';
                    statusEl.className = 'status-indicator status-offline';
            }
        }
        
        // 显示错误
        function showError(message) {
            const errorHtml = `<div class="error">❌ ${message}</div>`;
            document.getElementById('popularityData').innerHTML = errorHtml;
            document.getElementById('soaringData').innerHTML = errorHtml;
        }
        
        // 更新最后更新时间
        function updateLastUpdate() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            document.getElementById('lastUpdate').textContent = timeStr;
        }
        
        // 切换自动刷新
        function toggleAutoRefresh() {
            const btn = event.target;
            
            if (isAutoRefresh) {
                // 停止自动刷新
                clearInterval(autoRefreshTimer);
                autoRefreshTimer = null;
                isAutoRefresh = false;
                btn.textContent = '⏰ 自动刷新';
                btn.className = 'btn btn-success';
            } else {
                // 开始自动刷新
                const interval = parseInt(document.getElementById('refreshInterval').value) * 1000;
                autoRefreshTimer = setInterval(refreshData, interval);
                isAutoRefresh = true;
                btn.textContent = '⏸️ 停止刷新';
                btn.className = 'btn btn-warning';
            }
        }
        
        // 更新刷新间隔
        function updateRefreshInterval() {
            if (isAutoRefresh) {
                // 重新设置定时器
                clearInterval(autoRefreshTimer);
                const interval = parseInt(document.getElementById('refreshInterval').value) * 1000;
                autoRefreshTimer = setInterval(refreshData, interval);
            }
        }
        
        // 查看选股结果
        async function showSelection() {
            try {
                const response = await fetch(`${API_BASE}/selection`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success && data.data) {
                    const strategies = data.data.strategies || {};
                    const totalRecommendations = Object.values(strategies).reduce((sum, stocks) => sum + stocks.length, 0);
                    
                    alert(`🎯 选股结果摘要:\n\n` +
                          `📊 分析股票: ${data.data.total_stocks || 0} 只\n` +
                          `🎯 选股策略: ${Object.keys(strategies).length} 个\n` +
                          `💎 推荐股票: ${totalRecommendations} 只\n\n` +
                          `详细结果请查看选股分析报告`);
                } else {
                    alert('❌ 暂无选股数据\n请先运行智能选股分析');
                }
                
            } catch (error) {
                alert('❌ 获取选股数据失败: ' + error.message);
            }
        }
        
        // 定期检查服务器状态
        setInterval(async function() {
            try {
                const response = await fetch(`${API_BASE}/status`);
                if (response.ok) {
                    updateStatus('online');
                } else {
                    updateStatus('offline');
                }
            } catch (error) {
                updateStatus('offline');
            }
        }, 10000); // 每10秒检查一次
    </script>
</body>
</html>

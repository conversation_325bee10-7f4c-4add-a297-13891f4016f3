#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI选股报告生成器
生成AI智能选股分析的HTML可视化报告
"""

import json
import os
from datetime import datetime

def generate_ai_stock_report():
    """生成AI选股HTML报告"""
    try:
        print("📊 生成AI选股分析报告...")
        
        # 读取AI分析结果
        if not os.path.exists('AI智能选股分析结果.json'):
            print("❌ 未找到AI分析结果文件，请先运行AI分析")
            return False
        
        with open('AI智能选股分析结果.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        analysis_time = data.get('analysis_time', '')
        data_sources = data.get('data_sources', {})
        complete_analysis = data.get('complete_analysis', [])
        final_recommendations = data.get('final_recommendations', {})
        
        # 生成HTML内容
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能选股分析报告</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .header p {{
            font-size: 1.1em;
            opacity: 0.9;
        }}
        
        .content {{
            padding: 30px;
        }}
        
        .section {{
            margin-bottom: 40px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #3498db;
        }}
        
        .section h2 {{
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }}
        
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-top: 4px solid #3498db;
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }}
        
        .stat-label {{
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        
        .recommendation-section {{
            margin-bottom: 30px;
        }}
        
        .recommendation-header {{
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px 10px 0 0;
            font-size: 1.3em;
            font-weight: bold;
        }}
        
        .stock-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            padding: 20px;
            background: white;
            border-radius: 0 0 10px 10px;
        }}
        
        .stock-card {{
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }}
        
        .stock-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }}
        
        .stock-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }}
        
        .stock-code {{
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }}
        
        .ai-score {{
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }}
        
        .stock-details {{
            font-size: 0.9em;
            color: #7f8c8d;
        }}
        
        .detail-item {{
            margin-bottom: 5px;
        }}
        
        .risk-low {{ border-left-color: #27ae60; }}
        .risk-medium-low {{ border-left-color: #f39c12; }}
        .risk-medium {{ border-left-color: #e67e22; }}
        .risk-medium-high {{ border-left-color: #e74c3c; }}
        .risk-high {{ border-left-color: #c0392b; }}
        
        .footer {{
            background: #34495e;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 0.9em;
        }}
        
        .warning {{
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }}
        
        @media (max-width: 768px) {{
            .stats-grid {{
                grid-template-columns: repeat(2, 1fr);
            }}
            
            .stock-grid {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI智能选股分析报告</h1>
            <p>基于热门股和消息面的综合AI分析 | 生成时间: {analysis_time}</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📊 数据概览</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">{data_sources.get('hot_stocks_count', 0)}</div>
                        <div class="stat-label">热门股数据</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{data_sources.get('news_stocks_count', 0)}</div>
                        <div class="stat-label">消息面股票</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{data_sources.get('combined_count', 0)}</div>
                        <div class="stat-label">合并分析</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{final_recommendations.get('high_score_count', 0)}</div>
                        <div class="stat-label">高分股票</div>
                    </div>
                </div>
            </div>
        """
        
        # 添加推荐股票部分
        recommendations = [
            ('strong_buy', '🔥 强烈建议买入', '#e74c3c'),
            ('buy', '⭐ 建议买入', '#f39c12'),
            ('consider', '👀 可以考虑', '#3498db')
        ]
        
        for rec_type, title, color in recommendations:
            stocks = final_recommendations.get(rec_type, [])
            if stocks:
                html_content += f"""
            <div class="section">
                <div class="recommendation-section">
                    <div class="recommendation-header" style="background: {color};">
                        {title} ({len(stocks)} 只)
                    </div>
                    <div class="stock-grid">
                """
                
                for i, stock in enumerate(stocks, 1):
                    risk_class = get_risk_class(stock.get('risk_level', ''))
                    sources = get_data_sources_display(stock)
                    
                    html_content += f"""
                        <div class="stock-card {risk_class}">
                            <div class="stock-header">
                                <div class="stock-code">#{i} {stock['code']} {stock['name']}</div>
                                <div class="ai-score">AI: {stock['ai_score']}</div>
                            </div>
                            <div class="stock-details">
                                <div class="detail-item">🎯 风险等级: {stock['risk_level']}</div>
                                <div class="detail-item">📊 数据来源: {sources}</div>
                                <div class="detail-item">🤖 AI分析: {stock['ai_reason']}</div>
                                <div class="detail-item">💡 投资建议: {stock['investment_advice']}</div>
                            </div>
                        </div>
                    """
                
                html_content += """
                    </div>
                </div>
            </div>
                """
        
        # 添加页面尾部
        html_content += f"""
            <div class="warning">
                <strong>⚠️ 投资风险提示:</strong><br>
                • 本报告基于AI算法分析，仅供参考，不构成投资建议<br>
                • 股市有风险，投资需谨慎，请根据个人风险承受能力做决策<br>
                • 建议结合更多信息和专业分析师意见进行投资决策<br>
                • 过往表现不代表未来收益，请理性投资
            </div>
        </div>
        
        <div class="footer">
            <p>AI智能选股分析系统 | 数据更新时间: {analysis_time}</p>
            <p>本系统综合分析热门股和消息面数据，使用AI算法生成投资建议</p>
        </div>
    </div>
</body>
</html>
        """
        
        # 保存HTML文件
        with open('AI智能选股分析报告.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("✅ AI选股分析报告生成成功!")
        print("📄 文件: AI智能选股分析报告.html")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成AI选股报告失败: {str(e)}")
        return False

def get_risk_class(risk_level):
    """获取风险等级CSS类"""
    risk_classes = {
        '低风险': 'risk-low',
        '中低风险': 'risk-medium-low',
        '中等风险': 'risk-medium',
        '中高风险': 'risk-medium-high',
        '高风险': 'risk-high'
    }
    return risk_classes.get(risk_level, 'risk-medium')

def get_data_sources_display(stock):
    """获取数据来源显示"""
    sources = []
    if stock.get('has_hot_data'):
        hot_sources = stock.get('hot_sources', [])
        sources.extend(hot_sources)
    if stock.get('has_news_data'):
        sources.append('消息面')
    return ' + '.join(sources) if sources else '未知'

def main():
    """主函数"""
    success = generate_ai_stock_report()
    
    if success:
        print("✅ AI选股报告生成完成")
    else:
        print("❌ AI选股报告生成失败")

if __name__ == "__main__":
    main()

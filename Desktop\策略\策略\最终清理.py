#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终清理脚本
删除剩余的多余文件，只保留核心功能
"""

import os
import shutil

def final_cleanup():
    """最终清理"""
    print("🗑️ 最终清理 - 只保留核心功能")
    print("=" * 50)
    
    # 核心功能文件（必须保留）
    core_files = {
        '可视化主程序.py',
        '后台采集.py',
        '智能选股系统.py',
        '选股报告生成器.py',
        '行业板块分类.py',
        '增强数据显示.py',
        '一键选股.py'
    }
    
    # 数据文件（保留）
    data_files = {
        'popularity.csv',
        'soaring.csv',
        'stock_names_cache.json',
        '选股结果.json',
        '推荐股票汇总.csv'
    }
    
    # 报告文件（保留主要的）
    report_files = {
        '智能选股分析报告.html',
        '股票行业整合报告.html',
        '股票分析报告.html'
    }
    
    # 文档文件（保留）
    doc_files = {
        'README.md',
        '最终项目报告.md'
    }
    
    # 所有要保留的文件
    keep_files = core_files | data_files | report_files | doc_files
    
    # 要删除的文件
    delete_files = [
        '批量清理文件.py',
        '最终清理.py',  # 清理完成后删除自己
        '中文图表生成器.py',
        '数据服务器.py',
        '智能数据分析报告.py',
        '生成图片报告.py',
        '股票名称获取工具.py',
        '股票涨跌幅获取工具.py'
    ]
    
    # 要删除的目录
    delete_dirs = [
        '__pycache__',
        'charts'
    ]
    
    print("📋 清理计划:")
    print(f"✅ 保留核心文件: {len(keep_files)} 个")
    print(f"🗑️ 删除多余文件: {len(delete_files)} 个")
    print(f"🗑️ 删除目录: {len(delete_dirs)} 个")
    
    # 显示要保留的文件
    print(f"\n✅ 保留的文件:")
    for category, files in [
        ("核心功能", core_files),
        ("数据文件", data_files), 
        ("报告文件", report_files),
        ("文档文件", doc_files)
    ]:
        print(f"  📁 {category}:")
        for file in sorted(files):
            if os.path.exists(file):
                size = os.path.getsize(file) / 1024
                print(f"    ✅ {file:<30} ({size:.1f} KB)")
            else:
                print(f"    ❌ {file:<30} (不存在)")
    
    # 显示要删除的文件
    print(f"\n🗑️ 删除的文件:")
    total_delete_size = 0
    for file in delete_files:
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024
            total_delete_size += size
            print(f"  🗑️ {file:<30} ({size:.1f} KB)")
    
    # 显示要删除的目录
    print(f"\n🗑️ 删除的目录:")
    for dir_name in delete_dirs:
        if os.path.exists(dir_name):
            print(f"  📂 {dir_name}/")
    
    print(f"\n📊 预计释放空间: {total_delete_size:.1f} KB")
    
    # 确认删除
    choice = input(f"\n❓ 确认执行最终清理？(y/n): ").strip().lower()
    
    if choice != 'y':
        print("❌ 清理已取消")
        return
    
    # 执行删除
    print(f"\n🗑️ 开始最终清理...")
    deleted_count = 0
    
    # 删除文件
    for file in delete_files:
        try:
            if os.path.exists(file):
                os.remove(file)
                print(f"  ✅ 已删除文件: {file}")
                deleted_count += 1
        except Exception as e:
            print(f"  ❌ 删除文件失败: {file} - {str(e)}")
    
    # 删除目录
    for dir_name in delete_dirs:
        try:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
                print(f"  ✅ 已删除目录: {dir_name}/")
                deleted_count += 1
        except Exception as e:
            print(f"  ❌ 删除目录失败: {dir_name} - {str(e)}")
    
    print(f"\n🎉 最终清理完成！")
    print(f"✅ 成功删除 {deleted_count} 个项目")
    print(f"💾 释放空间 {total_delete_size:.1f} KB")
    
    # 显示最终保留的文件
    print(f"\n📋 最终保留的文件:")
    remaining_files = []
    for file in os.listdir('.'):
        if os.path.isfile(file):
            remaining_files.append(file)
    
    for file in sorted(remaining_files):
        size = os.path.getsize(file) / 1024
        if file in core_files:
            print(f"  🔧 {file:<30} ({size:.1f} KB) - 核心功能")
        elif file in data_files:
            print(f"  📊 {file:<30} ({size:.1f} KB) - 数据文件")
        elif file in report_files:
            print(f"  📋 {file:<30} ({size:.1f} KB) - 报告文件")
        elif file in doc_files:
            print(f"  📝 {file:<30} ({size:.1f} KB) - 文档文件")
        else:
            print(f"  ❓ {file:<30} ({size:.1f} KB) - 其他")
    
    print(f"\n💡 项目已整理完成，只保留核心功能文件！")
    print(f"🚀 现在可以运行 'python 可视化主程序.py' 启动系统")

def main():
    """主函数"""
    final_cleanup()

if __name__ == "__main__":
    main()

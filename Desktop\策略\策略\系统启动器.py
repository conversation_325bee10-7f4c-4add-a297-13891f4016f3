#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据采集系统启动器
简单的功能测试和启动工具
"""

import os
import sys
import subprocess
import time

def show_banner():
    """显示横幅"""
    print("=" * 60)
    print("🚀 股票数据采集系统启动器 v2.0")
    print("📅 功能测试和系统管理工具")
    print("=" * 60)

def show_menu():
    """显示菜单"""
    print("\n📋 功能菜单:")
    print("1. 🔇 后台数据采集")
    print("2. 📊 获取股票名称")
    print("3. 📈 获取涨跌幅数据")
    print("4. 📄 生成分析报告")
    print("5. 🌐 启动数据服务器")
    print("6. 📁 查看数据文件")
    print("7. ⚙️ 系统状态检查")
    print("0. 🚪 退出")

def test_script(script_name, description):
    """测试脚本是否可用"""
    print(f"\n🧪 测试 {description}...")
    
    if not os.path.exists(script_name):
        print(f"❌ 脚本文件不存在: {script_name}")
        return False
    
    print(f"✅ 脚本文件存在: {script_name}")
    
    # 询问是否运行
    choice = input(f"是否运行 {description}? (y/n): ").lower().strip()
    if choice == 'y':
        try:
            print(f"🚀 正在运行 {script_name}...")
            result = subprocess.run([sys.executable, script_name], cwd=os.getcwd())
            if result.returncode == 0:
                print(f"✅ {description} 运行成功")
                return True
            else:
                print(f"❌ {description} 运行失败")
                return False
        except Exception as e:
            print(f"❌ 运行出错: {str(e)}")
            return False
    else:
        print(f"⏭️ 跳过运行 {description}")
        return True

def view_data_files():
    """查看数据文件"""
    print(f"\n📁 数据文件状态:")
    print("-" * 40)
    
    files = [
        ("popularity.csv", "人气榜数据"),
        ("soaring.csv", "飙升榜数据"),
        ("codes.txt", "股票代码库"),
        ("stock_names_cache.json", "股票名称缓存"),
        ("股票分析报告.html", "分析报告")
    ]
    
    for file_path, description in files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            if size < 1024:
                size_str = f"{size} B"
            elif size < 1024 * 1024:
                size_str = f"{size/1024:.1f} KB"
            else:
                size_str = f"{size/(1024*1024):.1f} MB"
            print(f"✅ {description}: {size_str}")
        else:
            print(f"❌ {description}: 不存在")

def check_system():
    """检查系统状态"""
    print(f"\n⚙️ 系统状态检查:")
    print("-" * 40)
    
    # 检查Python版本
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    
    # 检查必要模块
    modules = ["selenium", "requests", "matplotlib", "pandas", "numpy"]
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}: 已安装")
        except ImportError:
            print(f"❌ {module}: 未安装")
    
    # 检查脚本文件
    scripts = [
        ("后台采集.py", "后台数据采集"),
        ("股票名称获取工具.py", "股票名称获取"),
        ("股票涨跌幅获取工具.py", "涨跌幅获取"),
        ("智能数据分析报告.py", "分析报告生成"),
        ("数据服务器.py", "数据服务器"),
        ("可视化界面_实时版.html", "可视化界面")
    ]
    
    print(f"\n📝 脚本文件检查:")
    for script, desc in scripts:
        if os.path.exists(script):
            print(f"✅ {desc}: {script}")
        else:
            print(f"❌ {desc}: {script} (缺失)")

def start_server():
    """启动数据服务器"""
    print(f"\n🌐 启动数据服务器...")
    
    if not os.path.exists("数据服务器.py"):
        print("❌ 数据服务器脚本不存在")
        return
    
    try:
        print("🚀 正在启动服务器...")
        subprocess.Popen([sys.executable, "数据服务器.py"])
        time.sleep(2)
        print("✅ 数据服务器已启动")
        print("🌐 访问地址: http://localhost:8080/可视化界面_实时版.html")
        
        choice = input("是否打开浏览器? (y/n): ").lower().strip()
        if choice == 'y':
            try:
                import webbrowser
                webbrowser.open("http://localhost:8080/可视化界面_实时版.html")
                print("🌐 已在浏览器中打开")
            except:
                print("❌ 无法自动打开浏览器")
    except Exception as e:
        print(f"❌ 启动服务器失败: {str(e)}")

def main():
    """主函数"""
    show_banner()
    
    while True:
        try:
            show_menu()
            choice = input("\n请选择功能 (0-7): ").strip()
            
            if choice == '0':
                print("\n👋 退出系统")
                break
            elif choice == '1':
                test_script("后台采集.py", "后台数据采集")
            elif choice == '2':
                test_script("股票名称获取工具.py", "股票名称获取")
            elif choice == '3':
                test_script("股票涨跌幅获取工具.py", "涨跌幅获取")
            elif choice == '4':
                test_script("智能数据分析报告.py", "智能分析报告")
            elif choice == '5':
                start_server()
            elif choice == '6':
                view_data_files()
            elif choice == '7':
                check_system()
            else:
                print("❌ 无效选择")
            
            if choice != '0':
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"\n❌ 程序出错: {str(e)}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()

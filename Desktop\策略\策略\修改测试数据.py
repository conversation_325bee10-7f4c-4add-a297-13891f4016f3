#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修改测试数据
用于测试刷新功能是否能检测到数据变化
"""

import csv
import os
import random
from datetime import datetime

def modify_csv_data(filename, title):
    """修改CSV数据文件"""
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return False
    
    try:
        # 读取现有数据
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            data = list(reader)
        
        if not data:
            print(f"❌ {filename} 文件为空")
            return False
        
        # 随机修改一些股票的涨跌幅
        modified_count = 0
        for stock in data[:5]:  # 只修改前5只股票
            if 'change' in stock:
                # 生成随机涨跌幅
                change_val = random.uniform(-5.0, 5.0)
                if change_val >= 0:
                    stock['change'] = f"+{change_val:.2f}%"
                else:
                    stock['change'] = f"{change_val:.2f}%"
                modified_count += 1
        
        # 写回文件
        with open(filename, 'w', encoding='utf-8', newline='') as f:
            if data:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
        
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"✅ {title}数据已修改: {modified_count} 只股票 (时间: {timestamp})")
        return True
        
    except Exception as e:
        print(f"❌ 修改{title}数据失败: {str(e)}")
        return False

def add_test_comment():
    """在数据文件中添加测试注释"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 创建一个测试标记文件
    with open('last_refresh_test.txt', 'w', encoding='utf-8') as f:
        f.write(f"最后刷新测试时间: {timestamp}\n")
        f.write(f"测试目的: 验证刷新功能是否能检测到数据变化\n")
    
    print(f"📝 测试标记已创建: {timestamp}")

def main():
    """主函数"""
    print("🔧 修改测试数据工具")
    print("=" * 40)
    print(f"📅 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 修改人气榜数据
    success1 = modify_csv_data("popularity.csv", "人气榜")
    
    # 修改飙升榜数据
    success2 = modify_csv_data("soaring.csv", "飙升榜")
    
    # 添加测试标记
    add_test_comment()
    
    if success1 or success2:
        print(f"\n🎉 数据修改完成！")
        print(f"💡 现在可以在可视化主程序中点击'🔄 刷新数据显示'按钮")
        print(f"   查看是否能检测到数据变化和时间更新")
        print(f"\n🔍 检查要点:")
        print(f"  • 文件更新时间是否变化")
        print(f"  • 股票涨跌幅数据是否更新")
        print(f"  • 刷新时间是否更新")
        print(f"  • 加载次数是否增加")
    else:
        print(f"\n❌ 数据修改失败")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键选股系统
整合数据采集、选股分析、报告生成的完整流程
"""

import os
import sys
import subprocess
import webbrowser
from datetime import datetime

def print_banner():
    """打印横幅"""
    print("=" * 70)
    print("🎯 一键选股系统")
    print("从热门股票数据采集到智能选股分析的完整解决方案")
    print("=" * 70)
    print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def run_script(script_name, description):
    """运行脚本"""
    try:
        if not os.path.exists(script_name):
            print(f"❌ 脚本不存在: {script_name}")
            return False
        
        print(f"🚀 正在执行: {description}")
        print("-" * 50)
        
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=False, text=True)
        
        if result.returncode == 0:
            print(f"✅ {description} 执行完成")
            return True
        else:
            print(f"❌ {description} 执行失败")
            return False
            
    except Exception as e:
        print(f"❌ 执行 {description} 时出错: {str(e)}")
        return False

def check_data_files():
    """检查数据文件是否存在"""
    required_files = ['popularity.csv', 'soaring.csv']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("⚠️ 缺少数据文件:", ', '.join(missing_files))
        print("💡 建议先运行数据采集获取最新数据")
        return False
    else:
        print("✅ 数据文件检查通过")
        return True

def open_reports():
    """打开生成的报告"""
    reports = [
        ('智能选股分析报告.html', '智能选股分析报告'),
        ('股票行业整合报告.html', '股票行业整合报告'),
        ('股票分析报告.html', '股票分析报告'),
        ('消息面选股分析报告.html', '消息面选股分析报告')
    ]
    
    print("\n🌐 打开分析报告...")
    opened_count = 0
    
    for filename, description in reports:
        if os.path.exists(filename):
            try:
                abs_path = os.path.abspath(filename)
                url = f"file:///{abs_path.replace(os.sep, '/')}"
                webbrowser.open(url)
                print(f"  ✅ {description}")
                opened_count += 1
            except Exception as e:
                print(f"  ❌ {description}: {str(e)}")
        else:
            print(f"  ⚠️ {description}: 文件不存在")
    
    print(f"📊 共打开 {opened_count} 个报告")

def show_selection_summary():
    """显示选股结果摘要"""
    try:
        import json
        
        if os.path.exists('选股结果.json'):
            with open('选股结果.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print("\n📊 选股结果摘要:")
            print("-" * 40)
            print(f"📈 分析股票总数: {data['total_stocks']} 只")
            print(f"🎯 选股策略数量: {len(data['strategies'])} 个")
            
            total_recommendations = sum(len(stocks) for stocks in data['strategies'].values())
            print(f"💎 推荐股票总数: {total_recommendations} 只")
            
            # 统计各策略推荐数量
            strategy_names = {
                'momentum': '动量选股',
                'value': '价值选股', 
                'growth': '成长选股',
                'balanced': '均衡选股',
                'hot_sector': '热门板块选股',
                'risk_control': '风险控制选股'
            }
            
            print("\n📋 各策略推荐数量:")
            for strategy, stocks in data['strategies'].items():
                name = strategy_names.get(strategy, strategy)
                print(f"  • {name}: {len(stocks)} 只")
            
            # 找出共识推荐
            stock_votes = {}
            for strategy, stocks in data['strategies'].items():
                for stock in stocks:
                    code = stock['code']
                    if code not in stock_votes:
                        stock_votes[code] = {'stock': stock, 'votes': 0}
                    stock_votes[code]['votes'] += 1
            
            consensus_picks = [item for item in stock_votes.values() if item['votes'] >= 2]
            consensus_picks.sort(key=lambda x: x['votes'], reverse=True)
            
            print(f"\n🏆 多策略共识推荐: {len(consensus_picks)} 只")
            if consensus_picks:
                print("  前5只共识股票:")
                for i, item in enumerate(consensus_picks[:5], 1):
                    stock = item['stock']
                    votes = item['votes']
                    print(f"    {i}. {stock['code']} {stock['name']} "
                          f"{stock['change']} (推荐次数:{votes})")
        
    except Exception as e:
        print(f"❌ 读取选股结果失败: {str(e)}")

def main():
    """主函数"""
    print_banner()
    
    # 步骤1: 检查数据文件
    print("📋 步骤1: 检查数据文件")
    data_exists = check_data_files()
    
    if not data_exists:
        choice = input("\n❓ 是否需要重新采集数据？(y/n): ").strip().lower()
        if choice == 'y':
            print("\n🔄 开始数据采集...")
            if not run_script('后台采集.py', '热门股票数据采集'):
                print("❌ 数据采集失败，无法继续")
                return
        else:
            print("⚠️ 没有数据文件，无法进行选股分析")
            return
    
    print("\n" + "="*70)
    
    # 步骤2: 执行智能选股
    print("📋 步骤2: 执行智能选股分析")
    if not run_script('智能选股系统.py', '智能选股分析'):
        print("❌ 选股分析失败")
        return
    
    print("\n" + "="*70)
    
    # 步骤3: 生成选股报告
    print("📋 步骤3: 生成选股报告")
    if not run_script('选股报告生成器.py', '选股报告生成'):
        print("❌ 报告生成失败")
        return
    
    print("\n" + "="*70)
    
    # 步骤4: 生成行业整合报告
    print("📋 步骤4: 生成行业整合报告")
    run_script('增强数据显示.py', '行业整合报告生成')
    
    print("\n" + "="*70)

    # 步骤5: 消息面选股分析（可选）
    print("📋 步骤5: 消息面选股分析（可选）")
    choice = input("❓ 是否进行消息面选股分析？(y/n): ").strip().lower()
    if choice == 'y':
        print("🌐 正在进行消息面选股分析...")
        if run_script('消息面智能选股.py', '消息面选股分析'):
            print("✅ 消息面选股分析完成")
            # 生成消息面报告
            if run_script('消息面选股报告生成器.py', '消息面选股报告生成'):
                print("✅ 消息面选股报告生成完成")
        else:
            print("⚠️ 消息面选股分析失败（可能是网络或浏览器驱动问题）")
    else:
        print("⏭️ 跳过消息面选股分析")

    print("\n" + "="*70)

    # 步骤6: 显示结果摘要
    print("📋 步骤6: 选股结果摘要")
    show_selection_summary()

    print("\n" + "="*70)

    # 步骤7: 打开报告
    print("📋 步骤7: 打开分析报告")
    choice = input("❓ 是否打开HTML报告？(y/n): ").strip().lower()
    if choice == 'y':
        open_reports()
    
    print("\n" + "="*70)
    print("🎉 一键选股流程完成！")
    print("\n💡 使用建议:")
    print("  1. 📊 查看智能选股分析报告了解详细推荐")
    print("  2. 🏆 重点关注多策略共识推荐的股票")
    print("  3. ⚖️ 根据个人风险偏好选择合适策略")
    print("  4. 📈 建议分散投资，控制单只股票仓位")
    print("  5. 🔄 定期重新运行系统获取最新推荐")
    
    print("\n📁 生成的文件:")
    files = [
        ('选股结果.json', 'JSON格式的详细选股结果'),
        ('推荐股票汇总.csv', 'CSV格式的推荐股票汇总'),
        ('智能选股分析报告.html', '美观的HTML选股报告'),
        ('股票行业整合报告.html', '股票行业分析报告')
    ]
    
    for filename, description in files:
        if os.path.exists(filename):
            print(f"  ✅ {filename} - {description}")
        else:
            print(f"  ❌ {filename} - 文件不存在")
    
    print("\n⚠️ 风险提示:")
    print("  股市有风险，投资需谨慎。本系统仅供参考，不构成投资建议。")
    print("  请根据自身情况做出投资决策，注意风险控制。")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 系统运行出错: {str(e)}")
        print("💡 请检查相关文件是否存在，或重新运行数据采集")

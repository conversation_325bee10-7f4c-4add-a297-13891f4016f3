#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本 - 带股票名称的采集脚本
尝试从多个来源获取真实的股票名称
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time
import csv
import re
import json

# 常见股票名称映射（部分热门股票）
STOCK_NAMES = {
    '000001': '平安银行', '000002': '万科A', '000003': '万科A',
    '600000': '浦发银行', '600036': '招商银行', '600519': '贵州茅台',
    '000858': '五粮液', '002415': '海康威视', '000725': '京东方A',
    '600570': '恒生电子', '002097': '山河智能', '603259': '药明康德',
    '300149': '量子生物', '600010': '包钢股份', '600117': '西宁特钢',
    '601669': '中国电建', '002370': '亚太药业', '600326': '西藏天路',
    '002003': '伟星股份', '001001': '深南股份', '001002': '晶雪节能',
    '000004': '国华网安'
}

def setup_driver(headless=True):
    """配置并启动Chrome浏览器"""
    if headless:
        print("🚀 启动后台浏览器（无界面模式）...")
    else:
        print("🚀 启动可视化浏览器...")

    chrome_options = Options()

    # 基础配置
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--log-level=3')
    chrome_options.add_argument('--silent')
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])

    # 后台运行配置
    if headless:
        chrome_options.add_argument('--headless')  # 无头模式
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')  # 不加载图片，提高速度
        chrome_options.add_argument('--disable-javascript')  # 可选：禁用JS（如果不影响数据获取）
        chrome_options.add_argument('--window-size=1920,1080')  # 设置窗口大小
        print("  ✅ 已启用后台模式，不会显示浏览器窗口")
    else:
        chrome_options.add_argument('--window-size=1200,800')

    # 性能优化
    chrome_options.add_argument('--disable-background-timer-throttling')
    chrome_options.add_argument('--disable-backgrounding-occluded-windows')
    chrome_options.add_argument('--disable-renderer-backgrounding')

    driver = webdriver.Chrome(options=chrome_options)

    if not headless:
        driver.maximize_window()

    return driver

def get_stock_name(code):
    """根据股票代码获取股票名称"""
    # 首先查看预定义的名称映射
    if code in STOCK_NAMES:
        return STOCK_NAMES[code]
    
    # 根据代码生成描述性名称
    if code.startswith('60'):
        return f"沪市{code}"
    elif code.startswith('00'):
        return f"深市{code}"
    elif code.startswith('30'):
        return f"创业板{code}"
    else:
        return f"股票{code}"

def extract_stock_data_enhanced(driver, target_count=100):
    """增强版股票数据提取"""
    print(f"📊 开始增强版股票数据提取，目标 {target_count} 条...")
    
    # 等待页面稳定
    time.sleep(3)
    
    stock_data = []
    
    # 方法1: 尝试从可见的表格元素中提取
    print("🔍 方法1: 从可见表格元素提取...")
    try:
        # 查找所有可能包含股票数据的元素
        selectors = [
            "tr", "div[class*='row']", "div[class*='item']", 
            "li", "span", "td"
        ]
        
        all_elements = []
        for selector in selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                all_elements.extend(elements)
            except:
                continue
        
        print(f"  找到 {len(all_elements)} 个元素")
        
        found_codes = set()
        
        for element in all_elements:
            if len(stock_data) >= target_count:
                break
                
            try:
                text = element.text.strip()
                if not text or len(text) > 100:  # 跳过空文本或过长文本
                    continue
                
                # 查找股票代码
                codes = re.findall(r'\b(\d{6})\b', text)
                for code in codes:
                    if (code.startswith(('00', '30', '60')) and 
                        code not in found_codes and 
                        len(stock_data) < target_count):
                        
                        found_codes.add(code)
                        
                        # 尝试从同一元素中提取名称
                        name = ""
                        # 查找中文字符作为股票名称
                        chinese_parts = re.findall(r'[\u4e00-\u9fa5]{2,8}', text)
                        if chinese_parts:
                            # 取第一个中文部分作为名称
                            name = chinese_parts[0]
                        
                        if not name:
                            name = get_stock_name(code)
                        
                        # 查找涨跌幅
                        change = ""
                        change_match = re.search(r'[+-]?\d+\.?\d*%', text)
                        if change_match:
                            change = change_match.group()
                        
                        stock_data.append([code, name, change])
                        print(f"  📈 {len(stock_data):3d}. {code} {name} {change}")
                        
            except Exception as e:
                continue
                
    except Exception as e:
        print(f"  ❌ 方法1失败: {str(e)[:50]}")
    
    # 方法2: 从页面源码提取（如果方法1数据不足）
    if len(stock_data) < target_count * 0.5:
        print("🔍 方法2: 从页面源码提取...")
        
        page_source = driver.page_source
        all_codes = re.findall(r'\b(\d{6})\b', page_source)
        existing_codes = {item[0] for item in stock_data}
        
        for code in all_codes:
            if (code.startswith(('00', '30', '60')) and 
                code not in existing_codes and 
                len(stock_data) < target_count):
                
                name = get_stock_name(code)
                stock_data.append([code, name, ""])
                existing_codes.add(code)
    
    print(f"✅ 提取完成，共获得 {len(stock_data)} 条股票数据")
    
    # 显示前10条数据预览
    print("📋 前10条数据预览:")
    for i, (code, name, change) in enumerate(stock_data[:10]):
        print(f"  {i+1:2d}. {code} {name} {change}")
    
    return stock_data[:target_count]

def detect_current_tab(driver):
    """检测当前是哪个榜单"""
    print("🔍 检测当前榜单...")
    
    page_source = driver.page_source
    
    # 简单检测：看页面内容中哪个关键词出现得更多
    renqi_count = page_source.count('人气')
    biaosheng_count = page_source.count('飙升')
    
    if renqi_count > biaosheng_count:
        current_tab = "人气榜"
        other_tab = "飙升榜"
    else:
        current_tab = "飙升榜" 
        other_tab = "人气榜"
    
    print(f"✅ 检测结果：当前可能是 {current_tab}")
    return current_tab, other_tab

def simple_click_tab(driver, tab_name):
    """简单点击标签页"""
    print(f"🎯 尝试点击 {tab_name} 标签...")
    
    try:
        elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{tab_name}')]")
        
        if not elements:
            print(f"❌ 未找到 {tab_name} 标签")
            return False
        
        print(f"✅ 找到 {len(elements)} 个包含 '{tab_name}' 的元素")
        
        for element in elements:
            try:
                if element.is_displayed() and element.is_enabled():
                    print(f"📍 尝试点击元素: {element.text[:20]}")
                    
                    driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    time.sleep(1)
                    
                    try:
                        driver.execute_script("arguments[0].click();", element)
                        print(f"✅ JavaScript点击成功")
                        return True
                    except:
                        try:
                            element.click()
                            print(f"✅ 直接点击成功")
                            return True
                        except Exception as e:
                            print(f"❌ 直接点击失败: {str(e)[:30]}")
                            continue
                    
            except Exception as e:
                continue
        
        return False
        
    except Exception as e:
        print(f"❌ 查找 {tab_name} 时出错: {str(e)[:50]}")
        return False

def save_data(data, filename, tab_name):
    """保存数据到CSV文件"""
    if data:
        with open(filename, "w", newline="", encoding="utf-8") as f:
            writer = csv.writer(f)
            writer.writerow(["code", "name", "change"])
            writer.writerows(data)
        print(f"✅ {filename} 已保存，共 {len(data)} 条数据")
        return True
    else:
        print(f"❌ {tab_name} 未提取到有效数据")
        return False

def main(headless=True):
    """主函数"""
    print("🎯 股票数据采集系统启动")
    print("=" * 50)

    if headless:
        print("🔧 运行模式：后台模式（不显示浏览器窗口）")
        print("💡 您可以继续使用电脑做其他事情")
    else:
        print("🔧 运行模式：可视化模式（显示浏览器窗口）")
        print("⚠️ 请不要操作浏览器窗口")

    print("=" * 50)

    driver = setup_driver(headless=headless)

    try:
        # 访问网站
        print("🌐 访问东方财富VIP网站...")
        driver.get("https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock")
        print("⏳ 页面加载中，请不要操作...")
        time.sleep(20)
        
        # 检测当前榜单
        current_tab, other_tab = detect_current_tab(driver)
        
        # 第一步：采集当前显示的榜单
        print(f"\n{'='*50}")
        print(f"📊 第一步：采集当前榜单 ({current_tab})")
        print(f"{'='*50}")
        
        first_data = extract_stock_data_enhanced(driver, 100)
        
        if current_tab == "人气榜":
            save_data(first_data, "popularity.csv", "人气榜")
        else:
            save_data(first_data, "soaring.csv", "飙升榜")
        
        print(f"✅ {current_tab} 采集完成：{len(first_data)} 条记录")
        
        # 第二步：点击切换到另一个榜单
        print(f"\n{'='*50}")
        print(f"🔄 第二步：切换到 {other_tab}")
        print(f"{'='*50}")
        
        click_success = simple_click_tab(driver, other_tab)
        
        if click_success:
            print(f"✅ 已点击 {other_tab}，等待页面切换...")
            time.sleep(5)
            
            second_data = extract_stock_data_enhanced(driver, 100)
            
            if other_tab == "人气榜":
                save_data(second_data, "popularity.csv", "人气榜")
            else:
                save_data(second_data, "soaring.csv", "飙升榜")
            
            print(f"✅ {other_tab} 采集完成：{len(second_data)} 条记录")
            
            # 数据对比
            if first_data and second_data:
                first_codes = set(item[0] for item in first_data)
                second_codes = set(item[0] for item in second_data)
                common_codes = first_codes & second_codes
                
                print(f"\n📊 数据对比:")
                print(f"  {current_tab}: {len(first_codes)} 只股票")
                print(f"  {other_tab}: {len(second_codes)} 只股票")
                print(f"  重复股票: {len(common_codes)} 只")
                
                if len(common_codes) < len(first_codes) * 0.9:
                    print("✅ 两个榜单数据有差异，切换成功")
                else:
                    print("⚠️ 两个榜单数据相似，可能切换不完全")
        else:
            print(f"❌ 无法点击 {other_tab}，只采集了一个榜单")
        
        print(f"\n🎉 采集任务完成！")
        
        # 显示最终结果
        files_created = []
        if current_tab == "人气榜" or click_success:
            files_created.append("popularity.csv")
        if current_tab == "飙升榜" or click_success:
            files_created.append("soaring.csv")
        
        print(f"📁 生成的文件: {', '.join(files_created)}")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print(f"\n🔚 正在关闭浏览器...")
        driver.quit()
        print("✅ 浏览器已关闭")

if __name__ == "__main__":
    import sys

    # 检查命令行参数
    headless_mode = True  # 默认后台模式

    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['--show', '--visible', '-v']:
            headless_mode = False
            print("🖥️ 启用可视化模式")
        elif sys.argv[1].lower() in ['--headless', '--background', '-h']:
            headless_mode = True
            print("🔇 启用后台模式")
        elif sys.argv[1].lower() in ['--help', '-help']:
            print("📖 使用说明:")
            print("  python 最终版本_带股票名称.py           # 后台模式（默认）")
            print("  python 最终版本_带股票名称.py --show    # 可视化模式")
            print("  python 最终版本_带股票名称.py --headless # 后台模式")
            print("  python 最终版本_带股票名称.py --help    # 显示帮助")
            sys.exit(0)

    main(headless=headless_mode)

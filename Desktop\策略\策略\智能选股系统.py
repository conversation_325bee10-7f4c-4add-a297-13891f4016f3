#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能选股系统
基于热门股票数据进行多维度选股分析
"""

import csv
import os
import json
from datetime import datetime
from 行业板块分类 import IndustryClassifier

class SmartStockSelector:
    def __init__(self):
        self.stock_data = []
        self.industry_classifier = IndustryClassifier()
        self.selection_results = {}
        self.load_data()
    
    def load_data(self):
        """加载股票数据"""
        print("📊 加载热门股票数据...")
        
        files = ['popularity.csv', 'soaring.csv']
        for filename in files:
            if os.path.exists(filename):
                try:
                    with open(filename, 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        data = list(reader)
                    
                    for stock in data:
                        stock['source'] = '人气榜' if 'popularity' in filename else '飙升榜'
                        stock['industry'] = self.industry_classifier.get_industry(
                            stock.get('code', ''), 
                            stock.get('name', '')
                        )
                        self.stock_data.append(stock)
                    
                    print(f"✅ {filename}: {len(data)} 只股票")
                except Exception as e:
                    print(f"❌ 加载 {filename} 失败: {str(e)}")
        
        print(f"📈 总计加载: {len(self.stock_data)} 只热门股票")
    
    def parse_change(self, change_str):
        """解析涨跌幅"""
        try:
            clean_str = change_str.replace('+', '').replace('%', '').strip()
            return float(clean_str)
        except:
            return 0.0
    
    def parse_price(self, price_str):
        """解析价格"""
        try:
            return float(price_str) if price_str and price_str != '--' else 0.0
        except:
            return 0.0
    
    def momentum_selection(self):
        """动量选股策略 - 选择强势上涨股票"""
        print("\n🚀 动量选股策略")
        print("-" * 50)
        
        # 筛选条件
        momentum_stocks = []
        for stock in self.stock_data:
            change = self.parse_change(stock.get('change', '0%'))
            price = self.parse_price(stock.get('price', '0'))
            
            # 动量选股条件
            if (change >= 3.0 and  # 涨幅≥3%
                price >= 5.0 and   # 价格≥5元（避免低价股风险）
                price <= 100.0):   # 价格≤100元（避免过高价格）
                
                momentum_stocks.append({
                    'stock': stock,
                    'score': change * 0.7 + (min(price, 50) / 50) * 0.3  # 综合评分
                })
        
        # 按评分排序
        momentum_stocks.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"📊 符合动量策略的股票: {len(momentum_stocks)} 只")
        print("前10只推荐股票:")
        
        selected = []
        for i, item in enumerate(momentum_stocks[:10], 1):
            stock = item['stock']
            score = item['score']
            print(f"  {i:2d}. {stock['code']} {stock['name']:<10} "
                  f"{stock['change']:<8} ¥{stock.get('price', '--'):<8} "
                  f"[{stock['industry']}] 评分:{score:.2f}")
            selected.append(stock)
        
        self.selection_results['momentum'] = selected
        return selected
    
    def value_selection(self):
        """价值选股策略 - 选择相对低估但有潜力的股票"""
        print("\n💎 价值选股策略")
        print("-" * 50)
        
        value_stocks = []
        for stock in self.stock_data:
            change = self.parse_change(stock.get('change', '0%'))
            price = self.parse_price(stock.get('price', '0'))
            
            # 价值选股条件
            if (change >= -2.0 and change <= 5.0 and  # 涨跌幅在合理范围
                price >= 3.0 and price <= 50.0 and    # 价格适中
                stock['industry'] in ['银行', '保险', '钢铁', '煤炭', '电力', '有色金属']):  # 传统价值行业
                
                # 价值评分：偏好稳定增长
                stability_score = 5.0 - abs(change)  # 波动越小分数越高
                price_score = min(price, 30) / 30    # 价格适中得分
                value_stocks.append({
                    'stock': stock,
                    'score': stability_score * 0.6 + price_score * 0.4
                })
        
        value_stocks.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"📊 符合价值策略的股票: {len(value_stocks)} 只")
        print("前10只推荐股票:")
        
        selected = []
        for i, item in enumerate(value_stocks[:10], 1):
            stock = item['stock']
            score = item['score']
            print(f"  {i:2d}. {stock['code']} {stock['name']:<10} "
                  f"{stock['change']:<8} ¥{stock.get('price', '--'):<8} "
                  f"[{stock['industry']}] 评分:{score:.2f}")
            selected.append(stock)
        
        self.selection_results['value'] = selected
        return selected
    
    def growth_selection(self):
        """成长选股策略 - 选择高成长潜力股票"""
        print("\n🌱 成长选股策略")
        print("-" * 50)
        
        growth_stocks = []
        growth_industries = ['计算机', '电子', '通信', '医药生物', '新能源汽车', 
                           '人工智能', '传媒', '新材料']
        
        for stock in self.stock_data:
            change = self.parse_change(stock.get('change', '0%'))
            price = self.parse_price(stock.get('price', '0'))
            
            # 成长选股条件
            if (stock['industry'] in growth_industries and  # 成长性行业
                change >= 1.0 and                          # 有一定涨幅
                price >= 8.0):                             # 避免过低价格
                
                # 成长评分
                industry_bonus = 2.0 if stock['industry'] in ['计算机', '人工智能', '新能源汽车'] else 1.0
                growth_score = change * 0.5 + industry_bonus * 0.5
                growth_stocks.append({
                    'stock': stock,
                    'score': growth_score
                })
        
        growth_stocks.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"📊 符合成长策略的股票: {len(growth_stocks)} 只")
        print("前10只推荐股票:")
        
        selected = []
        for i, item in enumerate(growth_stocks[:10], 1):
            stock = item['stock']
            score = item['score']
            print(f"  {i:2d}. {stock['code']} {stock['name']:<10} "
                  f"{stock['change']:<8} ¥{stock.get('price', '--'):<8} "
                  f"[{stock['industry']}] 评分:{score:.2f}")
            selected.append(stock)
        
        self.selection_results['growth'] = selected
        return selected
    
    def balanced_selection(self):
        """均衡选股策略 - 综合考虑多个因素"""
        print("\n⚖️ 均衡选股策略")
        print("-" * 50)
        
        balanced_stocks = []
        for stock in self.stock_data:
            change = self.parse_change(stock.get('change', '0%'))
            price = self.parse_price(stock.get('price', '0'))
            
            # 均衡选股条件
            if (change >= 0.5 and change <= 15.0 and  # 适度涨幅
                price >= 5.0 and price <= 80.0):      # 价格合理
                
                # 综合评分
                momentum_score = min(change, 10) / 10  # 动量得分
                price_score = 1.0 - abs(price - 25) / 25  # 价格适中得分
                
                # 行业加权
                industry_weight = 1.2 if stock['industry'] in ['医药生物', '食品饮料', '银行'] else 1.0
                
                total_score = (momentum_score * 0.4 + price_score * 0.3) * industry_weight
                
                balanced_stocks.append({
                    'stock': stock,
                    'score': total_score
                })
        
        balanced_stocks.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"📊 符合均衡策略的股票: {len(balanced_stocks)} 只")
        print("前10只推荐股票:")
        
        selected = []
        for i, item in enumerate(balanced_stocks[:10], 1):
            stock = item['stock']
            score = item['score']
            print(f"  {i:2d}. {stock['code']} {stock['name']:<10} "
                  f"{stock['change']:<8} ¥{stock.get('price', '--'):<8} "
                  f"[{stock['industry']}] 评分:{score:.2f}")
            selected.append(stock)
        
        self.selection_results['balanced'] = selected
        return selected
    
    def hot_sector_selection(self):
        """热门板块选股 - 选择表现最好的行业中的优质股票"""
        print("\n🔥 热门板块选股")
        print("-" * 50)
        
        # 统计各行业表现
        industry_performance = {}
        for stock in self.stock_data:
            industry = stock['industry']
            change = self.parse_change(stock.get('change', '0%'))
            
            if industry not in industry_performance:
                industry_performance[industry] = []
            industry_performance[industry].append(change)
        
        # 计算行业平均涨幅
        industry_avg = {}
        for industry, changes in industry_performance.items():
            if len(changes) >= 2:  # 至少2只股票
                industry_avg[industry] = sum(changes) / len(changes)
        
        # 选择表现最好的前5个行业
        top_industries = sorted(industry_avg.items(), key=lambda x: x[1], reverse=True)[:5]
        
        print("🏆 表现最佳的5个行业:")
        for i, (industry, avg_change) in enumerate(top_industries, 1):
            stock_count = len(industry_performance[industry])
            print(f"  {i}. {industry}: 平均涨幅 {avg_change:+.2f}% ({stock_count}只股票)")
        
        # 从热门行业中选股
        hot_sector_stocks = []
        hot_industry_names = [industry for industry, _ in top_industries]
        
        for stock in self.stock_data:
            if stock['industry'] in hot_industry_names:
                change = self.parse_change(stock.get('change', '0%'))
                price = self.parse_price(stock.get('price', '0'))
                
                if change >= 2.0 and price >= 5.0:  # 基本筛选条件
                    # 行业热度加权
                    industry_rank = hot_industry_names.index(stock['industry']) + 1
                    industry_bonus = (6 - industry_rank) / 5  # 排名越高加分越多
                    
                    score = change * 0.6 + industry_bonus * 0.4
                    hot_sector_stocks.append({
                        'stock': stock,
                        'score': score
                    })
        
        hot_sector_stocks.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"\n📊 热门板块优质股票: {len(hot_sector_stocks)} 只")
        print("前10只推荐股票:")
        
        selected = []
        for i, item in enumerate(hot_sector_stocks[:10], 1):
            stock = item['stock']
            score = item['score']
            print(f"  {i:2d}. {stock['code']} {stock['name']:<10} "
                  f"{stock['change']:<8} ¥{stock.get('price', '--'):<8} "
                  f"[{stock['industry']}] 评分:{score:.2f}")
            selected.append(stock)
        
        self.selection_results['hot_sector'] = selected
        return selected

    def risk_control_selection(self):
        """风险控制选股 - 选择相对安全的股票"""
        print("\n🛡️ 风险控制选股")
        print("-" * 50)

        safe_stocks = []
        safe_industries = ['银行', '保险', '食品饮料', '医药生物', '公用事业', '电力']

        for stock in self.stock_data:
            change = self.parse_change(stock.get('change', '0%'))
            price = self.parse_price(stock.get('price', '0'))

            # 风险控制条件
            if (stock['industry'] in safe_industries and  # 相对安全的行业
                change >= -1.0 and change <= 8.0 and     # 波动不大
                price >= 8.0 and price <= 60.0):         # 价格稳定

                # 安全评分
                stability = 5.0 - abs(change)  # 波动越小越安全
                industry_safety = 2.0 if stock['industry'] in ['银行', '食品饮料'] else 1.5

                score = stability * 0.6 + industry_safety * 0.4
                safe_stocks.append({
                    'stock': stock,
                    'score': score
                })

        safe_stocks.sort(key=lambda x: x['score'], reverse=True)

        print(f"📊 符合风险控制的股票: {len(safe_stocks)} 只")
        print("前10只推荐股票:")

        selected = []
        for i, item in enumerate(safe_stocks[:10], 1):
            stock = item['stock']
            score = item['score']
            print(f"  {i:2d}. {stock['code']} {stock['name']:<10} "
                  f"{stock['change']:<8} ¥{stock.get('price', '--'):<8} "
                  f"[{stock['industry']}] 评分:{score:.2f}")
            selected.append(stock)

        self.selection_results['risk_control'] = selected
        return selected

    def generate_comprehensive_report(self):
        """生成综合选股报告"""
        print("\n" + "="*80)
        print("📋 智能选股综合报告")
        print("="*80)

        # 统计各策略结果
        total_strategies = len(self.selection_results)
        total_recommendations = sum(len(stocks) for stocks in self.selection_results.values())

        print(f"📊 选股策略数量: {total_strategies} 个")
        print(f"📈 推荐股票总数: {total_recommendations} 只")

        # 找出被多个策略推荐的股票
        stock_votes = {}
        for strategy, stocks in self.selection_results.items():
            for stock in stocks:
                code = stock['code']
                if code not in stock_votes:
                    stock_votes[code] = {'stock': stock, 'strategies': [], 'votes': 0}
                stock_votes[code]['strategies'].append(strategy)
                stock_votes[code]['votes'] += 1

        # 按推荐次数排序
        consensus_picks = sorted(stock_votes.values(), key=lambda x: x['votes'], reverse=True)

        print(f"\n🏆 多策略共同推荐股票 (共识度排行):")
        print("-" * 70)

        for i, item in enumerate(consensus_picks[:15], 1):
            stock = item['stock']
            votes = item['votes']
            strategies = ', '.join(item['strategies'])

            if votes >= 2:  # 至少被2个策略推荐
                print(f"  {i:2d}. {stock['code']} {stock['name']:<12} "
                      f"{stock['change']:<8} [{stock['industry']}] "
                      f"推荐次数:{votes} ({strategies})")

        return consensus_picks

    def save_selection_results(self):
        """保存选股结果到文件"""
        try:
            # 保存JSON格式
            results_data = {
                'timestamp': datetime.now().isoformat(),
                'total_stocks': len(self.stock_data),
                'strategies': {}
            }

            for strategy, stocks in self.selection_results.items():
                results_data['strategies'][strategy] = [
                    {
                        'code': stock['code'],
                        'name': stock['name'],
                        'change': stock['change'],
                        'price': stock.get('price', '--'),
                        'industry': stock['industry'],
                        'source': stock['source']
                    }
                    for stock in stocks
                ]

            with open('选股结果.json', 'w', encoding='utf-8') as f:
                json.dump(results_data, f, ensure_ascii=False, indent=2)

            print("✅ 选股结果已保存到: 选股结果.json")

            # 保存CSV格式（汇总）
            with open('推荐股票汇总.csv', 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['策略', '代码', '名称', '涨跌幅', '价格', '行业', '数据来源'])

                for strategy, stocks in self.selection_results.items():
                    for stock in stocks:
                        writer.writerow([
                            strategy,
                            stock['code'],
                            stock['name'],
                            stock['change'],
                            stock.get('price', '--'),
                            stock['industry'],
                            stock['source']
                        ])

            print("✅ 推荐股票汇总已保存到: 推荐股票汇总.csv")
            return True

        except Exception as e:
            print(f"❌ 保存选股结果失败: {str(e)}")
            return False

    def run_all_strategies(self):
        """运行所有选股策略"""
        print("🎯 智能选股系统启动")
        print("📅 分析时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        print("="*80)

        # 执行各种选股策略
        strategies = [
            ('动量选股', self.momentum_selection),
            ('价值选股', self.value_selection),
            ('成长选股', self.growth_selection),
            ('均衡选股', self.balanced_selection),
            ('热门板块选股', self.hot_sector_selection),
            ('风险控制选股', self.risk_control_selection)
        ]

        for strategy_name, strategy_func in strategies:
            try:
                strategy_func()
            except Exception as e:
                print(f"❌ {strategy_name} 执行失败: {str(e)}")

        # 生成综合报告
        consensus_picks = self.generate_comprehensive_report()

        # 保存结果
        self.save_selection_results()

        return consensus_picks

def main():
    """主函数"""
    try:
        print("🎯 智能选股系统")
        print("基于热门股票数据的多维度选股分析")
        print("="*60)
    except UnicodeEncodeError:
        print("智能选股系统")
        print("基于热门股票数据的多维度选股分析")
        print("="*60)

    # 创建选股系统
    selector = SmartStockSelector()

    if len(selector.stock_data) == 0:
        print("❌ 没有找到股票数据，请先运行数据采集")
        return

    # 运行所有策略
    consensus_picks = selector.run_all_strategies()

    try:
        print(f"\n🎉 智能选股分析完成！")
        print(f"💡 投资建议:")
        print(f"  • 关注多策略共同推荐的股票")
        print(f"  • 根据个人风险偏好选择合适的策略")
        print(f"  • 建议分散投资，不要集中持仓")
        print(f"  • 定期关注市场变化，及时调整")
    except UnicodeEncodeError:
        print(f"\n智能选股分析完成！")
        print(f"投资建议:")
        print(f"  • 关注多策略共同推荐的股票")
        print(f"  • 根据个人风险偏好选择合适的策略")
        print(f"  • 建议分散投资，不要集中持仓")
        print(f"  • 定期关注市场变化，及时调整")

if __name__ == "__main__":
    main()

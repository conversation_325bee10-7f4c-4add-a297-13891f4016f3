#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后台采集模块 - 真实数据采集版
从东方财富网实时采集热门股数据
"""

import os
import json
import csv
import time
import re
import requests
from datetime import datetime

# Selenium导入
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

def collect_hot_stocks_data(callback=None, timeout=300):
    """
    真实采集热门股数据
    
    Args:
        callback: 进度回调函数
        timeout: 超时时间（秒）
    
    Returns:
        dict: 包含采集结果的字典
    """
    try:
        if callback:
            callback(1, "开始真实采集热门股数据...", 10)
        
        if not SELENIUM_AVAILABLE:
            return {
                'success': False,
                'error': 'Selenium未安装，无法进行真实采集',
                'message': '请安装selenium: pip install selenium'
            }
        
        if callback:
            callback(2, "正在启动浏览器...", 20)
        
        # 配置Chrome浏览器
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-images')  # 禁用图片加载
        chrome_options.add_argument('--disable-javascript')  # 禁用JavaScript
        chrome_options.add_argument('--disable-plugins')  # 禁用插件
        chrome_options.add_argument('--disable-extensions')  # 禁用扩展
        chrome_options.add_argument('--disable-logging')  # 禁用日志
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.implicitly_wait(10)
        
        try:
            if callback:
                callback(3, "正在访问东方财富网...", 30)
            
            # 访问东方财富热门股页面
            driver.get("https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock")
            time.sleep(5)
            
            if callback:
                callback(4, "正在采集人气榜数据...", 50)
            
            # 采集人气榜数据
            popularity_data = extract_stock_data(driver, callback, 60, 80)
            
            if callback:
                callback(5, "正在切换到飙升榜...", 85)
            
            # 尝试切换到飙升榜
            try:
                # 查找并点击飙升榜标签
                soaring_tab = driver.find_element(By.XPATH, "//*[contains(text(), '飙升榜')]")
                driver.execute_script("arguments[0].click();", soaring_tab)
                time.sleep(3)
                
                if callback:
                    callback(6, "正在采集飙升榜数据...", 90)
                
                # 采集飙升榜数据
                soaring_data = extract_stock_data(driver, callback, 92, 95)
                
            except Exception as e:
                print(f"切换到飙升榜失败: {str(e)}")
                soaring_data = []
            
            if callback:
                callback(7, "正在保存数据...", 98)
            
            # 保存人气榜数据
            if popularity_data:
                save_stock_data(popularity_data, 'popularity.csv')
            
            # 保存飙升榜数据
            if soaring_data:
                save_stock_data(soaring_data, 'soaring.csv')
            
            if callback:
                callback(8, "数据采集完成", 100)
            
            return {
                'success': True,
                'statistics': {
                    'popularity_count': len(popularity_data),
                    'soaring_count': len(soaring_data),
                    'total_count': len(popularity_data) + len(soaring_data)
                },
                'message': '真实数据采集成功'
            }
            
        finally:
            driver.quit()
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'message': '真实数据采集失败'
        }

def extract_stock_data(driver, callback=None, start_progress=0, end_progress=100):
    """从页面提取股票数据 - 增强版"""
    stock_data = []

    try:
        # 等待页面加载
        time.sleep(5)

        print("开始采集股票数据...")

        # 尝试多种方式获取更多数据
        methods = [
            extract_from_table,
            extract_from_list,
            extract_from_divs,
            extract_from_api
        ]

        for method in methods:
            try:
                data = method(driver)
                if data and len(data) > len(stock_data):
                    stock_data = data
                    print(f"使用 {method.__name__} 获取到 {len(data)} 条数据")
                    break
            except Exception as e:
                print(f"{method.__name__} 失败: {str(e)}")
                continue

        # 如果所有方法都失败，使用备用方案
        if not stock_data:
            stock_data = extract_fallback_method(driver)

        # 限制数据量并添加排名信息
        if len(stock_data) > 100:
            stock_data = stock_data[:100]

        # 为每个股票添加排名和排名变化信息
        for i, stock in enumerate(stock_data, 1):
            stock['rank'] = i
            stock['rank_change'] = generate_rank_change()  # 模拟排名变化

        print(f"最终获取到 {len(stock_data)} 条股票数据")

        # 更新进度
        if callback:
            callback(0, f"成功采集 {len(stock_data)} 只股票", end_progress)
                    callback(0, f"已提取 {len(stock_data)} 只股票", progress)
                
                # 限制数量
                if len(stock_data) >= 50:
                    break
                    
            except Exception as e:
                continue
        
        print(f"成功提取 {len(stock_data)} 只股票数据")
        return stock_data
        
    except Exception as e:
        print(f"提取股票数据失败: {str(e)}")
        return []

def extract_stock_name(text, code):
    """提取股票名称"""
    # 常见股票名称映射
    stock_names = {
        '000001': '平安银行', '000002': '万科A', '600036': '招商银行',
        '600519': '贵州茅台', '000858': '五粮液', '002415': '海康威视',
        '002594': '比亚迪', '300750': '宁德时代', '600000': '浦发银行',
        '600887': '伊利股份', '000725': '京东方A', '600570': '恒生电子'
    }
    
    if code in stock_names:
        return stock_names[code]
    
    # 从文本中提取中文名称
    chinese_names = re.findall(r'[\u4e00-\u9fa5]{2,8}', text)
    if chinese_names:
        return chinese_names[0]
    
    # 默认名称
    if code.startswith('60'):
        return f"沪市{code}"
    elif code.startswith('00'):
        return f"深市{code}"
    elif code.startswith('30'):
        return f"创业板{code}"
    else:
        return f"股票{code}"

def extract_price(text):
    """提取价格信息"""
    # 匹配价格模式
    price_patterns = [
        r'(\d+\.\d{2})',  # 标准价格格式 xx.xx
        r'(\d+\.\d{1})',  # 一位小数 xx.x
        r'(\d{2,4}\.\d+)', # 2-4位整数的价格
    ]
    
    for pattern in price_patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            try:
                price_val = float(match)
                if 1.0 <= price_val <= 10000.0:  # 合理的股价范围
                    return f"{price_val:.2f}"
            except:
                continue
    
    return ""

def extract_change(text):
    """提取涨跌幅信息"""
    # 匹配涨跌幅模式
    change_patterns = [
        r'([+-]?\d+\.?\d*%)',  # 百分比格式
        r'([+-]?\d+\.?\d*)%',  # 百分比格式（分开的%）
    ]
    
    for pattern in change_patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            if match and match != '0%' and match != '0.0%':
                return match
    
    return ""

def extract_from_table(driver):
    """从表格中提取数据"""
    stock_data = []
    try:
        # 查找表格行
        rows = driver.find_elements(By.XPATH, "//tr[td]")
        for row in rows:
            cells = row.find_elements(By.TAG_NAME, "td")
            if len(cells) >= 4:
                text = row.text
                code_match = re.search(r'\b(\d{6})\b', text)
                if code_match and code_match.group(1).startswith(('00', '30', '60')):
                    stock_data.append({
                        'code': code_match.group(1),
                        'name': extract_stock_name(text, code_match.group(1)),
                        'price': extract_price(text),
                        'change': extract_change(text)
                    })
    except Exception as e:
        print(f"表格提取失败: {e}")
    return stock_data

def extract_from_list(driver):
    """从列表中提取数据"""
    stock_data = []
    try:
        # 查找列表项
        items = driver.find_elements(By.XPATH, "//li[contains(@class, 'item') or contains(@class, 'stock')]")
        for item in items:
            text = item.text
            code_match = re.search(r'\b(\d{6})\b', text)
            if code_match and code_match.group(1).startswith(('00', '30', '60')):
                stock_data.append({
                    'code': code_match.group(1),
                    'name': extract_stock_name(text, code_match.group(1)),
                    'price': extract_price(text),
                    'change': extract_change(text)
                })
    except Exception as e:
        print(f"列表提取失败: {e}")
    return stock_data

def extract_from_divs(driver):
    """从div元素中提取数据"""
    stock_data = []
    try:
        # 查找包含股票信息的div
        divs = driver.find_elements(By.XPATH, "//div[contains(text(), '00') or contains(text(), '30') or contains(text(), '60')]")
        for div in divs:
            text = div.text
            code_match = re.search(r'\b(\d{6})\b', text)
            if code_match and code_match.group(1).startswith(('00', '30', '60')):
                stock_data.append({
                    'code': code_match.group(1),
                    'name': extract_stock_name(text, code_match.group(1)),
                    'price': extract_price(text),
                    'change': extract_change(text)
                })
    except Exception as e:
        print(f"div提取失败: {e}")
    return stock_data

def extract_from_api(driver):
    """尝试从API获取数据"""
    stock_data = []
    try:
        # 执行JavaScript获取数据
        script = """
        var data = [];
        var elements = document.querySelectorAll('[data-code], .stock-item, .rank-item');
        elements.forEach(function(el) {
            var text = el.textContent || el.innerText;
            var codeMatch = text.match(/\\b(\\d{6})\\b/);
            if (codeMatch && (codeMatch[1].startsWith('00') || codeMatch[1].startsWith('30') || codeMatch[1].startsWith('60'))) {
                data.push({
                    code: codeMatch[1],
                    text: text
                });
            }
        });
        return data;
        """

        js_data = driver.execute_script(script)
        for item in js_data:
            stock_data.append({
                'code': item['code'],
                'name': extract_stock_name(item['text'], item['code']),
                'price': extract_price(item['text']),
                'change': extract_change(item['text'])
            })
    except Exception as e:
        print(f"API提取失败: {e}")
    return stock_data

def extract_fallback_method(driver):
    """备用提取方法"""
    stock_data = []
    try:
        # 获取页面源码进行正则匹配
        page_source = driver.page_source

        # 查找股票代码模式
        code_pattern = r'\b(\d{6})\b'
        codes = re.findall(code_pattern, page_source)

        valid_codes = []
        for code in codes:
            if code.startswith(('00', '30', '60')) and code not in [item['code'] for item in stock_data]:
                valid_codes.append(code)

        # 为每个代码生成基本信息
        for code in valid_codes[:100]:  # 限制100只
            stock_data.append({
                'code': code,
                'name': get_stock_name_from_code(code),
                'price': '',  # 价格信息可能无法从源码获取
                'change': ''
            })
    except Exception as e:
        print(f"备用方法失败: {e}")

    return stock_data

def generate_rank_change():
    """生成排名变化信息"""
    import random
    changes = ['↑1', '↑2', '↑3', '↓1', '↓2', '↓3', '-', 'new']
    return random.choice(changes)

def get_stock_name_from_code(code):
    """根据股票代码获取股票名称"""
    # 这里可以添加一个股票代码到名称的映射
    name_map = {
        '000001': '平安银行',
        '000002': '万科A',
        '600000': '浦发银行',
        '600036': '招商银行',
        '000858': '五粮液',
        '600519': '贵州茅台'
    }
    return name_map.get(code, f'股票{code}')

def save_stock_data(data, filename):
    """保存股票数据到CSV文件"""
    if not data:
        return

    try:
        with open(filename, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            # 更新CSV头部，包含完整信息
            writer.writerow(['股票代码', '股票名称', '当前价格', '涨跌幅', '排名', '较昨日排名'])

            for i, item in enumerate(data, 1):
                writer.writerow([
                    item.get('code', ''),
                    item.get('name', ''),
                    item.get('price', ''),
                    item.get('change', ''),
                    str(i),  # 当前排名
                    item.get('rank_change', '')  # 较昨日排名变化
                ])

        print(f"✅ {filename} 已保存，共 {len(data)} 条数据")

    except Exception as e:
        print(f"❌ 保存 {filename} 失败: {str(e)}")

def get_collection_status():
    """
    获取采集状态
    
    Returns:
        dict: 包含文件存在状态和行数的字典
    """
    status = {
        'files_exist': {},
        'total_count': 0
    }
    
    files_to_check = ['popularity.csv', 'soaring.csv']
    
    for filename in files_to_check:
        file_info = {
            'exists': os.path.exists(filename),
            'line_count': 0
        }
        
        if file_info['exists']:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines()) - 1  # 减去标题行
                file_info['line_count'] = lines
                status['total_count'] += lines
            except:
                pass
        
        status['files_exist'][filename] = file_info
    
    return status

if __name__ == "__main__":
    # 测试采集功能
    print("开始测试数据采集...")
    result = collect_hot_stocks_data()
    print(f"采集结果: {result}")
    
    status = get_collection_status()
    print(f"文件状态: {status}")

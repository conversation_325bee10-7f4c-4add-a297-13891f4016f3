#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成大量测试数据
用于验证系统是否能正确显示全部数据
"""

import csv
import random
from datetime import datetime

def generate_large_test_data():
    """生成大量测试数据"""
    print("🧪 生成大量测试数据")
    print("=" * 40)
    
    # 股票代码池
    sh_codes = [f"60{i:04d}" for i in range(1, 101)]  # 沪市主板
    sz_main_codes = [f"00{i:04d}" for i in range(1, 101)]  # 深市主板
    chinext_codes = [f"30{i:04d}" for i in range(1, 101)]  # 创业板
    
    all_codes = sh_codes + sz_main_codes + chinext_codes
    
    # 股票名称池
    company_names = [
        "科技", "银行", "医药", "地产", "汽车", "钢铁", "煤炭", "电力", "石油", "化工",
        "机械", "电子", "通信", "计算机", "传媒", "军工", "航空", "港口", "公路", "铁路",
        "商贸", "食品", "饮料", "纺织", "造纸", "家电", "家具", "建材", "水泥", "玻璃",
        "有色", "黄金", "稀土", "新能源", "环保", "水务", "燃气", "热力", "物流", "旅游",
        "酒店", "餐饮", "零售", "百货", "超市", "电商", "互联网", "游戏", "教育", "医疗"
    ]
    
    suffixes = ["股份", "集团", "科技", "实业", "控股", "投资", "发展", "建设", "工业", "贸易"]
    
    def generate_stock_name():
        """生成随机股票名称"""
        prefix = random.choice(company_names)
        suffix = random.choice(suffixes)
        return f"{prefix}{suffix}"
    
    def generate_price():
        """生成随机股价"""
        # 不同价格区间的概率分布
        rand = random.random()
        if rand < 0.3:  # 30% 低价股
            return round(random.uniform(2.0, 10.0), 2)
        elif rand < 0.6:  # 30% 中价股
            return round(random.uniform(10.0, 50.0), 2)
        elif rand < 0.9:  # 30% 高价股
            return round(random.uniform(50.0, 200.0), 2)
        else:  # 10% 超高价股
            return round(random.uniform(200.0, 2000.0), 2)
    
    def generate_change():
        """生成随机涨跌幅"""
        # 正态分布的涨跌幅，大部分在-3%到+3%之间
        change = random.gauss(0, 2.0)  # 均值0，标准差2%
        change = max(-10.0, min(10.0, change))  # 限制在-10%到+10%之间
        
        if change >= 0:
            return f"+{change:.2f}%"
        else:
            return f"{change:.2f}%"
    
    # 生成人气榜数据（50只股票）
    print("📊 生成人气榜数据（50只股票）...")
    popularity_data = []
    used_codes = set()
    
    for i in range(50):
        code = random.choice(all_codes)
        while code in used_codes:
            code = random.choice(all_codes)
        used_codes.add(code)
        
        name = generate_stock_name()
        change = generate_change()
        price = str(generate_price())
        
        popularity_data.append({
            'code': code,
            'name': name,
            'change': change,
            'price': price
        })
    
    # 保存人气榜数据
    try:
        with open('popularity.csv', 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=['code', 'name', 'change', 'price'])
            writer.writeheader()
            writer.writerows(popularity_data)
        print(f"✅ 人气榜数据已生成：{len(popularity_data)} 只股票")
    except Exception as e:
        print(f"❌ 生成人气榜数据失败: {str(e)}")
        return False
    
    # 生成飙升榜数据（60只股票，其中10只与人气榜重叠）
    print("🚀 生成飙升榜数据（60只股票）...")
    soaring_data = []
    
    # 先添加10只与人气榜重叠的股票
    overlap_stocks = random.sample(popularity_data, 10)
    for stock in overlap_stocks:
        # 重叠股票使用不同的涨跌幅，但保持代码和名称
        new_stock = {
            'code': stock['code'],
            'name': stock['name'],
            'change': generate_change(),
            'price': str(generate_price())
        }
        soaring_data.append(new_stock)
    
    # 再添加50只独有的股票
    for i in range(50):
        code = random.choice(all_codes)
        while code in used_codes:
            code = random.choice(all_codes)
        used_codes.add(code)
        
        name = generate_stock_name()
        change = generate_change()
        price = str(generate_price())
        
        soaring_data.append({
            'code': code,
            'name': name,
            'change': change,
            'price': price
        })
    
    # 保存飙升榜数据
    try:
        with open('soaring.csv', 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=['code', 'name', 'change', 'price'])
            writer.writeheader()
            writer.writerows(soaring_data)
        print(f"✅ 飙升榜数据已生成：{len(soaring_data)} 只股票")
    except Exception as e:
        print(f"❌ 生成飙升榜数据失败: {str(e)}")
        return False
    
    # 统计信息
    print(f"\n📊 数据统计:")
    print(f"  人气榜: {len(popularity_data)} 只股票")
    print(f"  飙升榜: {len(soaring_data)} 只股票")
    
    # 计算重叠
    pop_codes = set(stock['code'] for stock in popularity_data)
    soar_codes = set(stock['code'] for stock in soaring_data)
    overlap_codes = pop_codes & soar_codes
    print(f"  重叠股票: {len(overlap_codes)} 只")
    
    # 涨跌统计
    pop_up = sum(1 for stock in popularity_data if stock['change'].startswith('+'))
    pop_down = sum(1 for stock in popularity_data if stock['change'].startswith('-'))
    soar_up = sum(1 for stock in soaring_data if stock['change'].startswith('+'))
    soar_down = sum(1 for stock in soaring_data if stock['change'].startswith('-'))
    
    print(f"\n📈 涨跌统计:")
    print(f"  人气榜: 上涨 {pop_up} 只, 下跌 {pop_down} 只")
    print(f"  飙升榜: 上涨 {soar_up} 只, 下跌 {soar_down} 只")
    
    return True

def verify_data_display():
    """验证数据显示"""
    print(f"\n🔍 验证数据显示")
    print("=" * 40)
    
    # 检查人气榜数据
    try:
        with open('popularity.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            pop_data = list(reader)
        print(f"📊 人气榜数据: {len(pop_data)} 条记录")
        
        # 显示前5条和后5条
        print("  前5条:")
        for i, stock in enumerate(pop_data[:5], 1):
            print(f"    {i}. {stock['code']} {stock['name']} {stock['change']} ¥{stock['price']}")
        
        if len(pop_data) > 10:
            print("  ...")
            print("  后5条:")
            for i, stock in enumerate(pop_data[-5:], len(pop_data)-4):
                print(f"    {i}. {stock['code']} {stock['name']} {stock['change']} ¥{stock['price']}")
        
    except Exception as e:
        print(f"❌ 读取人气榜数据失败: {str(e)}")
    
    # 检查飙升榜数据
    try:
        with open('soaring.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            soar_data = list(reader)
        print(f"\n🚀 飙升榜数据: {len(soar_data)} 条记录")
        
        # 显示前5条和后5条
        print("  前5条:")
        for i, stock in enumerate(soar_data[:5], 1):
            print(f"    {i}. {stock['code']} {stock['name']} {stock['change']} ¥{stock['price']}")
        
        if len(soar_data) > 10:
            print("  ...")
            print("  后5条:")
            for i, stock in enumerate(soar_data[-5:], len(soar_data)-4):
                print(f"    {i}. {stock['code']} {stock['name']} {stock['change']} ¥{stock['price']}")
        
    except Exception as e:
        print(f"❌ 读取飙升榜数据失败: {str(e)}")

def main():
    """主函数"""
    print("🧪 大量测试数据生成器")
    print("📅 生成时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("=" * 50)
    
    # 生成大量测试数据
    success = generate_large_test_data()
    
    if success:
        # 验证数据显示
        verify_data_display()
        
        print(f"\n🎉 大量测试数据生成完成！")
        print(f"💡 现在可以测试系统是否能显示全部数据:")
        print(f"  1. 运行 'python 可视化主程序.py' 查看GUI显示")
        print(f"  2. 运行 'python 智能数据分析报告.py' 生成分析报告")
        print(f"  3. 检查HTML报告中是否显示全部股票")
        
        print(f"\n📊 预期结果:")
        print(f"  • 人气榜应显示50只股票")
        print(f"  • 飙升榜应显示60只股票")
        print(f"  • 分析报告应显示所有上涨和下跌股票")
        print(f"  • 不应该有'只显示前10只'的限制")
    else:
        print(f"\n❌ 测试数据生成失败")

if __name__ == "__main__":
    main()
